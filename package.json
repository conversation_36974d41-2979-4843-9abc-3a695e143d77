{"name": "soybean-admin", "type": "module", "version": "1.3.13", "description": "A fresh and elegant admin template, based on Vue3、Vite6、TypeScript、NaiveUI and UnoCSS. 一个基于Vue3、Vite6、TypeScript、NaiveUI and UnoCSS的清新优雅的中后台模版。", "author": {"name": "Soybean", "email": "<EMAIL>", "url": "https://github.com/soybeanjs"}, "license": "MIT", "homepage": "https://github.com/soybeanjs/soybean-admin", "repository": {"url": "https://github.com/soybeanjs/soybean-admin.git"}, "bugs": {"url": "https://github.com/soybeanjs/soybean-admin/issues"}, "keywords": ["Vue3 admin ", "vue-admin-template", "Vite6", "TypeScript", "naive-ui", "naive-ui-admin", "ant-design-vue v4", "UnoCSS"], "engines": {"node": ">=18.20.0", "pnpm": ">=8.7.0"}, "scripts": {"build": "vite build --mode prod", "build:test": "vite build --mode test", "cleanup": "sa cleanup", "commit": "sa git-commit", "commit:zh": "sa git-commit -l=zh-cn", "dev": "vite --mode test", "dev:prod": "vite --mode prod", "gen-route": "sa gen-route", "lint": "eslint . --fix", "prepare": "simple-git-hooks", "preview": "vite preview", "release": "sa release", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "update-pkg": "sa update-pkg"}, "dependencies": {"@better-scroll/core": "2.5.1", "@iconify/vue": "4.3.0", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "@soybeanjs/eslint-config": "^1.6.1", "@vueuse/core": "13.0.0", "ant-design-x-vue": "^1.2.4", "clipboard": "2.0.11", "dayjs": "1.11.13", "defu": "6.1.4", "echarts": "5.6.0", "json5": "2.2.3", "naive-ui": "2.41.0", "nprogress": "0.2.0", "pinia": "3.0.1", "tailwind-merge": "3.0.2", "vue": "3.5.13", "vue-draggable-plus": "0.6.0", "vue-i18n": "11.1.2", "vue-router": "4.5.0"}, "devDependencies": {"@elegant-router/vue": "0.3.8", "@iconify/json": "2.2.318", "@sa/scripts": "workspace:*", "@sa/uno-preset": "workspace:*", "@types/node": "22.13.10", "@types/nprogress": "0.2.3", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "@unocss/eslint-config": "66.0.0", "@unocss/preset-icons": "66.0.0", "@unocss/preset-uno": "66.0.0", "@unocss/transformer-directives": "66.0.0", "@unocss/transformer-variant-group": "66.0.0", "@unocss/vite": "66.0.0", "@vitejs/plugin-vue": "5.2.3", "@vitejs/plugin-vue-jsx": "4.1.2", "consola": "3.4.2", "element-plus": "^2.9.10", "eslint": "9.28.0", "eslint-plugin-vue": "10.0.0", "kolorist": "1.8.0", "sass": "1.86.0", "simple-git-hooks": "2.11.1", "tsx": "4.19.3", "typescript": "5.8.3", "unocss": "^66.3.3", "unplugin-icons": "22.1.0", "unplugin-vue-components": "28.4.1", "vite": "6.2.2", "vite-plugin-progress": "0.0.7", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.7.2", "vue-eslint-parser": "10.1.1", "vue-tsc": "2.2.8"}, "simple-git-hooks": {"pre-commit": "pnpm typecheck && git diff --exit-code"}, "website": "https://admin.soybeanjs.cn"}
#应用的基础URL，默认为"/"
#如果使用子目录，必须以"/"结尾，例"/admin/"而不是"/admin"
VITE_BASE_URL=/

VITE_APP_TITLE=资产巡检系统

VITE_APP_DESC=基于Vue3的资产巡检管理系统

# 图标名称前缀
VITE_ICON_PREFIX=icon

# 本地svg图标组件前缀，必须包含VITE_ICON_PREFIX
# 格式为{VITE_ICON_PREFIX}-{本地图标名称}
VITE_ICON_LOCAL_PREFIX=icon-local

# 权限路由模式：static(静态)|dynamic(动态)
VITE_AUTH_ROUTE_MODE=dynamic

# 静态权限路由首页
VITE_ROUTE_HOME=home

# 默认菜单图标
VITE_MENU_ICON=mdi:menu

# 开发模式下是否启用HTTP代理
VITE_HTTP_PROXY=Y

# vue-router模式：hash | history | memory
VITE_ROUTER_HISTORY_MODE=history

# 后端服务成功代码，收到此代码表示请求成功
VITE_SERVICE_SUCCESS_CODE=200

# 后端服务登出代码，收到此代码用户将被登出并重定向到登录页
VITE_SERVICE_LOGOUT_CODES=8888,8889

# 后端服务模态框登出代码，收到此代码将通过模态框提示用户登出
VITE_SERVICE_MODAL_LOGOUT_CODES=7777,7778

# 后端服务token过期代码，收到此代码将刷新token并重新发送请求
VITE_SERVICE_EXPIRED_TOKEN_CODES=9999,9998,3333

# 当路由模式为静态时，定义的超级角色
VITE_STATIC_SUPER_ROLE=R_SUPER

# 是否生成sourcemap
VITE_SOURCE_MAP=N

# 用于区分不同域名的存储
VITE_STORAGE_PREFIX=SOY_

# 用于控制程序是否自动检测更新
VITE_AUTOMATICALLY_DETECT_UPDATE=Y

# 在终端显示代理URL日志
VITE_PROXY_LOG=Y

# 用于控制是否启动编辑器
# 注意：此插件仅在开发模式下可用，构建模式下不可用
VITE_DEVTOOLS_LAUNCH_EDITOR=code

# 用于控制是否验证git提交信息格式
VITE_GIT_COMMIT_VERIFY=Y

# 百度地图API Key
VITE_BAIDU_MAP_API_KEY=uMqXFdZuFsLsMkMU94BYgaXFkjzfWrzV

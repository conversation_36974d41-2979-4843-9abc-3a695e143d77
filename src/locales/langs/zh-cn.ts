const local: App.I18n.Schema = {
  system: {
    title: '资产巡检 管理系统',
    updateTitle: '系统版本更新通知',
    updateContent: '检测到系统有新版本发布，是否立即刷新页面？',
    updateConfirm: '立即刷新',
    updateCancel: '稍后再说'
  },
  common: {
    action: '操作',
    add: '新增',
    addSuccess: '添加成功',
    backToHome: '返回首页',
    batchDelete: '批量删除',
    cancel: '取消',
    close: '关闭',
    check: '勾选',
    expandColumn: '展开列',
    columnSetting: '列设置',
    config: '配置',
    confirm: '确认',
    delete: '删除',
    deleteSuccess: '删除成功',
    deleteError: '删除失败',
    confirmDelete: '确认删除吗？',
    view: '查看',
    edit: '编辑',
    warning: '警告',
    error: '错误',
    index: '序号',
    keywordSearch: '请输入关键词搜索',
    logout: '退出登录',
    logoutConfirm: '确认退出登录吗？',
    lookForward: '敬请期待',
    modify: '修改',
    modifySuccess: '修改成功',
    modifyFailed: '修改失败',
    more: '更多',
    moreOperation: '更多操作',
    noData: '无数据',
    operate: '操作',
    pleaseCheckValue: '请检查输入的值是否合法',
    refresh: '刷新',
    reset: '重置',
    search: '搜索',
    switch: '切换',
    tip: '提示',
    trigger: '触发',
    update: '更新',
    updateSuccess: '更新成功',
    upload: '上传',
    uploadSuccess: '上传成功',
    userCenter: '个人中心',
    yesOrNo: {
      yes: '是',
      no: '否'
    },
    enableStatus: {
      enable: '启用',
      disable: '禁用'
    },
    userGender: {
      male: '男',
      female: '女'
    },
    requestError: '请求出错',
    noPermission: '暂无权限访问'
  },
  request: {
    logout: '请求失败后登出用户',
    logoutMsg: '用户状态失效，请重新登录',
    logoutWithModal: '请求失败后弹出模态框再登出用户',
    logoutWithModalMsg: '用户状态失效，请重新登录',
    refreshToken: '请求的token已过期，刷新token',
    tokenExpired: 'token已过期'
  },
  theme: {
    themeSchema: {
      title: '主题模式',
      light: '亮色模式',
      dark: '暗黑模式',
      auto: '跟随系统'
    },
    grayscale: '灰色模式',
    colourWeakness: '色弱模式',
    layoutMode: {
      title: '布局模式',
      vertical: '左侧菜单模式',
      'vertical-mix': '左侧菜单混合模式',
      horizontal: '顶部菜单模式',
      'horizontal-mix': '顶部菜单混合模式',
      reverseHorizontalMix: '一级菜单与子级菜单位置反转'
    },
    recommendColor: '应用推荐算法的颜色',
    recommendColorDesc: '推荐颜色的算法参照',
    themeColor: {
      title: '主题颜色',
      primary: '主色',
      info: '信息色',
      success: '成功色',
      warning: '警告色',
      error: '错误色',
      followPrimary: '跟随主色'
    },
    scrollMode: {
      title: '滚动模式',
      wrapper: '外层滚动',
      content: '主体滚动'
    },
    page: {
      animate: '页面切换动画',
      mode: {
        title: '页面切换动画类型',
        'fade-slide': '滑动',
        fade: '淡入淡出',
        'fade-bottom': '底部消退',
        'fade-scale': '缩放消退',
        'zoom-fade': '渐变',
        'zoom-out': '闪现',
        none: '无'
      }
    },
    fixedHeaderAndTab: '固定头部和标签栏',
    header: {
      height: '头部高度',
      breadcrumb: {
        visible: '显示面包屑',
        showIcon: '显示面包屑图标'
      },
      multilingual: {
        visible: '显示多语言按钮'
      }
    },
    tab: {
      visible: '显示标签栏',
      cache: '标签栏信息缓存',
      height: '标签栏高度',
      mode: {
        title: '标签栏风格',
        chrome: '谷歌风格',
        button: '按钮风格'
      }
    },
    sider: {
      inverted: '深色侧边栏',
      width: '侧边栏宽度',
      collapsedWidth: '侧边栏折叠宽度',
      mixWidth: '混合布局侧边栏宽度',
      mixCollapsedWidth: '混合布局侧边栏折叠宽度',
      mixChildMenuWidth: '混合布局子菜单宽度'
    },
    footer: {
      visible: '显示底部',
      fixed: '固定底部',
      height: '底部高度',
      right: '底部局右'
    },
    watermark: {
      visible: '显示全屏水印',
      text: '水印文本'
    },
    themeDrawerTitle: '主题配置',
    pageFunTitle: '页面功能',
    resetCacheStrategy: {
      title: '重置缓存策略',
      close: '关闭页面',
      refresh: '刷新页面'
    },
    configOperation: {
      copyConfig: '复制配置',
      copySuccessMsg: '复制成功，请替换 src/theme/settings.ts 中的变量 themeSettings',
      resetConfig: '重置配置',
      resetSuccessMsg: '重置成功'
    }
  },
  route: {
    login: '登录',
    403: '无权限',
    404: '页面不存在',
    500: '服务器错误',
    'iframe-page': '外链页面',
    home: '首页',
    manage: '系统管理',
    manage_user: '用户管理',
    manage_role: '角色管理',
    manage_menu: '菜单管理',
    manage_org: '组织管理',
    manage_gateway: '网关管理',
    aichat: 'AI智能助手',
    'manage_user-detail': '用户详情',
    devicemanagement: '设备管理',
    devicemanagement_device: '设备列表',
    'devicemanagement_vehicle-location': '车辆定位'
  },
  ai: {
    chat: {
      title: 'AI 智能助手',
      connected: '已连接',
      disconnected: '未连接',
      messages: '条消息',
      newChat: '新对话',
      config: '配置',
      clear: '清空',
      configureFirst: '配置 AI 助手',
      configureDescription: '请先配置 DeepSeek API Key 才能开始使用 AI 助手进行对话',
      configureNow: '立即配置',
      startChat: '开始对话',
      inputPlaceholder: '输入消息，支持 Shift+Enter 换行...',
      send: '发送',
      copy: '复制',
      justNow: '刚刚',
      minutesAgo: '分钟前',
      hoursAgo: '小时前',
      startChatting: '开始对话吧',
      newChatCreated: '已创建新对话',
      configSaved: '配置已保存',
      historyCleared: '对话历史已清空',
      chatStarted: '对话已开始',
      copiedToClipboard: '已复制到剪贴板',
      copyFailed: '复制失败',
      connectionSuccess: '连接成功',
      connectionFailed: '连接失败，请检查配置',
      welcomeMessage: '您好！我是AI助手，很高兴为您服务。请问有什么我可以帮助您的吗？',
      minimize: '缩小',
      maximize: '放大',
      close: '关闭'
    },
    welcome: {
      title: '您好，我是 AI 智能助手',
      description: '基于先进的人工智能技术，我可以帮助您解答问题、协助工作、提供建议。让我们开始智能对话吧！',
      quickStart: '快速开始：',
      features: '主要特性：',
      startChat: '开始对话',
      learnFeatures: '了解功能',
      analyzeProblems: '问题分析',
      getAdvice: '获取建议',
      examplePrompts: {
        learnFeatures: '请介绍一下你的功能',
        analyzeProblems: '帮我分析一下这个问题',
        getAdvice: '请给我一些建议'
      },
      featureList: {
        smartQA: '智能问答与对话',
        docAnalysis: '文档分析与总结',
        efficiency: '工作效率提升',
        streaming: '实时流式回复'
      }
    },
    config: {
      title: 'AI 助手配置',
      apiKey: 'API Key',
      apiKeyPlaceholder: '请输入 DeepSeek API Key',
      apiUrl: 'API 地址',
      model: '模型',
      modelPlaceholder: '选择模型',
      maxTokens: '最大Token数',
      temperature: '温度',
      testConnection: '测试连接',
      saveConfig: '保存配置',
      validation: {
        apiKeyRequired: '请输入 API Key',
        apiUrlRequired: '请输入 API 地址',
        modelRequired: '请选择模型',
        maxTokensRange: '最大 Token 数必须在 1-8192 之间',
        temperatureRange: '温度值必须在 0-2 之间'
      }
    }
  },
  page: {
    login: {
      common: {
        loginOrRegister: '登录 / 注册',
        userNamePlaceholder: '请输入用户名',
        phonePlaceholder: '请输入手机号',
        codePlaceholder: '请输入验证码',
        passwordPlaceholder: '请输入密码',
        confirmPasswordPlaceholder: '请再次输入密码',
        codeLogin: '验证码登录',
        confirm: '确定',
        back: '返回',
        validateSuccess: '验证成功',
        loginSuccess: '登录成功',
        welcomeBack: '欢迎回来，{userName} ！'
      },
      pwdLogin: {
        title: '密码登录',
        rememberMe: '记住我',
        forgetPassword: '忘记密码？',
        register: '注册账号',
        otherAccountLogin: '其他账号登录',
        otherLoginMode: '其他登录方式',
        superAdmin: '超级管理员',
        admin: '管理员',
        user: '普通用户'
      },
      codeLogin: {
        title: '验证码登录',
        getCode: '获取验证码',
        reGetCode: '{time}秒后重新获取',
        sendCodeSuccess: '验证码发送成功',
        imageCodePlaceholder: '请输入图片验证码'
      },
      register: {
        title: '注册账号',
        agreement: '我已经仔细阅读并接受',
        protocol: '《用户协议》',
        policy: '《隐私权政策》'
      },
      resetPwd: {
        title: '重置密码'
      },
      bindWeChat: {
        title: '绑定微信'
      }
    },
    home: {
      branchDesc:
        '为了方便大家开发和更新合并，我们对main分支的代码进行了精简，只保留了首页菜单，其余内容已移至example分支进行维护。预览地址显示的内容即为example分支的内容。',
      greeting: '早安，{userName}, 今天又是充满活力的一天!',
      weatherDesc: '今日多云转晴，20℃ - 25℃!',
      projectCount: '项目数',
      todo: '待办',
      message: '消息',
      downloadCount: '下载量',
      registerCount: '注册量',
      schedule: '作息安排',
      study: '学习',
      work: '工作',
      rest: '休息',
      entertainment: '娱乐',
      visitCount: '访问量',
      turnover: '成交额',
      dealCount: '成交量',
      projectNews: {
        title: '项目动态',
        moreNews: '更多动态',
        desc1: 'Soybean 在2021年5月28日创建了开源项目 soybean-admin!',
        desc2: 'Yanbowe 向 soybean-admin 提交了一个bug，多标签栏不会自适应。',
        desc3: 'Soybean 准备为 soybean-admin 的发布做充分的准备工作!',
        desc4: 'Soybean 正在忙于为soybean-admin写项目说明文档！',
        desc5: 'Soybean 刚才把工作台页面随便写了一些，凑合能看了！'
      },
      creativity: '创意'
    },
    manage: {
      user: {
        title: '用户列表',
        selectTreeIsEmptyTip: '请选择左侧组织机构',
        userName: '用户名',
        realName: '真实姓名',
        gender: '性别',
        phone: '手机号',
        email: '邮箱',
        status: '状态',
        password: '密码',
        userRole: '用户角色',
        userOrgUnits: '所属组织',
        roleEnum: {
          admin: '管理员',
          user: '普通用户'
        },
        positionEnum: {
          manager: '经理',
          staff: '员工'
        },
        orgEnum: {
          dept1: '部门一',
          dept2: '部门二'
        },
        resetPwd: '重置密码',
        resetPwdSuccess: '密码重置成功',
        resetPwdFail: '密码重置失败',
        resetPassword: '重置密码',
        newPassword: '新密码',
        unassigned: '未分配',
        noPermissionDesc: '您没有查看用户管理的权限',
        addUser: '新增用户',
        editUser: '编辑用户',
        deleteUser: '删除用户',
        batchDeleteUser: '批量删除用户',
        pleaseSelectUser: '请选择要操作的用户',
        form: {
          userName: '请输入用户名',
          password: '请输入密码',
          realName: '请输入真实姓名',
          phone: '请输入手机号',
          email: '请输入邮箱',
          userRole: '请选择用户角色',
          userOrgUnits: '请选择所属组织'
        },
        validate: {
          passwordRequired: '密码不能为空',
          passwordLength: '密码长度至少为6位',
          phoneInvalid: '请输入有效的手机号',
          emailInvalid: '请输入有效的邮箱'
        }
      },
      orgUnits: {
        title: '组织结构',
        name: '组织名称',
        code: '组织编码',
        abbr: '组织简称',
        level: '组织层级',
        description: '组织描述',
        status: '组织状态',
        sort: '排序',
        unassigned: '未分配',
        orgStructure: '组织结构',
        addOrgUnits: '新增组织',
        editOrgUnits: '编辑组织',
        addChildOrgUnits: '新增子组织',
        noPermissionDesc: '您没有查看组织管理的权限',
        form: {
          name: '请输入组织名称',
          code: '请输入组织编码',
          abbr: '请输入组织简称',
          description: '请输入组织描述',
          status: '请选择组织状态',
          sort: '请输入排序序号'
        }
      },
      role: {
        title: '角色管理',
        roleName: '角色名称',
        roleCode: '角色编码',
        roleDesc: '角色描述',
        roleStatus: '角色状态',
        status: '状态',
        sort: '排序',
        addRole: '新增角色',
        editRole: '编辑角色',
        menuAuth: '菜单权限',
        buttonAuth: '按钮权限',
        selectAll: '全部勾选',
        unselectAll: '全部取消',
        selectGroup: '本组全选',
        unselectGroup: '本组取消',
        selected: '已选择',
        items: '项',
        permissions: '个权限',
        noPermissionDesc: '您没有查看角色管理的权限',
        form: {
          roleName: '请输入角色名称',
          roleCode: '请输入角色编码',
          roleDesc: '请输入角色描述',
          roleStatus: '请选择角色状态',
          status: '请选择状态'
        }
      },
      menu: {
        title: '菜单管理',
        id: '菜单ID',
        menuType: '菜单类型',
        icon: '图标',
        iconTypeTitle: '图标类型',
        routeName: '路由名称',
        routePath: '路由路径',
        menuStatus: '菜单状态',
        status: '状态',
        hideInMenu: '隐藏菜单',
        parentId: '父级ID',
        order: '排序',
        sort: '排序',
        addChildMenu: '添加子菜单',
        addMenu: '添加菜单',
        editMenu: '编辑菜单',
        keepAlive: '缓存路由',
        constant: '常量路由',
        href: '外部链接',
        iframeUrl: '内嵌页面地址',
        activeMenu: '激活菜单',
        multiTab: '支持多页签',
        fixedIndexInTab: '固定页签位置',
        query: '路由参数',
        pathParam: '路径参数',
        i18nKey: '国际化键名',
        button: '按钮',
        layout: '布局组件',
        page: '页面组件',
        home: '首页',
        detail: '菜单详情',
        type: '菜单类型',
        name: '菜单名称',
        selectTreeIsEmptyTip: '请选择左侧菜单树',
        menuTypeIsDirectory: '请选择目录类型的菜单',
        menuTypeEnum: {
          topDirectory: '目录',
          subMenu: '子菜单',
          singleMenu: '顶级菜单',
          specialPage: '特殊页面',
          // 保留旧的键值以兼容
          directory: '目录',
          menu: '菜单'
        },
        iconTypeEnum: {
          iconify: 'Iconify图标',
          local: '本地图标'
        },
        form: {
          name: '请输入菜单名称',
          routeName: '请输入路由名称',
          routePath: '路由路径会根据路由名称自动生成',
          pathParam: '请输入路径参数',
          i18nKey: '国际化键名',
          icon: '请输入Iconify图标名称',
          localIcon: '请选择本地图标',
          order: '请输入排序',
          sort: '请输入排序',
          layout: '请选择布局组件',
          page: '请选择页面组件',
          href: '请输入外部链接',
          iframeUrl: '请输入内嵌页面地址',
          activeMenu: '请选择激活菜单',
          fixedIndexInTab: '请输入固定页签位置',
          queryKey: '参数名',
          queryValue: '参数值',
          buttonCode: '按钮编码',
          buttonDesc: '按钮描述'
        },
        componentTips: {
          topDirectory: '目录：使用 layout.base',
          subMenu: '子菜单：使用 view.xxx（继承父级布局）',
          singleMenu: '顶级菜单：使用 layout.base$view.xxx',
          specialPage: '特殊页面：使用 layout.blank$view.xxx',
          topDirectoryNoPage: '目录不需要页面组件',
          subMenuNeedPage: '子菜单：选择对应的页面组件（view.xxx）',
          singleMenuNeedPage: '顶级菜单：选择对应的页面组件（view.xxx）',
          specialPageNeedPage: '特殊页面：选择对应的页面组件（view.xxx）',
          // 保留旧的键值以兼容
          directory: '目录菜单：使用 layout.base',
          topLevelMenu: '顶级菜单：使用 layout.base$view.xxx 或 layout.blank$view.xxx',
          directoryNoPage: '目录菜单不需要页面组件',
          menuNeedPage: '页面菜单：选择对应的页面组件（view.xxx）'
        },
        noPermissionDesc: '您没有查看菜单管理的权限',
        permission: {
          title: '权限管理',
          name: '权限名称',
          resource: '资源',
          description: '描述',
          status: '状态',
          sort: '排序',
          addButton: '添加权限',
          editButton: '编辑权限',
          form: {
            name: '请输入权限名称',
            resource: '请输入资源',
            description: '请输入描述',
            status: '请选择状态',
            sort: '请输入排序',
            resourceIntroduction:
              '资源格式说明：\n1. 资源格式为：模块:操作\n2. 例如：sys:user:add、sys:user:edit、sys:user:delete\n3. 多个资源用分号(;)分隔'
          },
          noPermissionDesc: '您没有查看权限管理的权限'
        }
      },
      gateway: {
        title: '网关日志',
        searchTitle: '搜索条件',
        tableTitle: '日志列表',
        noPermissionDesc: '您没有查看网关日志的权限',
        detail: {
          title: '网关日志详情',
          basicInfo: '基本信息',
          requestInfo: '请求信息',
          responseInfo: '响应信息',
          requestId: '请求ID',
          targetServer: '目标服务',
          requestTime: '请求时间',
          responseTime: '响应时间',
          executeTime: '耗时',
          protocol: '协议',
          clientIp: '客户端IP',
          userId: '用户ID',
          userName: '用户姓名',
          referer: '引用页面',
          requestMethod: '请求方法',
          requestPath: '请求路径',
          requestBody: '请求体',
          responseStatus: '响应状态',
          responseStatusDesc: '响应状态描述',
          responseData: '响应数据'
        },
        table: {
          userId: '用户ID',
          userName: '用户姓名',
          protocol: '协议',
          requestMethod: '请求方法',
          referer: '引用页面',
          requestPath: '请求路径',
          responseStatus: '响应状态',
          clientIp: '客户端IP',
          targetServer: '目标服务',
          executeTime: '耗时',
          requestTime: '请求时间',
          viewDetail: '查看详情'
        },
        search: {
          requestPath: '请求路径',
          requestMethod: '请求方法',
          responseStatus: '响应状态',
          clientIp: '客户端IP',
          targetServer: '目标服务',
          userId: '用户ID',
          timeRange: '时间范围',
          minExecuteTime: '最小耗时(ms)',
          maxExecuteTime: '最大耗时(ms)',
          reset: '重置',
          search: '搜索',
          placeholder: {
            requestPath: '请输入请求路径',
            requestMethod: '请选择请求方法',
            responseStatus: '请选择响应状态',
            clientIp: '请输入客户端IP',
            targetServer: '请输入目标服务',
            userId: '请输入用户ID',
            startTime: '开始时间',
            endTime: '结束时间',
            minExecuteTime: '请输入最小耗时',
            maxExecuteTime: '请输入最大耗时'
          },
          statusOptions: {
            success: '成功',
            badRequest: '请求错误',
            unauthorized: '未授权',
            forbidden: '禁止访问',
            notFound: '未找到',
            serverError: '服务器错误',
            badGateway: '网关错误',
            serviceUnavailable: '服务不可用'
          }
        }
      },
      notice: {
        categoryEnum: {
          notice: '通知',
          announcement: '公告'
        }
      },
      permission: {
        title: '权限管理',
        name: '权限名称',
        resource: '资源',
        description: '描述',
        status: '状态',
        sort: '排序',
        addButton: '添加权限',
        editButton: '编辑权限',
        form: {
          name: '请输入权限名称',
          resource: '请输入资源',
          description: '请输入描述',
          status: '请选择状态',
          sort: '请输入排序',
          resourceIntroduction:
            '资源格式说明：\n1. 资源格式为：模块:操作\n2. 例如：sys:user:add、sys:user:edit、sys:user:delete\n3. 多个资源用分号(;)分隔'
        }
      },
      device: {
        title: '设备管理',
        deviceId: '设备ID',
        mobileNo: '设备手机号',
        plateNo: '车牌号',
        deviceName: '设备名称',
        deviceType: '设备类型',
        manufacturer: '设备厂商',
        deviceModel: '设备型号',
        protocolVersion: '协议版本',
        status: '设备状态',
        statusDesc: '状态',
        isOnline: '在线状态',
        onlineDesc: '在线状态',
        lastOnlineTime: '最后在线时间',
        registerTime: '注册时间',
        driverId: '绑定司机',
        createTime: '创建时间',
        updateTime: '更新时间',
        createBy: '创建人',
        updateBy: '更新人',
        addDevice: '添加设备',
        editDevice: '编辑设备',
        deviceDetail: '设备详情',
        deleteDevice: '删除设备',
        batchDeleteDevice: '批量删除设备',
        enableDevice: '启用设备',
        disableDevice: '禁用设备',
        maintenanceDevice: '设备维护',
        deviceIdPlaceholder: '请输入设备ID',
        mobileNoPlaceholder: '请输入设备手机号',
        plateNoPlaceholder: '请输入车牌号',
        deviceNamePlaceholder: '请输入设备名称',
        deviceTypePlaceholder: '请选择设备类型',
        manufacturerPlaceholder: '请输入设备厂商',
        deviceModelPlaceholder: '请输入设备型号',
        protocolVersionPlaceholder: '请输入协议版本',
        statusPlaceholder: '请选择设备状态',
        driverIdPlaceholder: '请选择绑定司机',
        deviceIdRequired: '设备ID不能为空',
        mobileNoRequired: '设备手机号不能为空',
        statusRequired: '设备状态不能为空',
        deviceIdExists: '设备ID已存在',
        mobileNoExists: '设备手机号已存在',
        addDeviceSuccess: '设备添加成功',
        editDeviceSuccess: '设备编辑成功',
        deleteDeviceSuccess: '设备删除成功',
        batchDeleteDeviceSuccess: '设备批量删除成功',
        statusUpdateSuccess: '设备状态更新成功',
        confirmDelete: '确定要删除该设备吗？',
        confirmBatchDelete: '确定要删除选中的设备吗？',
        confirmStatusChange: '确定要修改设备状态吗？',
        statusEnabled: '启用',
        statusDisabled: '禁用',
        statusMaintenance: '维护中',
        onlineStatus: '在线',
        offlineStatus: '离线',
        noPermissionDesc: '您没有访问设备管理的权限，请联系管理员'
      },
      vehicleLocation: {
        title: '车辆定位',
        mapTitle: '车辆定位监控',
        deviceList: '设备列表',
        onlineDevices: '在线设备',
        totalDevices: '总设备数',
        lastUpdate: '最后更新',
        refresh: '刷新',
        search: '搜索车牌号、设备号...',
        deviceStatus: {
          online: '在线',
          offline: '离线',
          lost: '失联'
        },
        speedStatus: {
          stop: '静止',
          slow: '缓行',
          normal: '正常',
          fast: '高速'
        },
        track: '轨迹',
        trackPlayer: '轨迹回放',
        selectTimeRange: '选择时间范围',
        queryTrack: '查询轨迹',
        trackStats: {
          totalPoints: '轨迹点',
          totalDistance: '总距离(km)',
          duration: '总时长',
          maxSpeed: '最高速度(km/h)'
        },
        playControl: {
          play: '播放',
          pause: '暂停',
          reset: '重置',
          speed: '播放速度'
        },
        mapTools: {
          fitView: '适应视野',
          clearTrack: '清除轨迹'
        },
        deviceInfo: {
          longitude: '经度',
          latitude: '纬度',
          direction: '方向',
          satellite: '卫星',
          position: '位置',
          speed: '速度',
          updateTime: '更新时间'
        },
        noData: '暂无设备数据',
        noTrackData: '请选择时间范围并查询轨迹数据',
        noPermissionDesc: '您没有查看车辆定位的权限'
      }
    }
  },
  form: {
    required: '不能为空',
    userName: {
      required: '请输入用户名',
      invalid: '用户名格式不正确'
    },
    phone: {
      required: '请输入手机号',
      invalid: '手机号格式不正确'
    },
    pwd: {
      required: '请输入密码',
      invalid: '密码格式不正确，6-18位字符，包含字母、数字、下划线'
    },
    confirmPwd: {
      required: '请输入确认密码',
      invalid: '两次输入密码不一致'
    },
    code: {
      required: '请输入验证码',
      invalid: '验证码格式不正确'
    },
    email: {
      required: '请输入邮箱',
      invalid: '邮箱格式不正确'
    }
  },
  dropdown: {
    closeCurrent: '关闭',
    closeOther: '关闭其它',
    closeLeft: '关闭左侧',
    closeRight: '关闭右侧',
    closeAll: '关闭所有'
  },
  icon: {
    themeConfig: '主题配置',
    themeSchema: '主题模式',
    lang: '切换语言',
    fullscreen: '全屏',
    fullscreenExit: '退出全屏',
    reload: '刷新页面',
    collapse: '折叠菜单',
    expand: '展开菜单',
    pin: '固定',
    unpin: '取消固定'
  },
  datatable: {
    itemCount: '共 {total} 条'
  }
};

export default local;

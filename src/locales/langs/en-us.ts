const local: App.I18n.Schema = {
  system: {
    title: 'SoybeanAdmin',
    updateTitle: 'System Version Update Notification',
    updateContent: 'A new version of the system has been detected. Do you want to refresh the page immediately?',
    updateConfirm: 'Refresh immediately',
    updateCancel: 'Later'
  },
  common: {
    action: 'Action',
    add: 'Add',
    addSuccess: 'Add Success',
    backToHome: 'Back to home',
    batchDelete: 'Batch Delete',
    cancel: 'Cancel',
    close: 'Close',
    check: 'Check',
    expandColumn: 'Expand Column',
    columnSetting: 'Column Setting',
    config: 'Config',
    confirm: 'Confirm',
    delete: 'Delete',
    deleteSuccess: 'Delete Success',
    deleteError: 'Delete Failed',
    confirmDelete: 'Are you sure you want to delete?',
    view: 'View',
    edit: 'Edit',
    warning: 'Warning',
    error: 'Error',
    index: 'Index',
    keywordSearch: 'Please enter keyword',
    logout: 'Logout',
    logoutConfirm: 'Are you sure you want to log out?',
    lookForward: 'Coming soon',
    modify: 'Modify',
    modifySuccess: 'Modify Success',
    modifyFailed: 'Modify Failed',
    requestError: 'Request Error',
    more: 'More',
    moreOperation: 'More Operation',
    noData: 'No Data',
    operate: 'Operate',
    pleaseCheckValue: 'Please check whether the value is valid',
    refresh: 'Refresh',
    reset: 'Reset',
    search: 'Search',
    switch: 'Switch',
    tip: 'Tip',
    trigger: 'Trigger',
    update: 'Update',
    updateSuccess: 'Update Success',
    userCenter: 'User Center',
    upload: 'Upload',
    uploadSuccess: 'Upload Success',
    yesOrNo: {
      yes: 'Yes',
      no: 'No'
    },
    enableStatus: {
      enable: 'Enable',
      disable: 'Disable'
    },
    userGender: {
      male: 'Male',
      female: 'Female'
    },
    noPermission: 'No Permission to Access'
  },
  request: {
    logout: 'Logout user after request failed',
    logoutMsg: 'User status is invalid, please log in again',
    logoutWithModal: 'Pop up modal after request failed and then log out user',
    logoutWithModalMsg: 'User status is invalid, please log in again',
    refreshToken: 'The requested token has expired, refresh the token',
    tokenExpired: 'The requested token has expired'
  },
  theme: {
    themeSchema: {
      title: 'Theme Schema',
      light: 'Light',
      dark: 'Dark',
      auto: 'Follow System'
    },
    grayscale: 'Grayscale',
    colourWeakness: 'Colour Weakness',
    layoutMode: {
      title: 'Layout Mode',
      vertical: 'Vertical Menu Mode',
      horizontal: 'Horizontal Menu Mode',
      'vertical-mix': 'Vertical Mix Menu Mode',
      'horizontal-mix': 'Horizontal Mix menu Mode',
      reverseHorizontalMix: 'Reverse first level menus and child level menus position'
    },
    recommendColor: 'Apply Recommended Color Algorithm',
    recommendColorDesc: 'The recommended color algorithm refers to',
    themeColor: {
      title: 'Theme Color',
      primary: 'Primary',
      info: 'Info',
      success: 'Success',
      warning: 'Warning',
      error: 'Error',
      followPrimary: 'Follow Primary'
    },
    scrollMode: {
      title: 'Scroll Mode',
      wrapper: 'Wrapper',
      content: 'Content'
    },
    page: {
      animate: 'Page Animate',
      mode: {
        title: 'Page Animate Mode',
        fade: 'Fade',
        'fade-slide': 'Slide',
        'fade-bottom': 'Fade Zoom',
        'fade-scale': 'Fade Scale',
        'zoom-fade': 'Zoom Fade',
        'zoom-out': 'Zoom Out',
        none: 'None'
      }
    },
    fixedHeaderAndTab: 'Fixed Header And Tab',
    header: {
      height: 'Header Height',
      breadcrumb: {
        visible: 'Breadcrumb Visible',
        showIcon: 'Breadcrumb Icon Visible'
      },
      multilingual: {
        visible: 'Display multilingual button'
      }
    },
    tab: {
      visible: 'Tab Visible',
      cache: 'Tag Bar Info Cache',
      height: 'Tab Height',
      mode: {
        title: 'Tab Mode',
        chrome: 'Chrome',
        button: 'Button'
      }
    },
    sider: {
      inverted: 'Dark Sider',
      width: 'Sider Width',
      collapsedWidth: 'Sider Collapsed Width',
      mixWidth: 'Mix Sider Width',
      mixCollapsedWidth: 'Mix Sider Collapse Width',
      mixChildMenuWidth: 'Mix Child Menu Width'
    },
    footer: {
      visible: 'Footer Visible',
      fixed: 'Fixed Footer',
      height: 'Footer Height',
      right: 'Right Footer'
    },
    watermark: {
      visible: 'Watermark Full Screen Visible',
      text: 'Watermark Text'
    },
    themeDrawerTitle: 'Theme Configuration',
    pageFunTitle: 'Page Function',
    resetCacheStrategy: {
      title: 'Reset Cache Strategy',
      close: 'Close Page',
      refresh: 'Refresh Page'
    },
    configOperation: {
      copyConfig: 'Copy Config',
      copySuccessMsg: 'Copy Success, Please replace the variable "themeSettings" in "src/theme/settings.ts"',
      resetConfig: 'Reset Config',
      resetSuccessMsg: 'Reset Success'
    }
  },
  route: {
    login: 'Login',
    403: 'No Permission',
    404: 'Page Not Found',
    500: 'Server Error',
    'iframe-page': 'Iframe',
    home: 'Home',
    manage: 'System Manage',
    manage_user: 'User Manage',
    manage_role: 'Role Manage',
    manage_menu: 'Menu Manage',
    manage_org: 'Organization Manage',
    manage_gateway: 'Gateway Management',
    'manage_user-detail': 'User Detail',
    aichat: 'AI Chat',
    devicemanagement: 'Device Management',
    devicemanagement_device: 'Device Management',
    'devicemanagement_vehicle-location': 'Vehicle Location'
  },
  ai: {
    chat: {
      title: 'AI Assistant',
      connected: 'Connected',
      disconnected: 'Disconnected',
      messages: 'messages',
      newChat: 'New Chat',
      config: 'Config',
      clear: 'Clear',
      configureFirst: 'Configure AI Assistant',
      configureDescription: 'Please configure DeepSeek API Key before using AI Assistant',
      configureNow: 'Configure Now',
      startChat: 'Start Chat',
      inputPlaceholder: 'Enter message, press Shift+Enter for new line...',
      send: 'Send',
      copy: 'Copy',
      justNow: 'Just now',
      minutesAgo: 'minutes ago',
      hoursAgo: 'hours ago',
      startChatting: 'Start chatting',
      newChatCreated: 'New chat created',
      configSaved: 'Configuration saved',
      historyCleared: 'Chat history cleared',
      chatStarted: 'Chat started',
      copiedToClipboard: 'Copied to clipboard',
      copyFailed: 'Copy failed',
      connectionSuccess: 'Connection successful',
      connectionFailed: 'Connection failed, please check configuration',
      welcomeMessage: 'Hello! I am your AI assistant, happy to help you. How can I assist you today?',
      minimize: 'Minimize',
      maximize: 'Maximize',
      close: 'Close'
    },
    welcome: {
      title: 'Hello, I am AI Assistant',
      description:
        "Powered by advanced artificial intelligence technology, I can help you answer questions, assist with work, and provide suggestions. Let's start smart conversations!",
      quickStart: 'Quick Start:',
      features: 'Key Features:',
      startChat: 'Start Chat',
      learnFeatures: 'Learn Features',
      analyzeProblems: 'Analyze Problems',
      getAdvice: 'Get Advice',
      examplePrompts: {
        learnFeatures: 'Please introduce your functions',
        analyzeProblems: 'Help me analyze this problem',
        getAdvice: 'Please give me some advice'
      },
      featureList: {
        smartQA: 'Smart Q&A and Conversation',
        docAnalysis: 'Document Analysis and Summary',
        efficiency: 'Work Efficiency Enhancement',
        streaming: 'Real-time Streaming Response'
      }
    },
    config: {
      title: 'AI Assistant Configuration',
      apiKey: 'API Key',
      apiKeyPlaceholder: 'Please enter DeepSeek API Key',
      apiUrl: 'API URL',
      model: 'Model',
      modelPlaceholder: 'Select model',
      maxTokens: 'Max Tokens',
      temperature: 'Temperature',
      testConnection: 'Test Connection',
      saveConfig: 'Save Configuration',
      validation: {
        apiKeyRequired: 'Please enter API Key',
        apiUrlRequired: 'Please enter API URL',
        modelRequired: 'Please select model',
        maxTokensRange: 'Max tokens must be between 1-8192',
        temperatureRange: 'Temperature must be between 0-2'
      }
    }
  },
  page: {
    login: {
      common: {
        loginOrRegister: 'Login / Register',
        userNamePlaceholder: 'Please enter user name',
        phonePlaceholder: 'Please enter phone number',
        codePlaceholder: 'Please enter verification code',
        passwordPlaceholder: 'Please enter password',
        confirmPasswordPlaceholder: 'Please enter password again',
        codeLogin: 'Verification code login',
        confirm: 'Confirm',
        back: 'Back',
        validateSuccess: 'Verification passed',
        loginSuccess: 'Login successfully',
        welcomeBack: 'Welcome back, {userName} !'
      },
      pwdLogin: {
        title: 'Password Login',
        rememberMe: 'Remember me',
        forgetPassword: 'Forget password?',
        register: 'Register',
        otherAccountLogin: 'Other Account Login',
        otherLoginMode: 'Other Login Mode',
        superAdmin: 'Super Admin',
        admin: 'Admin',
        user: 'User'
      },
      codeLogin: {
        title: 'Verification Code Login',
        getCode: 'Get verification code',
        reGetCode: 'Reacquire after {time}s',
        sendCodeSuccess: 'Verification code sent successfully',
        imageCodePlaceholder: 'Please enter image verification code'
      },
      register: {
        title: 'Register',
        agreement: 'I have read and agree to',
        protocol: '《User Agreement》',
        policy: '《Privacy Policy》'
      },
      resetPwd: {
        title: 'Reset Password'
      },
      bindWeChat: {
        title: 'Bind WeChat'
      }
    },
    home: {
      branchDesc:
        'For the convenience of everyone in developing and updating the merge, we have streamlined the code of the main branch, only retaining the homepage menu, and the rest of the content has been moved to the example branch for maintenance. The preview address displays the content of the example branch.',
      greeting: 'Good morning, {userName}, today is another day full of vitality!',
      weatherDesc: 'Today is cloudy to clear, 20℃ - 25℃!',
      projectCount: 'Project Count',
      todo: 'Todo',
      message: 'Message',
      downloadCount: 'Download Count',
      registerCount: 'Register Count',
      schedule: 'Work and rest Schedule',
      study: 'Study',
      work: 'Work',
      rest: 'Rest',
      entertainment: 'Entertainment',
      visitCount: 'Visit Count',
      turnover: 'Turnover',
      dealCount: 'Deal Count',
      projectNews: {
        title: 'Project News',
        moreNews: 'More News',
        desc1: 'Soybean created the open source project soybean-admin on May 28, 2021!',
        desc2: 'Yanbowe submitted a bug to soybean-admin, the multi-tab bar will not adapt.',
        desc3: 'Soybean is ready to do sufficient preparation for the release of soybean-admin!',
        desc4: 'Soybean is busy writing project documentation for soybean-admin!',
        desc5: 'Soybean just wrote some of the workbench pages casually, and it was enough to see!'
      },
      creativity: 'Creativity'
    },
    manage: {
      user: {
        title: 'User List',
        selectTreeIsEmptyTip: 'Please select organization from left tree',
        userName: 'Username',
        realName: 'Real Name',
        gender: 'Gender',
        phone: 'Phone',
        email: 'Email',
        status: 'Status',
        password: 'Password',
        userRole: 'User Role',
        userOrgUnits: 'Organization',
        roleEnum: {
          admin: 'Administrator',
          user: 'User'
        },
        positionEnum: {
          manager: 'Manager',
          staff: 'Staff'
        },
        orgEnum: {
          dept1: 'Department 1',
          dept2: 'Department 2'
        },
        resetPwd: 'Reset Password',
        resetPwdSuccess: 'Password reset successful',
        resetPwdFail: 'Password reset failed',
        resetPassword: 'Reset Password',
        newPassword: 'New Password',
        unassigned: 'Unassigned',
        noPermissionDesc: 'You do not have permission to view user management',
        addUser: 'Add User',
        editUser: 'Edit User',
        deleteUser: 'Delete User',
        batchDeleteUser: 'Batch Delete Users',
        pleaseSelectUser: 'Please select users to operate',
        form: {
          userName: 'Please enter username',
          password: 'Please enter password',
          realName: 'Please enter real name',
          phone: 'Please enter phone number',
          email: 'Please enter email',
          userRole: 'Please select user role',
          userOrgUnits: 'Please select organization'
        },
        validate: {
          passwordRequired: 'Password is required',
          passwordLength: 'Password must be at least 6 characters',
          phoneInvalid: 'Please enter a valid phone number',
          emailInvalid: 'Please enter a valid email'
        }
      },
      orgUnits: {
        title: 'Organization Structure',
        name: 'Organization Name',
        code: 'Organization Code',
        abbr: 'Organization Abbreviation',
        level: 'Organization Level',
        description: 'Organization Description',
        status: 'Organization Status',
        sort: 'Sort',
        unassigned: 'Unassigned',
        orgStructure: 'Organization Structure',
        addOrgUnits: 'Add Organization',
        editOrgUnits: 'Edit Organization',
        addChildOrgUnits: 'Add Child Organization',
        noPermissionDesc: 'You do not have permission to view organization management',
        form: {
          name: 'Please enter organization name',
          code: 'Please enter organization code',
          abbr: 'Please enter organization abbreviation',
          description: 'Please enter organization description',
          status: 'Please select organization status',
          sort: 'Please enter sort order'
        }
      },
      role: {
        title: 'Role Management',
        roleName: 'Role Name',
        roleCode: 'Role Code',
        roleDesc: 'Role Description',
        roleStatus: 'Role Status',
        status: 'Status',
        sort: 'Sort',
        addRole: 'Add Role',
        editRole: 'Edit Role',
        menuAuth: 'Menu Permission',
        buttonAuth: 'Button Permission',
        selectAll: 'Select All',
        unselectAll: 'Unselect All',
        selectGroup: 'Select Group',
        unselectGroup: 'Unselect Group',
        selected: 'Selected',
        items: 'items',
        permissions: ' permissions',
        noPermissionDesc: 'You do not have permission to view role management',
        form: {
          roleName: 'Please enter role name',
          roleCode: 'Please enter role code',
          roleDesc: 'Please enter role description',
          roleStatus: 'Please select role status',
          status: 'Please select status'
        }
      },
      menu: {
        title: 'Menu Management',
        id: 'Menu ID',
        menuType: 'Menu Type',
        icon: 'Icon',
        iconTypeTitle: 'Icon Type',
        routeName: 'Route Name',
        routePath: 'Route Path',
        menuStatus: 'Menu Status',
        status: 'Status',
        hideInMenu: 'Hide Menu',
        parentId: 'Parent ID',
        order: 'Order',
        sort: 'Sort',
        addChildMenu: 'Add Child Menu',
        addMenu: 'Add Menu',
        editMenu: 'Edit Menu',
        keepAlive: 'Cache Route',
        constant: 'Constant Route',
        href: 'External Link',
        iframeUrl: 'Iframe URL',
        activeMenu: 'Active Menu',
        multiTab: 'Support Multi Tab',
        fixedIndexInTab: 'Fixed Tab Index',
        query: 'Route Query',
        pathParam: 'Path Parameter',
        i18nKey: 'I18n Key',
        button: 'Button',
        layout: 'Layout Component',
        page: 'Page Component',
        home: 'Home',
        detail: 'Menu Detail',
        type: 'Menu Type',
        name: 'Menu Name',
        selectTreeIsEmptyTip: 'Please select menu from left tree',
        menuTypeIsDirectory: 'Please select directory type menu',
        menuTypeEnum: {
          topDirectory: 'Top Directory',
          subMenu: 'Sub Menu',
          singleMenu: 'Single Menu',
          specialPage: 'Special Page',
          // 保留旧的键值以兼容
          directory: 'Directory',
          menu: 'Menu'
        },
        iconTypeEnum: {
          iconify: 'Iconify',
          local: 'Local'
        },
        form: {
          name: 'Please enter menu name',
          routeName: 'Please enter route name',
          routePath: 'Route path will be generated automatically based on route name',
          pathParam: 'Please enter path parameter',
          i18nKey: 'I18n key',
          icon: 'Please enter iconify icon name',
          localIcon: 'Please select local icon',
          order: 'Please enter order',
          sort: 'Please enter sort',
          layout: 'Please select layout component',
          page: 'Please select page component',
          href: 'Please enter external link',
          iframeUrl: 'Please enter iframe URL',
          activeMenu: 'Please select active menu',
          fixedIndexInTab: 'Please enter fixed tab index',
          queryKey: 'Query Key',
          queryValue: 'Query Value',
          buttonCode: 'Button Code',
          buttonDesc: 'Button Description'
        },
        componentTips: {
          topDirectory: 'Top directory: use layout.base',
          subMenu: 'Sub menu: use view.xxx (inherit parent layout)',
          singleMenu: 'Single menu: use layout.base$view.xxx',
          specialPage: 'Special page: use layout.blank$view.xxx',
          topDirectoryNoPage: 'Top directory does not need page component',
          subMenuNeedPage: 'Sub menu: select corresponding page component (view.xxx)',
          singleMenuNeedPage: 'Single menu: select corresponding page component (view.xxx)',
          specialPageNeedPage: 'Special page: select corresponding page component (view.xxx)',
          // 保留旧的键值以兼容
          directory: 'Directory menu: use layout.base',
          topLevelMenu: 'Top-level menu: use layout.base$view.xxx or layout.blank$view.xxx',
          directoryNoPage: 'Directory menu does not need page component',
          menuNeedPage: 'Page menu: select corresponding page component (view.xxx)'
        },
        noPermissionDesc: 'You do not have permission to view menu management',
        permission: {
          title: 'Permission Management',
          name: 'Permission Name',
          resource: 'Resource',
          description: 'Description',
          status: 'Status',
          sort: 'Sort',
          addButton: 'Add Permission',
          editButton: 'Edit Permission',
          form: {
            name: 'Please enter permission name',
            resource: 'Please enter resource',
            description: 'Please enter description',
            status: 'Please select status',
            sort: 'Please enter sort',
            resourceIntroduction:
              'Resource format description:\n1. Resource format: module:operation\n2. Example: sys:user:add, sys:user:edit, sys:user:delete\n3. Multiple resources separated by semicolon(;)'
          },
          noPermissionDesc: 'You do not have permission to view permission management'
        }
      },
      gateway: {
        title: 'Gateway Logs',
        searchTitle: 'Search Conditions',
        tableTitle: 'Log List',
        noPermissionDesc: 'You do not have permission to view gateway logs',
        detail: {
          title: 'Gateway Log Details',
          basicInfo: 'Basic Information',
          requestInfo: 'Request Information',
          responseInfo: 'Response Information',
          requestId: 'Request ID',
          targetServer: 'Target Server',
          requestTime: 'Request Time',
          responseTime: 'Response Time',
          executeTime: 'Execution Time',
          protocol: 'Protocol',
          clientIp: 'Client IP',
          userId: 'User ID',
          userName: 'User Name',
          referer: 'Referer',
          requestMethod: 'Request Method',
          requestPath: 'Request Path',
          requestBody: 'Request Body',
          responseStatus: 'Response Status',
          responseStatusDesc: 'Response Status Description',
          responseData: 'Response Data'
        },
        table: {
          userId: 'User ID',
          userName: 'User Name',
          protocol: 'Protocol',
          requestMethod: 'Request Method',
          referer: 'Referer',
          requestPath: 'Request Path',
          responseStatus: 'Response Status',
          clientIp: 'Client IP',
          targetServer: 'Target Server',
          executeTime: 'Execution Time',
          requestTime: 'Request Time',
          viewDetail: 'View Details'
        },
        search: {
          requestPath: 'Request Path',
          requestMethod: 'Request Method',
          responseStatus: 'Response Status',
          clientIp: 'Client IP',
          targetServer: 'Target Server',
          userId: 'User ID',
          timeRange: 'Time Range',
          minExecuteTime: 'Min Execution Time (ms)',
          maxExecuteTime: 'Max Execution Time (ms)',
          reset: 'Reset',
          search: 'Search',
          placeholder: {
            requestPath: 'Enter request path',
            requestMethod: 'Select request method',
            responseStatus: 'Select response status',
            clientIp: 'Enter client IP',
            targetServer: 'Enter target server',
            userId: 'Enter user ID',
            startTime: 'Start time',
            endTime: 'End time',
            minExecuteTime: 'Enter minimum execution time',
            maxExecuteTime: 'Enter maximum execution time'
          },
          statusOptions: {
            success: 'Success',
            badRequest: 'Bad Request',
            unauthorized: 'Unauthorized',
            forbidden: 'Forbidden',
            notFound: 'Not Found',
            serverError: 'Server Error',
            badGateway: 'Bad Gateway',
            serviceUnavailable: 'Service Unavailable'
          }
        }
      },
      notice: {
        categoryEnum: {
          notice: 'Notice',
          announcement: 'Announcement'
        }
      },
      permission: {
        title: 'Permission Management',
        name: 'Permission Name',
        resource: 'Resource',
        description: 'Description',
        status: 'Status',
        sort: 'Sort',
        addButton: 'Add Permission',
        editButton: 'Edit Permission',
        form: {
          name: 'Please enter permission name',
          resource: 'Please enter resource',
          description: 'Please enter description',
          status: 'Please select status',
          sort: 'Please enter sort',
          resourceIntroduction:
            'Resource format description:\n1. Resource format: module:operation\n2. Example: sys:user:add, sys:user:edit, sys:user:delete\n3. Multiple resources separated by semicolon(;)'
        }
      },
      device: {
        title: 'Device Management',
        deviceId: 'Device ID',
        mobileNo: 'Mobile Number',
        plateNo: 'Plate Number',
        deviceName: 'Device Name',
        deviceType: 'Device Type',
        manufacturer: 'Manufacturer',
        deviceModel: 'Device Model',
        protocolVersion: 'Protocol Version',
        status: 'Device Status',
        statusDesc: 'Status',
        isOnline: 'Online Status',
        onlineDesc: 'Online Status',
        lastOnlineTime: 'Last Online Time',
        registerTime: 'Register Time',
        driverId: 'Bound Driver',
        createTime: 'Create Time',
        updateTime: 'Update Time',
        createBy: 'Created By',
        updateBy: 'Updated By',
        addDevice: 'Add Device',
        editDevice: 'Edit Device',
        deviceDetail: 'Device Detail',
        deleteDevice: 'Delete Device',
        batchDeleteDevice: 'Batch Delete Device',
        enableDevice: 'Enable Device',
        disableDevice: 'Disable Device',
        maintenanceDevice: 'Device Maintenance',
        deviceIdPlaceholder: 'Please enter device ID',
        mobileNoPlaceholder: 'Please enter mobile number',
        plateNoPlaceholder: 'Please enter plate number',
        deviceNamePlaceholder: 'Please enter device name',
        deviceTypePlaceholder: 'Please select device type',
        manufacturerPlaceholder: 'Please enter manufacturer',
        deviceModelPlaceholder: 'Please enter device model',
        protocolVersionPlaceholder: 'Please enter protocol version',
        statusPlaceholder: 'Please select device status',
        driverIdPlaceholder: 'Please select bound driver',
        deviceIdRequired: 'Device ID is required',
        mobileNoRequired: 'Mobile number is required',
        statusRequired: 'Device status is required',
        deviceIdExists: 'Device ID already exists',
        mobileNoExists: 'Mobile number already exists',
        addDeviceSuccess: 'Device added successfully',
        editDeviceSuccess: 'Device edited successfully',
        deleteDeviceSuccess: 'Device deleted successfully',
        batchDeleteDeviceSuccess: 'Devices deleted successfully',
        statusUpdateSuccess: 'Device status updated successfully',
        confirmDelete: 'Are you sure to delete this device?',
        confirmBatchDelete: 'Are you sure to delete selected devices?',
        confirmStatusChange: 'Are you sure to change device status?',
        statusEnabled: 'Enabled',
        statusDisabled: 'Disabled',
        statusMaintenance: 'Maintenance',
        onlineStatus: 'Online',
        offlineStatus: 'Offline',
        noPermissionDesc: 'You do not have permission to access device management, please contact administrator'
      },
      vehicleLocation: {
        title: 'Vehicle Location',
        mapTitle: 'Vehicle Location Monitoring',
        deviceList: 'Device List',
        onlineDevices: 'Online Devices',
        totalDevices: 'Total Devices',
        lastUpdate: 'Last Update',
        refresh: 'Refresh',
        search: 'Search plate number, device ID...',
        deviceStatus: {
          online: 'Online',
          offline: 'Offline',
          lost: 'Lost'
        },
        speedStatus: {
          stop: 'Stop',
          slow: 'Slow',
          normal: 'Normal',
          fast: 'Fast'
        },
        track: 'Track',
        trackPlayer: 'Track Player',
        selectTimeRange: 'Select Time Range',
        queryTrack: 'Query Track',
        trackStats: {
          totalPoints: 'Track Points',
          totalDistance: 'Total Distance(km)',
          duration: 'Duration',
          maxSpeed: 'Max Speed(km/h)'
        },
        playControl: {
          play: 'Play',
          pause: 'Pause',
          reset: 'Reset',
          speed: 'Play Speed'
        },
        mapTools: {
          fitView: 'Fit View',
          clearTrack: 'Clear Track'
        },
        deviceInfo: {
          longitude: 'Longitude',
          latitude: 'Latitude',
          direction: 'Direction',
          satellite: 'Satellite',
          position: 'Position',
          speed: 'Speed',
          updateTime: 'Update Time'
        },
        noData: 'No device data',
        noTrackData: 'Please select time range and query track data',
        noPermissionDesc: 'You do not have permission to view vehicle location'
      }
    }
  },
  form: {
    required: 'Cannot be empty',
    userName: {
      required: 'Please enter user name',
      invalid: 'User name format is incorrect'
    },
    phone: {
      required: 'Please enter phone number',
      invalid: 'Phone number format is incorrect'
    },
    pwd: {
      required: 'Please enter password',
      invalid: '6-18 characters, including letters, numbers, and underscores'
    },
    confirmPwd: {
      required: 'Please enter password again',
      invalid: 'The two passwords are inconsistent'
    },
    code: {
      required: 'Please enter verification code',
      invalid: 'Verification code format is incorrect'
    },
    email: {
      required: 'Please enter email',
      invalid: 'Email format is incorrect'
    }
  },
  dropdown: {
    closeCurrent: 'Close Current',
    closeOther: 'Close Other',
    closeLeft: 'Close Left',
    closeRight: 'Close Right',
    closeAll: 'Close All'
  },
  icon: {
    themeConfig: 'Theme Configuration',
    themeSchema: 'Theme Schema',
    lang: 'Switch Language',
    fullscreen: 'Fullscreen',
    fullscreenExit: 'Exit Fullscreen',
    reload: 'Reload Page',
    collapse: 'Collapse Menu',
    expand: 'Expand Menu',
    pin: 'Pin',
    unpin: 'Unpin'
  },
  datatable: {
    itemCount: 'Total {total} items'
  }
};

export default local;

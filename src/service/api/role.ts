import { authServiceRequest } from '@/service/request/microservice';

/** 获取角色列表 */
export function fetchGetRoleList(params: Api.SystemManage.RoleSearchParams & Api.Common.CommonSearchParams) {
  return authServiceRequest<Api.Common.PaginatingQueryRecord<Api.SystemManage.Role>>({
    url: '/role/getRoleList',
    method: 'get',
    params
  });
}

/** 获取所有页面列表 */
export function fetchGetAllPages() {
  return authServiceRequest<Api.Common.CommonRecord<Api.SystemManage.Page>[]>({
    url: '/menu/getAllPages'
  });
}

/** 获取菜单树 */
export function fetchGetMenuTree() {
  return authServiceRequest<Api.SystemManage.MenuTree[]>({
    url: '/menu/tree'
  });
}

/** 删除角色 */
export function fetchDeleteRole(id: number) {
  return authServiceRequest({
    url: '/role/deleteRole',
    method: 'delete',
    params: { id }
  });
}

/** 批量删除角色 */
export function fetchDeleteRoleBatch(ids: number[]) {
  return authServiceRequest({
    url: '/role/deleteRoleBatch',
    method: 'delete',
    data: ids
  });
}

/** 添加角色 */
export function fetchAddRole(data: Omit<Api.SystemManage.RoleEdit, 'id'>) {
  return authServiceRequest({
    url: '/role/addRole',
    method: 'post',
    data
  });
}

/** 更新角色信息 */
export function fetchUpdateRoleInfo(data: Api.SystemManage.RoleEdit) {
  return authServiceRequest({
    url: '/role/updateRole',
    method: 'post',
    data
  });
}

/** 获取角色菜单IDs */
export function fetchGetRoleMenuIds(roleId: string) {
  return authServiceRequest<string[]>({
    url: '/menu/getRoleMenuIds',
    method: 'get',
    params: { roleId }
  });
}

/** 添加角色菜单 */
export function fetchAddRoleMenu(data: Api.SystemManage.RoleMenu) {
  return authServiceRequest({
    url: '/menu/updateRoleMenuIds',
    method: 'post',
    data
  });
}

/** 获取角色权限IDs */
export function fetchGetRolePermissionIds(roleId: string) {
  return authServiceRequest<string[]>({
    url: '/menu/getMenuPermissionByRoleId',
    method: 'get',
    params: { roleId }
  });
}

/** 添加角色权限 */
export function fetchAddRolePermission(data: Api.SystemManage.RolePermission) {
  return authServiceRequest({
    url: '/permission/updateRolePermission',
    method: 'post',
    data
  });
}

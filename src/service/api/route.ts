import { authServiceRequest } from '../request/microservice';

/** 获取常量路由 */
export function fetchGetConstantRoutes() {
  return authServiceRequest<Api.Route.MenuRoute[]>({ url: '/route/getConstantRoutes' });
}

/** 获取用户路由 */
export function fetchGetUserRoutes() {
  return authServiceRequest<any>({ url: '/route/getUserRoutes' });
}

/**
 * 判断路由是否存在
 *
 * @param routeName 路由名称
 */
export function fetchIsRouteExist(routeName: string) {
  return authServiceRequest<boolean>({ url: '/route/isRouteExist', params: { routeName } });
}

import { jt808ServiceRequest } from '@/service/request/microservice';

/** 获取设备最新位置 */
export function fetchGetLatestLocation(deviceId: string) {
  return jt808ServiceRequest<Api.Location.DeviceLocationVO>({
    url: `/location/latest/${deviceId}`,
    method: 'get'
  });
}

/** 获取所有在线设备的最新位置（地图展示） */
export function fetchGetAllOnlineDeviceLocations() {
  return jt808ServiceRequest<Api.Location.DeviceLocationVO[]>({
    url: '/location/online/all',
    method: 'get'
  });
}

/** 获取设备轨迹（指定时间范围） */
export function fetchGetDeviceTrack(params: Api.Location.DeviceTrackParams) {
  return jt808ServiceRequest<Api.Location.DeviceLocationVO[]>({
    url: `/location/track/${params.deviceId}`,
    method: 'get',
    params: {
      startTime: params.startTime,
      endTime: params.endTime,
      limit: params.limit || 1000
    }
  });
}

/** 获取设备最近轨迹 */
export function fetchGetRecentTrack(deviceId: string, count: number = 100) {
  return jt808ServiceRequest<Api.Location.DeviceLocationVO[]>({
    url: `/location/track/recent/${deviceId}`,
    method: 'get',
    params: { count }
  });
}

/** 获取指定区域内的设备位置 */
export function fetchGetLocationsByArea(params: Api.Location.AreaQueryParams) {
  return jt808ServiceRequest<Api.Location.DeviceLocationVO[]>({
    url: '/location/area',
    method: 'get',
    params
  });
}

/** 获取设备位置统计信息 */
export function fetchGetLocationStats(params?: Api.Location.LocationStatsParams) {
  return jt808ServiceRequest<Api.Location.LocationStatsVO>({
    url: '/location/stats',
    method: 'get',
    params
  });
}

/** 逆地理编码 - 根据经纬度获取地址 */
export function fetchReverseGeocode(lng: number, lat: number) {
  return jt808ServiceRequest<string>({
    url: '/location/geocode',
    method: 'get',
    params: { lng, lat }
  });
}

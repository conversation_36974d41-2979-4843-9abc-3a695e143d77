import { authServiceRequest } from '@/service/request/microservice';
export * from './auth';
export * from './route';
export * from './org-units';
export * from './gateway-log';
export * from './device';
export * from './location';
export {
  fetchGetRoleList,
  fetchGetMenuTree,
  fetchGetAllPages as fetchGetAllPagesFromRole,
  fetchDeleteRole,
  fetchDeleteRoleBatch,
  fetchAddRole,
  fetchUpdateRoleInfo,
  fetchGetRoleMenuIds,
  fetchAddRoleMenu,
  fetchGetRolePermissionIds,
  fetchAddRolePermission
} from './role';
export {
  fetchAddMenu,
  fetchDeleteMenu,
  fetchGetMenuPermission,
  fetchAddPermission,
  fetchUpdatePermissionInfo,
  fetchGetAllPermissionAnnotations,
  fetchDeletePermission,
  fetchGetPermissionList,
  fetchGetEditMenuInfo,
  fetchUpdateMenuInfo,
  fetchGetAllPages
} from './menu';

/** 获取所有角色列表 */
export function fetchGetAllRoles() {
  return authServiceRequest<Api.Common.CommonRecord<Api.SystemManage.Role>[]>({
    url: '/role/getAllRoles'
  });
}

/** 添加用户 */
export function fetchAddUser(data: Omit<Api.SystemManage.User, 'id'>) {
  return authServiceRequest({
    url: '/systemManage/addUser',
    method: 'post',
    data
  });
}

/** 更新用户 */
export function fetchUpdateUser(data: Api.SystemManage.User) {
  return authServiceRequest({
    url: '/systemManage/updateUser',
    method: 'post',
    data
  });
}

/** 删除用户 */
export function fetchDeleteUser(id: number) {
  return authServiceRequest({
    url: '/systemManage/deleteUser',
    method: 'delete',
    params: { id }
  });
}

/** 批量删除用户 */
export function fetchBatchDeleteUser(ids: number[]) {
  return authServiceRequest({
    url: '/systemManage/batchDeleteUser',
    method: 'post',
    data: ids
  });
}

/** 根据ID获取用户信息 */
export function fetchGetUserById(id: string) {
  return authServiceRequest<Api.SystemManage.User>({
    url: '/systemManage/getUserById',
    method: 'get',
    params: { id }
  });
}

/** 重置用户密码 */
export function fetchResetUserPassword(id: string) {
  return authServiceRequest<string>({
    url: '/systemManage/resetUserPassword',
    method: 'post',
    params: { id }
  });
}

/** 获取用户管理列表 */
export function fetchGetUserManageList(params: Api.SystemManage.UserSearchParams & Api.Common.CommonSearchParams) {
  return authServiceRequest<Api.Common.PaginatingQueryRecord<Api.SystemManage.User>>({
    url: '/systemManage/getUserManageList',
    method: 'get',
    params
  });
}

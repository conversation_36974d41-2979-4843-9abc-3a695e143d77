import { authServiceRequest } from '@/service/request/microservice';

/** 分页查询网关日志 */
export function fetchGetGatewayLogList(params: Api.Gateway.LogQueryParams) {
  return authServiceRequest<Api.Common.PaginatingQueryRecord<Api.Gateway.Log>>({
    url: '/gateway-log/list',
    method: 'get',
    params
  });
}

/** 根据ID查询网关日志详情 */
export function fetchGetGatewayLogById(id: string) {
  return authServiceRequest<Api.Gateway.Log>({
    url: `/gateway-log/${id}`,
    method: 'get'
  });
}

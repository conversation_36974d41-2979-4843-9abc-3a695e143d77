import { authServiceRequest } from '@/service/request/microservice';

/** 获取所有页面 */
export function fetchGetAllPages() {
  return authServiceRequest<Api.Common.CommonRecord<Api.SystemManage.Page>[]>({
    url: '/menu/getAllPages'
  });
}

/** 添加菜单 */
export function fetchAddMenu(data: Omit<Api.SystemManage.Menu, 'id'>) {
  return authServiceRequest({
    url: '/menu/addMenu',
    method: 'post',
    data
  });
}

/** 删除菜单 */
export function fetchDeleteMenu(id: number) {
  return authServiceRequest({
    url: '/menu/deleteMenu',
    method: 'delete',
    params: { id }
  });
}

/** 获取菜单按钮权限 */
export function fetchGetMenuPermission() {
  return authServiceRequest<Api.SystemManage.MenuPermission[]>({
    url: '/menu/getMenuPermission'
  });
}

/** 添加权限 */
export function fetchAddPermission(data: Api.SystemManage.PermissionEdit) {
  return authServiceRequest<Api.ApiResponse>({
    url: '/permission/addPermission',
    method: 'post',
    data
  });
}

/** 更新权限信息 */
export function fetchUpdatePermissionInfo(data: Api.SystemManage.PermissionEdit) {
  return authServiceRequest<Api.ApiResponse>({
    url: '/permission/updatePermission',
    method: 'post',
    data
  });
}

/** 获取所有权限注解 */
export function fetchGetAllPermissionAnnotations() {
  return authServiceRequest<Api.ApiResponse<Record<string, string>>>({
    url: '/permission/getAllPermissionAnnotations'
  });
}

/** 删除权限 */
export function fetchDeletePermission(id: string | number) {
  return authServiceRequest<Api.ApiResponse>({
    url: '/permission/deletePermission',
    method: 'post',
    params: { id }
  });
}

/** 获取权限列表 */
export function fetchGetPermissionList(
  params: Api.Common.CommonSearchParams & {
    menuId: string;
    name?: string | null;
  }
) {
  return authServiceRequest<Api.Common.PaginatingQueryRecord<Api.SystemManage.Permission>>({
    url: '/permission/getPermissionListByPageId',
    method: 'get',
    params
  });
}

/** 获取编辑菜单信息 */
export function fetchGetEditMenuInfo(id: string) {
  return authServiceRequest<Api.SystemManage.Menu>({
    url: '/menu/getEditMenuInfo',
    method: 'get',
    params: { id }
  });
}

/** 更新菜单信息 */
export function fetchUpdateMenuInfo(data: Api.SystemManage.Menu) {
  return authServiceRequest({
    url: '/menu/updateMenuInfo',
    method: 'post',
    data
  });
}

import { authServiceRequest } from '@/service/request/microservice';

/** 获取组织单位分页列表 */
export function fetchGetOrgUnitsPageList(
  params: Api.SystemManage.OrgUnitsSearchParams & Api.Common.CommonSearchParams
) {
  return authServiceRequest<Api.Common.PaginatingQueryRecord<Api.SystemManage.OrgUnits>>({
    url: '/sysOrgUnits/getOrgUnitsList',
    method: 'get',
    params
  });
}

/** 获取组织单位详情 */
export function fetchGetOrgUnits(id: number) {
  return authServiceRequest<Api.SystemManage.OrgUnits>({
    url: '/sysOrgUnits/getOrgUnitsById',
    method: 'get',
    params: { id }
  });
}

/** 添加组织单位 */
export function fetchAddOrgUnits(data: Api.SystemManage.OrgUnitsEdit) {
  return authServiceRequest<App.Service.Response<void>>({
    url: '/sysOrgUnits/addOrgUnits',
    method: 'post',
    data
  });
}

/** 更新组织单位 */
export function fetchUpdateOrgUnits(data: Api.SystemManage.OrgUnitsEdit) {
  return authServiceRequest<App.Service.Response<void>>({
    url: '/sysOrgUnits/updateOrgUnits',
    method: 'post',
    data
  });
}

/** 删除组织单位 */
export function fetchDeleteOrgUnits(id: number) {
  return authServiceRequest<App.Service.Response<void>>({
    url: '/sysOrgUnits/deleteOrgUnits',
    method: 'delete',
    params: { id }
  });
}

/** 批量删除组织单位 */
export function fetchDeleteOrgUnitsBatch(ids: number[]) {
  return authServiceRequest<App.Service.Response<void>>({
    url: '/sysOrgUnits/deleteOrgUnitsBatch',
    method: 'delete',
    data: ids
  });
}

/** 获取组织单位树结构 */
export function fetchGetOrgUnitsTree() {
  return authServiceRequest<Api.SystemManage.OrgUnitsTree>({
    url: '/sysOrgUnits/tree'
  });
}

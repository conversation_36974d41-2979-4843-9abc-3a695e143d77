import type { AiMessage, DeepSeekConfig, DeepSeekResponse, DeepSeekStreamChunk } from '@/types/ai';

/** DeepSeek API 客户端 */
export class DeepSeekApi {
  private config: DeepSeekConfig;

  constructor(config: DeepSeekConfig) {
    this.config = config;
  }

  /** 更新配置 */
  updateConfig(config: Partial<DeepSeekConfig>) {
    this.config = { ...this.config, ...config };
  }

  /** 发送消息（非流式） */
  async sendMessage(messages: AiMessage[]): Promise<DeepSeekResponse> {
    const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        stream: false
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API 请求失败: ${response.status} ${response.statusText} - ${errorText}`);
    }

    return response.json();
  }

  /** 处理单行流数据 */
  private static parseStreamLine(line: string): DeepSeekStreamChunk | null {
    const trimmed = line.trim();

    if (!trimmed.startsWith('data: ')) return null;

    const data = trimmed.slice(6); // 移除 "data: " 前缀

    if (data === '[DONE]') {
      return null;
    }

    try {
      const parsed = JSON.parse(data) as DeepSeekStreamChunk;
      return parsed;
    } catch {
      return null;
    }
  }

  /** 发送消息（流式） */
  async *sendMessageStream(messages: AiMessage[]): AsyncGenerator<DeepSeekStreamChunk> {
    const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        stream: true
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API 请求失败: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法获取响应流');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      // eslint-disable-next-line no-constant-condition
      while (true) {
        // eslint-disable-next-line no-await-in-loop
        const { done, value } = await reader.read();
        if (done) {
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          const chunk = DeepSeekApi.parseStreamLine(line);
          if (chunk) {
            yield chunk;
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  /** 测试连接 */
  async testConnection(): Promise<boolean> {
    try {
      const testMessage: AiMessage = {
        id: 'test',
        role: 'user',
        content: '你好',
        timestamp: Date.now()
      };

      await this.sendMessage([testMessage]);
      return true;
    } catch {
      return false;
    }
  }
}

/** 默认配置 */
export const DEFAULT_DEEPSEEK_CONFIG: DeepSeekConfig = {
  apiKey: '***********************************',
  baseUrl: 'https://api.deepseek.com',
  model: 'deepseek-chat',
  maxTokens: 4096,
  temperature: 0.7
};

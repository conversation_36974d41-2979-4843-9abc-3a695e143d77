import { jt808ServiceRequest } from '@/service/request/microservice';

/** 分页查询设备列表 */
export function fetchGetDevicePage(params: Api.Device.DeviceQueryParams) {
  return jt808ServiceRequest<Api.Common.PaginatingQueryRecord<Api.Device.DeviceVO>>({
    url: '/device/page',
    method: 'get',
    params
  });
}

/** 根据ID查询设备详情 */
export function fetchGetDeviceById(id: number) {
  return jt808ServiceRequest<Api.Device.DeviceVO>({
    url: `/device/${id}`,
    method: 'get'
  });
}

/** 添加设备 */
export function fetchAddDevice(data: Api.Device.DeviceDTO) {
  return jt808ServiceRequest<App.Service.Response<void>>({
    url: '/device',
    method: 'post',
    data
  });
}

/** 更新设备 */
export function fetchUpdateDevice(data: Api.Device.DeviceDTO) {
  return jt808ServiceRequest<App.Service.Response<void>>({
    url: '/device',
    method: 'put',
    data
  });
}

/** 删除设备 */
export function fetchDeleteDevice(id: number) {
  return jt808ServiceRequest<App.Service.Response<void>>({
    url: `/device/${id}`,
    method: 'delete'
  });
}

/** 批量删除设备 */
export function fetchBatchDeleteDevice(ids: number[]) {
  return jt808ServiceRequest<App.Service.Response<void>>({
    url: '/device/batch',
    method: 'delete',
    data: ids
  });
}

/** 更新设备状态 */
export function fetchUpdateDeviceStatus(id: number, status: number) {
  return jt808ServiceRequest<App.Service.Response<void>>({
    url: `/device/${id}/status/${status}`,
    method: 'put'
  });
}

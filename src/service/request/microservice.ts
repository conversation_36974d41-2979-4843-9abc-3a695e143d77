import type { AxiosResponse } from 'axios';
import { BACKEND_ERROR_CODE, createFlatRequest } from '@sa/axios';
import { useAuthStore } from '@/store/modules/auth';
import { createMicroserviceRequest } from '@/utils/microservice';
import { $t } from '@/locales';
import { getAuthorization, handleExpiredRequest, showErrorMsg } from './shared';
import type { RequestInstanceState } from './type';

const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y';
const microserviceRequest = createMicroserviceRequest(import.meta.env, isHttpProxy);

/**
 * 创建微服务请求实例
 *
 * @param serviceName 服务名称
 */
export function createMicroserviceRequestInstance(serviceName: string) {
  // 检查服务是否存在
  if (!microserviceRequest.hasService(serviceName)) {
    throw new Error(`Microservice "${serviceName}" not found in configuration`);
  }

  const baseURL = microserviceRequest.buildURL(serviceName, '');

  // 创建请求实例并保存引用以访问state
  const flatRequestInstance = createFlatRequest<App.Service.Response, RequestInstanceState>(
    {
      baseURL,
      headers: {
        apifoxToken: 'XL299LiMEDZ0H5h3A29PxwQXdMJqWyY2'
      }
    },
    {
      async onRequest(config) {
        const Authorization = getAuthorization();
        Object.assign(config.headers, { Authorization });

        return config;
      },
      isBackendSuccess(response) {
        return String(response.data.code) === import.meta.env.VITE_SERVICE_SUCCESS_CODE;
      },
      async onBackendFail(response, instance) {
        const authStore = useAuthStore();
        const responseCode = String(response.data.code);

        function handleLogout() {
          authStore.resetStore();
        }

        function logoutAndCleanup() {
          handleLogout();
          window.removeEventListener('beforeunload', handleLogout);

          // 使用外层实例的state而不是内部instance
          flatRequestInstance.state.errMsgStack =
            flatRequestInstance.state.errMsgStack?.filter(msg => msg !== response.data.msg) || [];
        }

        // when the backend response code is in `logoutCodes`, it means the user will be logged out and redirected to login page
        const logoutCodes = import.meta.env.VITE_SERVICE_LOGOUT_CODES?.split(',') || [];
        if (logoutCodes.includes(responseCode)) {
          handleLogout();
          return null;
        }

        // when the backend response code is in `modalLogoutCodes`, it means the user will be logged out by displaying a modal
        const modalLogoutCodes = import.meta.env.VITE_SERVICE_MODAL_LOGOUT_CODES?.split(',') || [];
        if (
          modalLogoutCodes.includes(responseCode) &&
          !flatRequestInstance.state.errMsgStack?.includes(response.data.msg)
        ) {
          flatRequestInstance.state.errMsgStack = [...(flatRequestInstance.state.errMsgStack || []), response.data.msg];

          // prevent the user from refreshing the page
          window.addEventListener('beforeunload', handleLogout);

          window.$dialog?.error({
            title: $t('common.error'),
            content: response.data.msg,
            positiveText: $t('common.confirm'),
            maskClosable: false,
            closeOnEsc: false,
            onPositiveClick() {
              logoutAndCleanup();
            },
            onClose() {
              logoutAndCleanup();
            }
          });

          return null;
        }

        // when the backend response code is in `expiredTokenCodes`, it means the token is expired, and refresh token
        const expiredTokenCodes = import.meta.env.VITE_SERVICE_EXPIRED_TOKEN_CODES?.split(',') || [];
        if (expiredTokenCodes.includes(responseCode)) {
          const success = await handleExpiredRequest(flatRequestInstance.state);
          if (success) {
            const Authorization = getAuthorization();
            Object.assign(response.config.headers, { Authorization });

            return instance.request(response.config) as Promise<AxiosResponse>;
          }
        }

        return null;
      },
      transformBackendResponse(response) {
        return response.data.data;
      },
      onError(error) {
        // when the request is fail, you can show error message

        let message = error.message;
        let backendErrorCode = '';

        // get backend error message and code
        if (error.code === BACKEND_ERROR_CODE) {
          message = error.response?.data?.msg || message;
          backendErrorCode = String(error.response?.data?.code || '');
        }

        // the error message is displayed in the modal
        const modalLogoutCodes = import.meta.env.VITE_SERVICE_MODAL_LOGOUT_CODES?.split(',') || [];
        if (modalLogoutCodes.includes(backendErrorCode)) {
          return;
        }

        // when the token is expired, refresh token and retry request, so no need to show error message
        const expiredTokenCodes = import.meta.env.VITE_SERVICE_EXPIRED_TOKEN_CODES?.split(',') || [];
        if (expiredTokenCodes.includes(backendErrorCode)) {
          return;
        }

        showErrorMsg(flatRequestInstance.state, message);
      }
    }
  );

  return flatRequestInstance;
}

/** 微服务请求工具 */
export const microserviceRequestUtils = {
  /** 获取所有可用的微服务 */
  getAvailableServices() {
    return microserviceRequest.getAvailableServices();
  },

  /** 检查服务是否存在 */
  hasService(serviceName: string) {
    return microserviceRequest.hasService(serviceName);
  },

  /** 构建微服务URL */
  buildURL(serviceName: string, path: string) {
    return microserviceRequest.buildURL(serviceName, path);
  }
};

// 预定义的微服务请求实例
export const authServiceRequest = createMicroserviceRequestInstance('auth-service');
export const jt808ServiceRequest = createMicroserviceRequestInstance('jt808-service');

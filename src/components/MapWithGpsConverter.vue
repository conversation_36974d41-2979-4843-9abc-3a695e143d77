<template>
  <div class="map-container">
    <div class="map-controls">
      <el-button @click="refreshVehicleData">刷新车辆位置</el-button>
      <el-button @click="showCoordinateInfo">显示坐标信息</el-button>
    </div>
    
    <!-- 百度地图容器 -->
    <div id="baiduMap" class="baidu-map"></div>
    
    <!-- 坐标信息显示 -->
    <el-dialog v-model="showDialog" title="坐标转换信息" width="600px">
      <div v-for="vehicle in vehicleList" :key="vehicle.id" class="coordinate-info">
        <h4>{{ vehicle.name }}</h4>
        <p><strong>原始WGS84坐标:</strong> {{ formatOriginalCoordinate(vehicle) }}</p>
        <p><strong>转换后BD09坐标:</strong> {{ formatConvertedCoordinate(vehicle) }}</p>
        <p><strong>速度:</strong> {{ vehicle.speed }} km/h</p>
        <hr />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import { CoordinateConverter } from '@/utils/coordinate-converter';
import { useGpsConverter } from '@/utils/coordinate-converter-example';

// 使用坐标转换composable
const { convertVehicleGps, formatCoordinate, calculateDistance } = useGpsConverter();

// 响应式数据
const vehicleList = ref<any[]>([]);
const showDialog = ref(false);
const map = ref<any>(null);
const markers = ref<any[]>([]);

// 模拟从后端获取的车辆GPS数据 (WGS84坐标系)
const mockVehicleData = [
  {
    id: 'vehicle001',
    name: '巡检车辆1',
    lng: 104.7436647,  // WGS84经度
    lat: 28.8150648,   // WGS84纬度
    speed: 60,
    status: 'running',
    direction: 90
  },
  {
    id: 'vehicle002',
    name: '巡检车辆2', 
    lng: 104.7536647,  // WGS84经度
    lat: 28.8250648,   // WGS84纬度
    speed: 45,
    status: 'stopped',
    direction: 180
  },
  {
    id: 'vehicle003',
    name: '巡检车辆3',
    lng: 104.7636647,  // WGS84经度
    lat: 28.8350648,   // WGS84纬度
    speed: 30,
    status: 'maintenance',
    direction: 270
  }
];

// 初始化百度地图
const initBaiduMap = () => {
  // 确保百度地图API已加载
  if (typeof BMap === 'undefined') {
    console.error('百度地图API未加载');
    return;
  }

  // 创建地图实例
  map.value = new BMap.Map('baiduMap');
  
  // 设置地图中心点 (使用转换后的BD09坐标)
  const centerPoint = CoordinateConverter.wgs84ToBd09(104.7536647, 28.8250648);
  const point = new BMap.Point(centerPoint.lng, centerPoint.lat);
  
  // 初始化地图
  map.value.centerAndZoom(point, 15);
  map.value.enableScrollWheelZoom(true);
  
  // 添加地图控件
  map.value.addControl(new BMap.NavigationControl());
  map.value.addControl(new BMap.ScaleControl());
};

// 刷新车辆数据并更新地图标记
const refreshVehicleData = async () => {
  try {
    // 模拟从API获取数据
    const rawData = [...mockVehicleData];
    
    // 转换GPS坐标
    const convertedData = convertVehicleGps(rawData);
    vehicleList.value = convertedData;
    
    // 更新地图标记
    updateMapMarkers(convertedData);
    
    console.log('车辆数据已更新:', convertedData);
  } catch (error) {
    console.error('刷新车辆数据失败:', error);
  }
};

// 更新地图标记
const updateMapMarkers = (vehicles: any[]) => {
  if (!map.value) return;
  
  // 清除现有标记
  markers.value.forEach(marker => {
    map.value.removeOverlay(marker);
  });
  markers.value = [];
  
  // 添加新标记
  vehicles.forEach(vehicle => {
    const point = new BMap.Point(vehicle.lng, vehicle.lat);
    const marker = new BMap.Marker(point);
    
    // 创建信息窗口
    const infoWindow = new BMap.InfoWindow(`
      <div>
        <h4>${vehicle.name}</h4>
        <p>速度: ${vehicle.speed} km/h</p>
        <p>状态: ${getStatusText(vehicle.status)}</p>
        <p>坐标: ${formatCoordinate(vehicle.lng, vehicle.lat)}</p>
        <p>原始坐标: ${formatCoordinate(vehicle.originalLng, vehicle.originalLat)}</p>
      </div>
    `);
    
    // 添加点击事件
    marker.addEventListener('click', () => {
      map.value.openInfoWindow(infoWindow, point);
    });
    
    // 设置标记图标（根据车辆状态）
    const icon = new BMap.Icon(
      getVehicleIcon(vehicle.status),
      new BMap.Size(32, 32)
    );
    marker.setIcon(icon);
    
    // 添加到地图
    map.value.addOverlay(marker);
    markers.value.push(marker);
  });
};

// 获取车辆状态文本
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    running: '运行中',
    stopped: '已停止', 
    maintenance: '维护中'
  };
  return statusMap[status] || '未知';
};

// 获取车辆图标
const getVehicleIcon = (status: string): string => {
  const iconMap: Record<string, string> = {
    running: '/icons/vehicle-running.png',
    stopped: '/icons/vehicle-stopped.png',
    maintenance: '/icons/vehicle-maintenance.png'
  };
  return iconMap[status] || '/icons/vehicle-default.png';
};

// 显示坐标信息对话框
const showCoordinateInfo = () => {
  showDialog.value = true;
};

// 格式化原始坐标显示
const formatOriginalCoordinate = (vehicle: any): string => {
  if (vehicle.originalLng && vehicle.originalLat) {
    return formatCoordinate(vehicle.originalLng, vehicle.originalLat);
  }
  return '无原始坐标';
};

// 格式化转换后坐标显示
const formatConvertedCoordinate = (vehicle: any): string => {
  return formatCoordinate(vehicle.lng, vehicle.lat);
};

// WebSocket连接处理GPS实时数据
const handleWebSocketMessage = (message: any) => {
  try {
    const data = JSON.parse(message.data);
    
    // 验证并转换GPS坐标
    if (data.type === 'gps_update' && data.vehicleId) {
      const convertedPoint = CoordinateConverter.convertGpsForMap({
        lng: data.longitude,
        lat: data.latitude
      });
      
      // 更新对应车辆的位置
      const vehicleIndex = vehicleList.value.findIndex(v => v.id === data.vehicleId);
      if (vehicleIndex !== -1) {
        vehicleList.value[vehicleIndex] = {
          ...vehicleList.value[vehicleIndex],
          lng: convertedPoint.lng,
          lat: convertedPoint.lat,
          originalLng: data.longitude,
          originalLat: data.latitude,
          speed: data.speed,
          direction: data.direction,
          timestamp: data.timestamp
        };
        
        // 更新地图标记
        updateMapMarkers(vehicleList.value);
      }
    }
  } catch (error) {
    console.error('处理WebSocket消息失败:', error);
  }
};

// 组件挂载时初始化
onMounted(async () => {
  await nextTick();
  initBaiduMap();
  refreshVehicleData();
  
  // 这里可以初始化WebSocket连接
  // const ws = new WebSocket('ws://your-websocket-url');
  // ws.onmessage = handleWebSocketMessage;
});
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 600px;
  position: relative;
}

.map-controls {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1000;
  display: flex;
  gap: 10px;
}

.baidu-map {
  width: 100%;
  height: 100%;
}

.coordinate-info {
  margin-bottom: 20px;
}

.coordinate-info h4 {
  color: #409eff;
  margin-bottom: 10px;
}

.coordinate-info p {
  margin: 5px 0;
  line-height: 1.5;
}

.coordinate-info hr {
  margin: 15px 0;
  border: none;
  border-top: 1px solid #eee;
}
</style>

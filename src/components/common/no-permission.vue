<script setup lang="ts">
import { computed } from 'vue';
import { useThemeStore } from '@/store/modules/theme';
import { useRouterPush } from '@/hooks/common/router';
import { $t } from '@/locales';

defineOptions({ name: 'NoPermission' });

interface Props {
  /** 描述文本 */
  description?: string;
  /** 是否显示返回按钮 */
  showBackButton?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  description: '',
  showBackButton: true
});

const themeStore = useThemeStore();
const { routerPushByKey } = useRouterPush();

const displayDescription = computed(() => {
  return props.description || $t('common.noPermission');
});

const isDark = computed(() => themeStore.darkMode);

function handleBackHome() {
  routerPushByKey('home');
}
</script>

<template>
  <div class="no-permission-container" :class="{ 'dark-theme': isDark }">
    <div class="no-permission-content">
      <!-- 图标区域 -->
      <div class="icon-wrapper">
        <div class="icon-bg">
          <!-- 使用更友好的锁定图标 -->
          <svg class="icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9V6zm9 14H6V10h12v10zm-6-3c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z"
              fill="currentColor"
            />
          </svg>
        </div>
      </div>

      <!-- 文本内容 -->
      <div class="text-content">
        <h3 class="title">{{ $t('common.noPermission') }}</h3>
        <p class="description">{{ displayDescription }}</p>
      </div>

      <!-- 操作按钮 -->
      <div v-if="showBackButton" class="action-buttons">
        <NButton type="primary" @click="handleBackHome">
          <template #icon>
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" fill="currentColor" />
            </svg>
          </template>
          {{ $t('common.backToHome') }}
        </NButton>
      </div>
    </div>
  </div>
</template>

<style lang="postcss" scoped>
.no-permission-container {
  @apply h-full flex items-center justify-center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 浅色主题背景 */
.no-permission-container {
  background: linear-gradient(
    135deg,
    var(--primary-color, #18a058) 0%,
    var(--primary-color-hover, #36ad6a) 50%,
    var(--primary-color-pressed, #0c7a43) 100%
  );
}

/* 深色主题背景 */
.no-permission-container.dark-theme {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
}

.no-permission-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.6;
}

/* 浅色主题装饰 */
.no-permission-container::before {
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
}

/* 深色主题装饰 */
.no-permission-container.dark-theme::before {
  background:
    radial-gradient(circle at 20% 80%, rgba(99, 226, 183, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(99, 226, 183, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(99, 226, 183, 0.05) 0%, transparent 50%);
}

.no-permission-content {
  @apply relative z-10 text-center;
  max-width: 420px;
  padding: 3rem 2.5rem;
  backdrop-filter: blur(20px);
  border-radius: 20px;
  animation: fadeInUp 0.6s ease-out;
  transition: all 0.3s ease;
}

/* 浅色主题卡片 */
.no-permission-content {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 32px rgba(0, 0, 0, 0.05);
}

/* 深色主题卡片 */
.dark-theme .no-permission-content {
  background: rgba(45, 45, 45, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 8px 32px rgba(0, 0, 0, 0.2);
}

.icon-wrapper {
  @apply mb-8;
}

.icon-bg {
  @apply w-24 h-24 mx-auto rounded-full flex items-center justify-center;
  animation: float 3s ease-in-out infinite;
  transition: all 0.3s ease;
}

/* 浅色主题图标背景 */
.icon-bg {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* 深色主题图标背景 */
.dark-theme .icon-bg {
  background: linear-gradient(135deg, rgba(99, 226, 183, 0.2), rgba(99, 226, 183, 0.1));
  box-shadow:
    0 8px 32px rgba(99, 226, 183, 0.2),
    inset 0 1px 0 rgba(99, 226, 183, 0.3);
}

.icon {
  @apply w-12 h-12;
  transition: all 0.3s ease;
}

/* 浅色主题图标颜色 */
.icon {
  color: var(--primary-color, #18a058);
}

/* 深色主题图标颜色 */
.dark-theme .icon {
  color: #63e2b7;
}

.text-content {
  @apply mb-8;
}

.title {
  @apply text-2xl font-bold mb-4;
  letter-spacing: -0.025em;
  transition: color 0.3s ease;
}

/* 浅色主题标题颜色 */
.title {
  color: #2d3748;
}

/* 深色主题标题颜色 */
.dark-theme .title {
  color: #f7fafc;
}

.description {
  @apply leading-relaxed;
  font-size: 0.95rem;
  transition: color 0.3s ease;
}

/* 浅色主题描述颜色 */
.description {
  color: #4a5568;
}

/* 深色主题描述颜色 */
.dark-theme .description {
  color: #a0aec0;
}

.action-buttons {
  @apply flex justify-center;
}

.action-buttons :deep(.n-button) {
  @apply px-8 py-3 rounded-full font-medium;
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.action-buttons :deep(.n-button:hover) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.action-buttons :deep(.n-button .n-button__icon) {
  @apply w-4 h-4;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .no-permission-content {
    margin: 1rem;
    padding: 2rem 1.5rem;
  }

  .title {
    @apply text-xl;
  }

  .description {
    font-size: 0.9rem;
  }
}
</style>

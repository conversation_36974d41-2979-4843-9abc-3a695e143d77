<script setup lang="ts">
import { reactive, ref } from 'vue';
import {
  type FormInst,
  type FormRules,
  NButton,
  NForm,
  NFormItem,
  NGi,
  NGrid,
  NInput,
  NInputNumber,
  NSelect
} from 'naive-ui';
import { useI18n } from 'vue-i18n';
import type { DeepSeekConfig } from '@/types/ai';
import { DEEPSEEK_MODELS } from '@/types/ai';

interface Props {
  config: DeepSeekConfig;
  isTesting?: boolean;
}

interface Emits {
  save: [config: DeepSeekConfig];
  test: [];
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const { t } = useI18n();

const formRef = ref<FormInst>();

const formData = reactive<DeepSeekConfig>({
  apiKey: props.config.apiKey,
  baseUrl: props.config.baseUrl,
  model: props.config.model,
  maxTokens: props.config.maxTokens,
  temperature: props.config.temperature
});

const modelOptions = Object.entries(DEEPSEEK_MODELS).map(([value, config]) => ({
  value,
  label: config.name,
  description: config.description
}));

const rules: FormRules = {
  apiKey: {
    required: true,
    message: t('ai.config.validation.apiKeyRequired'),
    trigger: 'blur'
  },
  baseUrl: {
    required: true,
    message: t('ai.config.validation.apiUrlRequired'),
    trigger: 'blur'
  },
  model: {
    required: true,
    message: t('ai.config.validation.modelRequired'),
    trigger: 'change'
  },
  maxTokens: {
    required: true,
    type: 'number',
    min: 1,
    max: 8192,
    message: t('ai.config.validation.maxTokensRange')
  },
  temperature: {
    required: true,
    type: 'number',
    min: 0,
    max: 2,
    message: t('ai.config.validation.temperatureRange')
  }
};

const handleTest = () => {
  emit('test');
};

const handleSave = () => {
  formRef.value?.validate(errors => {
    if (!errors) {
      emit('save', { ...formData });
    }
  });
};
</script>

<template>
  <div class="form-container">
    <NForm ref="formRef" :model="formData" :rules="rules" label-placement="top" class="config-form">
      <NFormItem :label="t('ai.config.apiKey')" path="apiKey">
        <NInput
          v-model:value="formData.apiKey"
          type="password"
          :placeholder="t('ai.config.apiKeyPlaceholder')"
          show-password-on="click"
        />
      </NFormItem>

      <NFormItem :label="t('ai.config.apiUrl')" path="baseUrl">
        <NInput v-model:value="formData.baseUrl" placeholder="https://api.deepseek.com" />
      </NFormItem>

      <NFormItem :label="t('ai.config.model')" path="model">
        <NSelect
          v-model:value="formData.model"
          :options="modelOptions"
          :placeholder="t('ai.config.modelPlaceholder')"
        />
      </NFormItem>

      <NGrid :cols="2" :x-gap="12">
        <NGi>
          <NFormItem :label="t('ai.config.maxTokens')" path="maxTokens">
            <NInputNumber v-model:value="formData.maxTokens" :min="1" :max="8192" class="full-width" />
          </NFormItem>
        </NGi>

        <NGi>
          <NFormItem :label="t('ai.config.temperature')" path="temperature">
            <NInputNumber v-model:value="formData.temperature" :min="0" :max="2" :step="0.1" class="full-width" />
          </NFormItem>
        </NGi>
      </NGrid>

      <div class="form-actions">
        <NButton type="primary" :loading="isTesting" @click="handleTest">
          <template #icon>
            <i class="i-carbon-wifi" />
          </template>
          {{ t('ai.config.testConnection') }}
        </NButton>

        <NButton type="primary" @click="handleSave">
          <template #icon>
            <i class="i-carbon-save" />
          </template>
          {{ t('ai.config.saveConfig') }}
        </NButton>
      </div>
    </NForm>
  </div>
</template>

<style>
/* 全局样式，不使用 scoped */
.n-modal-container {
  max-width: 400px !important;
  width: 400px !important;
}

/* 支持项目中使用的w-400px类名 */
.w-400px {
  width: 400px !important;
}
</style>

<style scoped>
.form-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.config-form {
  padding: 8px 0;
  width: 100%;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  justify-content: center;
}

.full-width {
  width: 100%;
}

/* 确保输入框适应容器宽度 */
:deep(.n-input),
:deep(.n-input-wrapper),
:deep(.n-select),
:deep(.n-base-selection) {
  width: 100%;
}
</style>

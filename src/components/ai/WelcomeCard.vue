<script setup lang="ts">
import { NCard } from 'naive-ui';
import { useI18n } from 'vue-i18n';

interface Emits {
  example: [prompt: string];
  startChat: [];
}

const emit = defineEmits<Emits>();
const { t } = useI18n();

const handleStartChat = () => {
  emit('startChat');
};

const handleExample = (prompt: string) => {
  emit('example', prompt);
};
</script>

<template>
  <div class="welcome-container">
    <NCard class="ai-welcome" :bordered="false">
      <div class="welcome-header">
        <img
          class="welcome-icon"
          src="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp"
          :alt="t('ai.chat.title')"
        />
        <h2 class="welcome-title">{{ t('ai.welcome.title') }}</h2>
        <p class="welcome-description">
          {{ t('ai.welcome.description') }}
        </p>
      </div>

      <div class="welcome-actions">
        <div class="quick-prompts">
          <h4>{{ t('ai.welcome.quickStart') }}</h4>
          <div class="prompt-buttons">
            <button class="prompt-btn primary-btn" @click="handleStartChat">
              <i class="i-carbon-chat-launch mr-2 text-xl"></i>
              {{ t('ai.welcome.startChat') }}
            </button>
            <button class="prompt-btn" @click="handleExample(t('ai.welcome.examplePrompts.learnFeatures'))">
              🤖 {{ t('ai.welcome.learnFeatures') }}
            </button>
            <button class="prompt-btn" @click="handleExample(t('ai.welcome.examplePrompts.analyzeProblems'))">
              🔍 {{ t('ai.welcome.analyzeProblems') }}
            </button>
            <button class="prompt-btn" @click="handleExample(t('ai.welcome.examplePrompts.getAdvice'))">
              💡 {{ t('ai.welcome.getAdvice') }}
            </button>
          </div>
        </div>

        <div class="features">
          <h4>{{ t('ai.welcome.features') }}</h4>
          <ul class="feature-list">
            <li>🎯 {{ t('ai.welcome.featureList.smartQA') }}</li>
            <li>📝 {{ t('ai.welcome.featureList.docAnalysis') }}</li>
            <li>💼 {{ t('ai.welcome.featureList.efficiency') }}</li>
            <li>🔄 {{ t('ai.welcome.featureList.streaming') }}</li>
          </ul>
        </div>
      </div>
    </NCard>
  </div>
</template>

<style scoped>
.welcome-container {
  padding: 30px 30px 30px 30px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: auto;
  background: linear-gradient(135deg, rgb(var(--layout-bg-color)) 0%, rgba(var(--base-text-color), 0.05) 100%);
  height: auto;
  overflow-y: auto;
}

.ai-welcome {
  max-width: 800px;
  background: rgba(var(--container-bg-color), 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(var(--base-text-color), 0.1);
  width: 100%;
}

.welcome-header {
  text-align: center;
  margin-bottom: 20px;
}

.welcome-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 12px;
}

.welcome-title {
  font-size: 24px;
  font-weight: 700;
  color: rgb(var(--base-text-color));
  margin: 0 0 12px 0;
}

.welcome-description {
  font-size: 16px;
  color: rgba(var(--base-text-color), 0.7);
  line-height: 1.6;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
}

.welcome-actions {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.quick-prompts h4,
.features h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: rgb(var(--base-text-color));
}

.prompt-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.prompt-btn {
  padding: 10px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3);
}

.primary-btn {
  background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
  box-shadow: 0 3px 8px rgba(79, 70, 229, 0.4);
  font-size: 15px;
  font-weight: 600;
  padding: 12px 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.prompt-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.primary-btn:hover {
  background: linear-gradient(135deg, #4338ca 0%, #312e81 100%);
  box-shadow: 0 8px 20px rgba(79, 70, 229, 0.5);
  transform: translateY(-3px);
}

.prompt-btn:active {
  transform: translateY(0);
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.feature-list li {
  padding: 8px 0;
  font-size: 14px;
  color: rgba(var(--base-text-color), 0.7);
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-container {
    padding: 15px;
  }

  .welcome-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 8px;
  }

  .welcome-title {
    font-size: 20px;
    margin: 0 0 8px 0;
  }

  .welcome-description {
    font-size: 14px;
  }

  .welcome-actions {
    margin-top: 16px;
    gap: 16px;
  }

  .quick-prompts h4,
  .features h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
  }

  .prompt-buttons {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .prompt-btn {
    padding: 8px 14px;
    font-size: 13px;
  }

  .primary-btn {
    padding: 10px 16px;
    font-size: 14px;
  }

  .feature-list {
    grid-template-columns: 1fr;
    gap: 4px;
  }

  .feature-list li {
    padding: 4px 0;
    font-size: 13px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .welcome-container {
    padding: 10px;
  }

  .ai-welcome {
    border-radius: 12px;
  }

  .welcome-header {
    margin-bottom: 12px;
  }

  .welcome-actions {
    margin-top: 12px;
    gap: 12px;
  }
}
</style>

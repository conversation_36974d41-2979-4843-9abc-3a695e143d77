<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, ref, watchEffect } from 'vue';
import { useRoute } from 'vue-router';
import { NBadge, NButton, NModal, useMessage } from 'naive-ui';
import { useI18n } from 'vue-i18n';
import { useAiStore } from '@/store/modules/ai';
import { useAuthStore } from '@/store/modules/auth';
import type { DeepSeekConfig } from '@/types/ai';
import MessageList from './MessageList.vue';
import MessageInput from './MessageInput.vue';
import ConfigForm from './ConfigForm.vue';
import WelcomeCard from './WelcomeCard.vue';

const aiStore = useAiStore();
const authStore = useAuthStore();
const route = useRoute();
const message = useMessage();
const { t } = useI18n();

const containerRef = ref<HTMLElement>();
const messageInputRef = ref<InstanceType<typeof MessageInput>>();
const showConfigModal = ref(false);
const isTestingConnection = ref(false);

// 位置管理和拖动功能
const position = ref({ x: window.innerWidth - 520, y: 80 });
const isDragging = ref(false);
const dragOffset = ref({ x: 0, y: 0 });

const containerStyle = computed(() => ({
  position: 'fixed' as const,
  left: `${position.value.x}px`,
  top: `${position.value.y}px`,
  zIndex: 9999,
  cursor: isDragging.value ? 'grabbing' : 'default'
}));

// 拖动中
const handleDragMove = (e: MouseEvent) => {
  if (!isDragging.value) return;

  const newX = e.clientX - dragOffset.value.x;
  const newY = e.clientY - dragOffset.value.y;

  // 限制在窗口内
  const maxX = window.innerWidth - 500; // 悬浮窗宽度
  const maxY = window.innerHeight - 100; // 给底部留一些空间

  position.value = {
    x: Math.max(0, Math.min(maxX, newX)),
    y: Math.max(0, Math.min(maxY, newY))
  };
};

// 拖动结束
const handleDragEnd = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', handleDragMove);
  document.removeEventListener('mouseup', handleDragEnd);

  // 保存位置到localStorage
  localStorage.setItem('ai-assistant-position', JSON.stringify(position.value));
};

// 拖动开始
const handleDragStart = (e: MouseEvent) => {
  if (e.target && (e.target as HTMLElement).closest('.header-actions')) {
    // 如果点击的是操作按钮区域，不触发拖动
    return;
  }

  isDragging.value = true;
  const rect = containerRef.value?.getBoundingClientRect();
  if (rect) {
    dragOffset.value = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };
  }

  document.addEventListener('mousemove', handleDragMove);
  document.addEventListener('mouseup', handleDragEnd);

  // 防止文本选择
  e.preventDefault();
};

// 加载保存的位置
const loadPosition = () => {
  const saved = localStorage.getItem('ai-assistant-position');
  if (saved) {
    const parsedPosition = JSON.parse(saved);
    // 确保位置在屏幕范围内
    const maxX = window.innerWidth - 500;
    const maxY = window.innerHeight - 100;

    position.value = {
      x: Math.max(0, Math.min(maxX, parsedPosition.x)),
      y: Math.max(0, Math.min(maxY, parsedPosition.y))
    };
  }
};

// 测试连接
const handleTestConnection = async () => {
  isTestingConnection.value = true;
  try {
    const success = await aiStore.testConnection();
    if (success) {
      message.success(t('ai.chat.connectionSuccess'));
    } else {
      message.error(t('ai.chat.connectionFailed'));
    }
  } finally {
    isTestingConnection.value = false;
  }
};

// 发送消息
const handleSendMessage = async (content: string) => {
  if (!aiStore.canSendMessage) {
    return;
  }

  await aiStore.sendMessage(content);
};

// 保存配置
const handleSaveConfig = async (newConfig: DeepSeekConfig) => {
  aiStore.updateConfig(newConfig);
  showConfigModal.value = false;
  message.success(t('ai.chat.configSaved'));

  // 如果有 API Key，尝试连接
  if (newConfig.apiKey) {
    await handleTestConnection();
  }
};

// 键盘快捷键
const handleGlobalKeydown = (e: KeyboardEvent) => {
  // Ctrl/Cmd + K 切换显示
  if ((e.ctrlKey || e.metaKey) && e.key === 'k' && !e.shiftKey) {
    e.preventDefault();
    aiStore.toggleVisibility();

    // 如果打开并且有API Key，聚焦输入框
    if (aiStore.isVisible && !aiStore.isMinimized && aiStore.hasApiKey) {
      nextTick(() => {
        messageInputRef.value?.focus();
      });
    }
  }
};

// 处理开始聊天
const handleStartChat = () => {
  // 添加一条初始欢迎消息
  aiStore.messages = [
    {
      id: `welcome-${Date.now()}`,
      role: 'assistant',
      content: t('ai.chat.welcomeMessage'),
      timestamp: Date.now()
    }
  ];
  message.success(t('ai.chat.chatStarted'));

  // 聚焦输入框
  nextTick(() => {
    messageInputRef.value?.focus();
  });
};

// 窗口大小改变时调整位置
const handleWindowResize = () => {
  const maxX = window.innerWidth - 500;
  const maxY = window.innerHeight - 100;

  position.value = {
    x: Math.max(0, Math.min(maxX, position.value.x)),
    y: Math.max(0, Math.min(maxY, position.value.y))
  };
};

// 计算是否应该显示AI组件
const shouldShowAi = computed(() => {
  // 用户必须已登录
  if (!authStore.isLogin) {
    return false;
  }

  // 当前页面不应该是公共页面（登录、404、500等）
  if (route.meta.constant === true) {
    return false;
  }

  return true;
});

// 初始化AI功能的函数
const initializeAi = () => {
  // 加载配置
  aiStore.loadConfig();

  // 加载保存的位置
  loadPosition();

  // 预设 API Key（如果没有的话）
  if (!aiStore.hasApiKey) {
    aiStore.updateConfig({
      apiKey: 'sk-8ff0f0dd8ba948d4ae31ef7a473b647d',
      baseUrl: 'https://api.deepseek.com',
      model: 'deepseek-chat',
      maxTokens: 4096,
      temperature: 0.7
    });
  }

  // 如果有API Key，自动测试连接
  if (aiStore.hasApiKey) {
    handleTestConnection();
  }

  // 注册全局快捷键和事件
  document.addEventListener('keydown', handleGlobalKeydown);
  window.addEventListener('resize', handleWindowResize);
};

// 标记是否已初始化
const isInitialized = ref(false);

// 监听shouldShowAi的变化，动态初始化AI功能
watchEffect(() => {
  if (shouldShowAi.value && !isInitialized.value) {
    initializeAi();
    isInitialized.value = true;
  }
});

onMounted(() => {
  // 初始化逻辑已移至watchEffect中处理
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleGlobalKeydown);
  window.removeEventListener('resize', handleWindowResize);

  // 清理拖动事件
  document.removeEventListener('mousemove', handleDragMove);
  document.removeEventListener('mouseup', handleDragEnd);
});
</script>

<template>
  <Teleport to="body">
    <Transition name="ai-assistant" appear>
      <div
        v-if="shouldShowAi && aiStore.isVisible"
        ref="containerRef"
        class="floating-ai-assistant"
        :class="{ minimized: aiStore.isMinimized }"
        :style="containerStyle"
      >
        <!-- 标题栏 -->
        <div class="assistant-header" @mousedown="handleDragStart">
          <div class="header-left">
            <i class="i-carbon-watson-health text-lg text-green-500" />
            <span class="title">{{ t('ai.chat.title') }}</span>
            <NBadge :type="aiStore.isConnected ? 'success' : 'error'" :show="true" size="small" dot />
          </div>

          <div class="header-actions">
            <NButton v-if="aiStore.messages.length > 0" size="small" class="header-btn" @click="aiStore.clearMessages">
              {{ t('ai.chat.newChat') }}
            </NButton>

            <NButton size="small" class="header-btn" @click="showConfigModal = true">
              {{ t('ai.chat.config') }}
            </NButton>

            <NButton size="small" class="header-btn" @click="aiStore.toggleMinimize">
              {{ aiStore.isMinimized ? t('ai.chat.maximize') : t('ai.chat.minimize') }}
            </NButton>

            <NButton size="small" class="header-btn" @click="aiStore.toggleVisibility">
              {{ t('ai.chat.close') }}
            </NButton>
          </div>
        </div>

        <!-- 主体内容 -->
        <div v-if="!aiStore.isMinimized" class="assistant-body">
          <!-- 配置提示 -->
          <div v-if="!aiStore.hasApiKey" class="config-prompt">
            <i class="i-carbon-warning text-2xl text-orange-500" />
            <p class="mt-2 text-center text-sm text-gray-600">{{ t('ai.chat.configureDescription') }}</p>
            <NButton type="primary" size="small" class="mt-3" @click="showConfigModal = true">
              {{ t('ai.chat.configureNow') }}
            </NButton>
          </div>

          <!-- 聊天界面 -->
          <template v-else>
            <!-- 欢迎页面 -->
            <WelcomeCard
              v-if="aiStore.messages.length === 0"
              class="floating-welcome"
              @example="handleSendMessage"
              @start-chat="handleStartChat"
            />

            <!-- 消息列表 -->
            <MessageList v-else :messages="aiStore.messages" class="floating-messages" />

            <MessageInput
              ref="messageInputRef"
              :loading="aiStore.isLoading"
              :disabled="!aiStore.canSendMessage"
              :placeholder="t('ai.chat.inputPlaceholder')"
              @send="handleSendMessage"
            />
          </template>
        </div>

        <!-- 配置弹窗 -->
        <NModal
          v-model:show="showConfigModal"
          :mask-closable="false"
          preset="card"
          :title="t('ai.config.title')"
          class="config-modal floating-modal-width"
        >
          <ConfigForm
            :config="aiStore.config"
            :is-testing="isTestingConnection"
            @save="handleSaveConfig"
            @test="handleTestConnection"
          />
        </NModal>
      </div>
    </Transition>
  </Teleport>
</template>

<style scoped>
.floating-ai-assistant {
  width: 500px;
  height: 600px;
  background: linear-gradient(135deg, rgb(var(--container-bg-color)) 0%, rgba(var(--base-text-color), 0.02) 100%);
  border: 1px solid rgba(var(--base-text-color), 0.15);
  border-radius: 16px;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(var(--base-text-color), 0.05);
  backdrop-filter: blur(20px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  user-select: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-ai-assistant:hover {
  box-shadow:
    0 25px 35px -5px rgba(0, 0, 0, 0.15),
    0 15px 15px -5px rgba(0, 0, 0, 0.08),
    0 0 0 1px rgba(var(--base-text-color), 0.08);
  transform: translateY(-2px);
}

.floating-ai-assistant.minimized {
  height: auto;
}

.assistant-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
  cursor: grab;
  user-select: none;
  border-radius: 16px 16px 0 0;
  color: white;
  transition: background 0.2s ease;
}

.assistant-header:active {
  cursor: grabbing;
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
}

.header-left .title {
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-actions :deep(.n-button) {
  color: #1a1a1a !important;
  border: 2px solid #ffffff !important;
  background: #ffffff !important;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(0, 0, 0, 0.1) !important;
  min-width: 36px !important;
  height: 36px !important;
  position: relative !important;
  border-radius: 6px !important;
}

.header-actions :deep(.n-button:hover) {
  background: #f0f0f0 !important;
  border-color: #ffffff !important;
  transform: scale(1.05);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(0, 0, 0, 0.2) !important;
}

.header-actions :deep(.n-button:active) {
  background: #e0e0e0 !important;
  transform: scale(0.95);
}

.header-actions :deep(.n-button .n-button__icon) {
  color: #333333 !important;
  font-weight: bold !important;
  font-size: 16px !important;
}

.header-actions :deep(.n-button .n-button__content) {
  color: #333333 !important;
  font-weight: 700 !important;
  font-size: 12px !important;
}

/* 按钮强化样式 - 确保可见性 */
.header-actions :deep(.n-button),
.header-actions .n-button {
  background: #ffffff !important;
  color: #333333 !important;
  border: 2px solid #ffffff !important;
}

.header-btn {
  background: #ffffff !important;
  color: #333 !important;
  border: 2px solid #fff !important;
}

.assistant-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  background: rgb(var(--container-bg-color));
}

.config-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  background: linear-gradient(135deg, rgba(var(--base-text-color), 0.05) 0%, rgba(var(--base-text-color), 0.1) 100%);
  margin: 20px;
  border-radius: 12px;
  border: 2px dashed rgba(var(--base-text-color), 0.2);
}

.config-prompt i {
  margin-bottom: 12px;
}

.config-prompt p {
  margin: 8px 0 16px 0;
  color: rgba(var(--base-text-color), 0.7);
  line-height: 1.6;
}

.ai-assistant-enter-active,
.ai-assistant-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ai-assistant-enter-from {
  opacity: 0;
  transform: scale(0.95) translateY(10px);
}

.ai-assistant-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(-10px);
}

.config-modal {
  border-radius: 16px;
  overflow: hidden;
}

.config-modal .n-card {
  border-radius: 16px;
}

/* 响应式 */
@media (max-width: 768px) {
  .floating-ai-assistant {
    width: calc(100vw - 32px);
    height: calc(100vh - 100px);
    left: 16px !important;
    top: 50px !important;
  }
}

/* 浮动窗口内的欢迎组件样式 */
.floating-welcome :deep(.welcome-container) {
  padding: 20px;
  min-height: 300px;
  background: linear-gradient(135deg, rgba(var(--base-text-color), 0.02) 0%, rgb(var(--container-bg-color)) 100%);
}

.floating-welcome :deep(.ai-welcome) {
  max-width: none;
  box-shadow: none;
  border: none;
  background: transparent;
}

.floating-welcome :deep(.prompt-buttons) {
  grid-template-columns: 1fr;
  gap: 8px;
}

.floating-welcome :deep(.prompt-btn) {
  padding: 8px 16px;
  font-size: 13px;
}

.floating-welcome :deep(.feature-list) {
  grid-template-columns: 1fr;
}

/* 浮动窗口内的消息列表样式 */
.floating-messages {
  flex: 1;
  min-height: 0;
}

.floating-messages :deep(.messages-wrapper) {
  padding: 15px;
  max-width: none;
}

.floating-messages :deep(.message-bubble) {
  max-width: 85%;
}

.floating-messages :deep(.message-text) {
  font-size: 13px;
}

/* 浮动窗口内的发送器样式 */
.floating-ai-assistant :deep(.message-sender-container) {
  padding: 15px;
  border-radius: 0;
}

.floating-ai-assistant :deep(.ai-sender) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.floating-ai-assistant :deep(.ai-sender .ant-sender-input) {
  padding: 12px 16px;
  font-size: 13px;
  min-height: 40px;
}

.floating-modal-width {
  width: 500px;
}
</style>

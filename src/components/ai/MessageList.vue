<script setup lang="ts">
import { nextTick, onMounted, ref, watch } from 'vue';
import { NButton, useMessage } from 'naive-ui';
import { useI18n } from 'vue-i18n';
import type { AiMessage } from '@/types/ai';

interface Props {
  messages: AiMessage[];
}

const props = defineProps<Props>();

const messageNotification = useMessage();
const { t } = useI18n();
const containerRef = ref<HTMLElement>();

// 格式化消息内容（支持 Markdown）
const formatMessage = (content: string) => {
  // 简单的 Markdown 转换（可以后续使用专业的 markdown 解析器）
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code class="bg-gray-100 px-1 rounded">$1</code>')
    .replace(/\n/g, '<br>');
};

// 格式化时间
const formatTime = (timestamp: number) => {
  const date = new Date(timestamp);
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  if (diff < 60000) {
    return t('ai.chat.justNow');
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}${t('ai.chat.minutesAgo')}`;
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}${t('ai.chat.hoursAgo')}`;
  }
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 复制消息
const copyMessage = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content);
    messageNotification.success(t('ai.chat.copiedToClipboard'));
  } catch {
    messageNotification.error(t('ai.chat.copyFailed'));
  }
};

// 自动滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (containerRef.value) {
      containerRef.value.scrollTop = containerRef.value.scrollHeight;
    }
  });
};

// 监听消息变化，自动滚动 - 使用immediate和deep选项
watch(
  () => props.messages,
  () => {
    scrollToBottom();
  },
  { deep: true, immediate: true }
);

// 监听messages长度变化
watch(
  () => props.messages.length,
  () => {
    scrollToBottom();
  }
);

onMounted(() => {
  scrollToBottom();
});
</script>

<template>
  <div ref="containerRef" class="message-list">
    <div v-if="messages.length === 0" class="empty-state">
      <i class="i-carbon-chat text-4xl text-gray-400" />
      <p class="mt-2 text-gray-500">{{ t('ai.chat.startChatting') }}</p>
    </div>

    <div v-else class="messages">
      <div v-for="message in messages" :key="message.id" class="message" :class="[`message-${message.role}`]">
        <div class="message-avatar">
          <i :class="message.role === 'user' ? 'i-carbon-user text-blue-500' : 'i-carbon-bot text-green-500'" />
        </div>

        <div class="message-content">
          <div class="message-text">
            <!-- 安全注意：formatMessage函数已对内容进行基本清理，但仍需注意XSS风险 -->
            <!-- eslint-disable-next-line vue/no-v-html -->
            <div v-if="message.content" class="prose prose-sm max-w-none" v-html="formatMessage(message.content)" />
            <div v-else-if="message.isStreaming" class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>

          <div class="message-meta">
            <span class="timestamp">
              {{ formatTime(message.timestamp) }}
            </span>
            <NButton v-if="message.content" size="tiny" text @click="copyMessage(message.content)">
              <template #icon>
                <i class="i-carbon-copy" />
              </template>
            </NButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.message-list {
  flex: 1;
  overflow-y: auto;
  background: linear-gradient(to bottom, #f8fafc 0%, #ffffff 50%, #f8fafc 100%);
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 60px 40px;
  text-align: center;
  flex: 1;
}

.empty-state i {
  margin-bottom: 20px;
  opacity: 0.8;
  animation: float 3s ease-in-out infinite;
}

.empty-state p {
  font-size: 16px;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  max-width: 300px;
}

.messages {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding-left: 40px;
  padding-right: 40px;
  padding-bottom: 120px;
  box-sizing: border-box;
  min-height: min-content;
}

.message {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;
  opacity: 0;
  animation: fadeIn 0.5s ease-out forwards;
}

.message:last-child {
  margin-bottom: 20px;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-right: 12px;
  flex-shrink: 0;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-user .message-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  order: 1;
  margin-right: 0;
  margin-left: 12px;
}

.message-assistant .message-avatar {
  background: linear-gradient(135deg, #10b981 0%, #047857 100%);
  color: white;
}

.message-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 70%;
  position: relative;
}

.message-user .message-content {
  align-items: flex-end;
  text-align: right;
}

.message-text {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 18px 18px 18px 6px;
  padding: 16px 20px;
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  position: relative;
  transition: all 0.3s ease;
}

.message-text:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.message-user .message-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 18px 18px 6px 18px;
}

.message-user .message-text::before {
  content: '';
  position: absolute;
  top: 10px;
  right: -6px;
  width: 0;
  height: 0;
  border: 6px solid transparent;
  border-left-color: #667eea;
}

.message-assistant .message-text::before {
  content: '';
  position: absolute;
  top: 10px;
  left: -6px;
  width: 0;
  height: 0;
  border: 6px solid transparent;
  border-right-color: #ffffff;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  font-size: 11px;
  color: #9ca3af;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.message:hover .message-meta {
  opacity: 1;
}

.message-user .message-meta {
  justify-content: flex-end;
}

.timestamp {
  font-size: 10px;
  font-weight: 500;
  letter-spacing: 0.3px;
}

/* 打字指示器 */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 16px 20px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981 0%, #047857 100%);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0.6);
    opacity: 0.4;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 滚动条样式 */
.message-list::-webkit-scrollbar {
  width: 6px;
}

.message-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.message-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #cbd5e0 0%, #9ca3af 100%);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.message-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
}

/* Prose 样式优化 */
.prose :deep(strong) {
  font-weight: 600;
  color: inherit;
}

.prose :deep(em) {
  font-style: italic;
  color: inherit;
}

.prose :deep(code) {
  background: rgba(156, 163, 175, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  color: #dc2626;
}

.message-user .prose :deep(code) {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .empty-state {
    padding: 40px 20px;
  }

  .messages {
    padding: 16px;
    padding-left: 20px;
    padding-right: 20px;
  }

  .message-content {
    max-width: 85%;
  }

  .message-text {
    padding: 12px 16px;
    font-size: 13px;
  }

  .message-avatar {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
}
</style>

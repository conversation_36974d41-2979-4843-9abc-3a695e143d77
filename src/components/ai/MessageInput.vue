<script setup lang="ts">
import { nextTick, ref } from 'vue';
import { NButton, NInput } from 'naive-ui';
import { useI18n } from 'vue-i18n';

interface Props {
  loading?: boolean;
  disabled?: boolean;
  placeholder?: string;
}

interface Emits {
  send: [content: string];
  focus: [];
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  disabled: false,
  placeholder: ''
});

const emit = defineEmits<Emits>();
const { t } = useI18n();

const inputRef = ref<InstanceType<typeof NInput>>();
const inputValue = ref('');

const handleSend = () => {
  const content = inputValue.value.trim();

  if (content && !props.disabled && !props.loading) {
    emit('send', content);
    inputValue.value = '';
  }
};

const handleKeydown = (e: KeyboardEvent) => {
  if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault();
    handleSend();
  }
};

const handleFocus = () => {
  emit('focus');
};

const focus = () => {
  nextTick(() => {
    inputRef.value?.focus();
  });
};

// 暴露方法给父组件
defineExpose({
  focus
});
</script>

<template>
  <div class="message-input">
    <div class="message-input-container">
      <NInput
        ref="inputRef"
        v-model:value="inputValue"
        type="textarea"
        :placeholder="placeholder || t('ai.chat.inputPlaceholder')"
        :autosize="{ minRows: 1, maxRows: 4 }"
        :disabled="disabled"
        @keydown="handleKeydown"
        @focus="handleFocus"
      />
      <div class="input-actions">
        <NButton
          size="medium"
          type="primary"
          :loading="loading"
          :disabled="disabled || !inputValue.trim()"
          round
          @click="handleSend"
        >
          {{ t('ai.chat.send') }}
        </NButton>
      </div>
    </div>
  </div>
</template>

<style scoped>
.message-input {
  padding: 20px;
  background: linear-gradient(135deg, rgb(var(--container-bg-color)) 0%, rgba(var(--base-text-color), 0.02) 100%);
  backdrop-filter: blur(10px);
  animation: slideUp 0.3s ease-out;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: center;
}

.message-input-container {
  width: 100%;
  max-width: 800px;
  padding-left: 40px;
  padding-right: 40px;
  position: relative;
  display: flex;
  align-items: center;
}

.message-input :deep(.n-input) {
  background: rgba(var(--container-bg-color), 0.8);
  border: 2px solid #4f46e5;
  border-radius: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.2), inset 0 1px 3px rgba(var(--base-text-color), 0.1);
  overflow: hidden;
  flex: 1;
  margin-right: 12px;
}

.message-input :deep(.n-input:hover) {
  border-color: #4338ca;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3), inset 0 1px 3px rgba(var(--base-text-color), 0.15);
}

.message-input :deep(.n-input.n-input--focus) {
  border-color: #667eea;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4), inset 0 1px 3px rgba(var(--base-text-color), 0.1);
}

.message-input :deep(.n-input__textarea) {
  min-height: 44px;
  display: flex;
  align-items: center;
}

.message-input :deep(.n-input__input-el) {
  padding: 12px 16px;
  font-size: 14px;
  line-height: 1.6;
  resize: none;
  background: transparent;
  color: rgb(var(--base-text-color));
  border: none;
  min-height: 24px;
}

.message-input :deep(.n-input__input-el::placeholder) {
  color: rgba(var(--base-text-color), 0.5);
  font-weight: 400;
}

.message-input :deep(.n-input__input-el:focus) {
  outline: none;
  box-shadow: none;
}

.input-actions {
  position: static;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 10px;
  flex-shrink: 0;
}

.input-actions .n-button {
  background: #4f46e5;
  border: none;
  border-radius: 18px;
  padding: 6px 20px;
  transition: all 0.2s ease;
  font-weight: 500;
  color: white;
  min-width: 70px;
  font-size: 14px;
  height: 36px;
}

.input-actions .n-button:hover {
  background: #4338ca;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.4);
}

.input-actions .n-button:active {
  transform: translateY(1px);
  box-shadow: 0 1px 4px rgba(79, 70, 229, 0.3);
}

.input-actions .n-button:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.8;
}

/* 加载状态 */
.message-input :deep(.n-input--disabled) {
  opacity: 0.7;
  cursor: not-allowed;
}

.message-input :deep(.n-input--disabled .n-input__input-el) {
  background: rgba(var(--base-text-color), 0.05);
  color: rgba(var(--base-text-color), 0.5);
}

/* 动画效果 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 暗色主题特殊处理 */
html.dark .message-input :deep(.n-input) {
  background: rgba(var(--layout-bg-color), 0.6);
  border: 2px solid #4f46e5;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3), inset 0 1px 3px rgba(255, 255, 255, 0.05);
}

html.dark .message-input :deep(.n-input:hover) {
  border-color: #4338ca;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4), inset 0 1px 3px rgba(255, 255, 255, 0.08);
}

html.dark .message-input :deep(.n-input.n-input--focus) {
  border-color: #667eea;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.5), inset 0 1px 3px rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-input {
    padding: 16px;
  }

  .message-input-container {
    padding-left: 20px;
    padding-right: 20px;
  }

  .message-input :deep(.n-input__input-el) {
    padding: 12px 12px;
    font-size: 16px; /* 防止iOS zoom */
  }

  .input-actions {
    margin-left: 8px;
  }

  .input-actions .n-button {
    padding: 6px 16px;
    min-width: 60px;
  }
}

/* 禁用状态样式 */
.message-input.disabled {
  opacity: 0.6;
}

.message-input.disabled :deep(.n-input) {
  border-color: #d1d5db;
  background: rgba(var(--base-text-color), 0.05);
  cursor: not-allowed;
}

.message-input.disabled :deep(.n-input:hover) {
  border-color: #d1d5db;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  transform: none;
}
</style>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { NButton } from 'naive-ui';
import { useAiStore } from '@/store/modules/ai';
import { useAuthStore } from '@/store/modules/auth';

const aiStore = useAiStore();
const authStore = useAuthStore();
const route = useRoute();

// 计算是否应该显示AI组件
const shouldShowAi = computed(() => {
  // 用户必须已登录
  if (!authStore.isLogin) {
    return false;
  }

  // 当前页面不应该是公共页面（登录、404、500等）
  if (route.meta.constant === true) {
    return false;
  }

  return true;
});

// 位置管理和拖动功能
const position = ref({ x: window.innerWidth - 72, y: window.innerHeight - 72 });
const isDragging = ref(false);
const dragOffset = ref({ x: 0, y: 0 });
const buttonRef = ref<HTMLElement>();
const hasDragged = ref(false);

const buttonStyle = computed(() => ({
  position: 'fixed' as const,
  left: `${position.value.x}px`,
  top: `${position.value.y}px`,
  cursor: isDragging.value ? 'grabbing' : 'grab',
  transform: isDragging.value ? 'scale(1.1)' : 'scale(1)',
  transition: isDragging.value ? 'none' : 'transform 0.2s ease'
}));

// 拖动中
const handleDragMove = (e: MouseEvent) => {
  if (!isDragging.value) return;

  hasDragged.value = true; // 标记发生了拖动

  const newX = e.clientX - dragOffset.value.x;
  const newY = e.clientY - dragOffset.value.y;

  // 限制在窗口内，给按钮留出空间
  const buttonSize = 48; // 按钮大小
  const maxX = window.innerWidth - buttonSize;
  const maxY = window.innerHeight - buttonSize;

  position.value = {
    x: Math.max(0, Math.min(maxX, newX)),
    y: Math.max(0, Math.min(maxY, newY))
  };
};

// 拖动结束
const handleDragEnd = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', handleDragMove);
  document.removeEventListener('mouseup', handleDragEnd);

  // 保存位置到localStorage
  localStorage.setItem('ai-trigger-position', JSON.stringify(position.value));
};

// 拖动开始
const handleDragStart = (e: MouseEvent) => {
  isDragging.value = true;
  hasDragged.value = false; // 重置拖动状态

  // 计算鼠标相对于按钮当前位置的偏移
  dragOffset.value = {
    x: e.clientX - position.value.x,
    y: e.clientY - position.value.y
  };

  document.addEventListener('mousemove', handleDragMove);
  document.addEventListener('mouseup', handleDragEnd);

  // 防止触发点击事件和文本选择
  e.preventDefault();
  e.stopPropagation();
};

// 处理点击事件（区分拖动和点击）
const handleClick = (e: MouseEvent) => {
  // 如果刚刚进行了拖动，不触发点击
  if (hasDragged.value) {
    e.preventDefault();
    e.stopPropagation();
    hasDragged.value = false; // 重置拖动状态
    return;
  }

  aiStore.toggleVisibility();
};

// 加载保存的位置
const loadPosition = () => {
  const saved = localStorage.getItem('ai-trigger-position');
  if (saved) {
    const parsedPosition = JSON.parse(saved);
    // 确保位置在屏幕范围内
    const buttonSize = 48;
    const maxX = window.innerWidth - buttonSize;
    const maxY = window.innerHeight - buttonSize;

    position.value = {
      x: Math.max(0, Math.min(maxX, parsedPosition.x)),
      y: Math.max(0, Math.min(maxY, parsedPosition.y))
    };
  }
};

// 窗口大小改变时调整位置
const handleWindowResize = () => {
  const buttonSize = 48;
  const maxX = window.innerWidth - buttonSize;
  const maxY = window.innerHeight - buttonSize;

  position.value = {
    x: Math.max(0, Math.min(maxX, position.value.x)),
    y: Math.max(0, Math.min(maxY, position.value.y))
  };
};

onMounted(() => {
  loadPosition();
  window.addEventListener('resize', handleWindowResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleWindowResize);
  // 清理拖动事件
  document.removeEventListener('mousemove', handleDragMove);
  document.removeEventListener('mouseup', handleDragEnd);
});
</script>

<template>
  <NButton
    v-if="shouldShowAi"
    ref="buttonRef"
    type="primary"
    size="large"
    circle
    class="ai-trigger-button"
    :style="buttonStyle"
    @click="handleClick"
    @mousedown="handleDragStart"
  >
    <template #icon>
      <img
        src="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp"
        alt="AI助手"
        class="ai-icon"
      />
    </template>
  </NButton>
</template>

<style scoped>
.ai-trigger-button {
  z-index: 1000;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  user-select: none;
  /* 移除transform transition，通过动态style控制 */
}

.ai-trigger-button:hover:not(:active) {
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.18);
}

.ai-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  pointer-events: none;
}
</style>

import { h } from 'vue';
import { type DropdownOption, NButton, NDropdown } from 'naive-ui';
import { $t } from '@/locales';

/** DataTable operation button dropdown */
export function useButtonAuthDropdown<T extends string | number, S>(options: CommonType.ButtonDropdown<T, S>[]) {
  // Convert ButtonDropdown options to NDropdown options
  function renderDropdown(row: S) {
    const dropdownOptions: DropdownOption[] = options
      .filter(opt => opt.show !== false)
      .map(opt => ({
        key: opt.key,
        label: opt.label,
        icon: opt.icon
      }));

    if (dropdownOptions.length === 0) {
      return null;
    }

    return h(
      NDropdown,
      {
        trigger: 'hover',
        options: dropdownOptions,
        size: 'small',
        onSelect: (key: T) => {
          const option = options.find(opt => opt.key === key);
          option?.handler?.(key, row);
        }
      },
      {
        default: () =>
          h(NButton, { quaternary: true, type: 'primary', size: 'small' }, { default: () => $t('common.more') })
      }
    );
  }

  return { renderDropdown };
}

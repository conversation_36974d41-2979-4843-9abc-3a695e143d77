import { useAuthStore } from '@/store/modules/auth';

export function useAuth() {
  const authStore = useAuthStore();

  function hasAuth(codes: string | string[]) {
    if (!authStore.isLogin) {
      // console.log('权限检查: 用户未登录，无权限');
      return false;
    }

    if (typeof codes === 'string') {
      const hasPermission = authStore.userInfo.buttons.includes(codes);
      // console.log(`权限检查: ${codes} = ${hasPermission}`);
      return hasPermission;
    }

    const hasAnyPermission = codes.some(code => authStore.userInfo.buttons.includes(code));
    // console.log(`权限检查(多个): ${codes.join(', ')} = ${hasAnyPermission}`);
    return hasAnyPermission;
  }

  return {
    hasAuth
  };
}

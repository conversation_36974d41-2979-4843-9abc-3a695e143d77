import { h } from 'vue';
import { NTag } from 'naive-ui';
import { $t } from '@/locales';

// 动态获取字典数据的函数
const getDictData = () => ({
  status: [
    { value: '1', label: $t('common.enableStatus.enable' as App.I18n.I18nKey), type: 'success' },
    { value: '2', label: $t('common.enableStatus.disable' as App.I18n.I18nKey), type: 'error' }
  ],
  gender: [
    { value: '1', label: $t('common.userGender.male' as App.I18n.I18nKey), type: 'info' },
    { value: '2', label: $t('common.userGender.female' as App.I18n.I18nKey), type: 'success' }
  ],
  feature_status: [
    { value: 'Y', label: $t('common.yesOrNo.yes' as App.I18n.I18nKey), type: 'success' },
    { value: 'N', label: $t('common.yesOrNo.no' as App.I18n.I18nKey), type: 'error' }
  ],
  notice_category: [
    { value: '1', label: $t('page.manage.notice.categoryEnum.notice' as App.I18n.I18nKey), type: 'primary' },
    { value: '2', label: $t('page.manage.notice.categoryEnum.announcement' as App.I18n.I18nKey), type: 'info' }
  ],
  menu_type: [
    { value: '1', label: $t('page.manage.menu.menuTypeEnum.topDirectory' as App.I18n.I18nKey), type: 'primary' },
    { value: '2', label: $t('page.manage.menu.menuTypeEnum.subMenu' as App.I18n.I18nKey), type: 'success' },
    { value: '3', label: $t('page.manage.menu.menuTypeEnum.singleMenu' as App.I18n.I18nKey), type: 'info' },
    { value: '4', label: $t('page.manage.menu.menuTypeEnum.specialPage' as App.I18n.I18nKey), type: 'warning' }
  ],
  menu_icon_type: [
    { value: '1', label: $t('page.manage.menu.iconTypeEnum.iconify' as App.I18n.I18nKey), type: 'primary' },
    { value: '2', label: $t('page.manage.menu.iconTypeEnum.local' as App.I18n.I18nKey), type: 'info' }
  ],
  userRole: [
    { value: 'admin', label: $t('page.manage.user.roleEnum.admin' as App.I18n.I18nKey), type: 'primary' },
    { value: 'user', label: $t('page.manage.user.roleEnum.user' as App.I18n.I18nKey), type: 'info' }
  ],
  userPosition: [
    { value: 'manager', label: $t('page.manage.user.positionEnum.manager' as App.I18n.I18nKey), type: 'primary' },
    { value: 'staff', label: $t('page.manage.user.positionEnum.staff' as App.I18n.I18nKey), type: 'info' }
  ],
  userOrgUnits: [
    { value: 'dept1', label: $t('page.manage.user.orgEnum.dept1' as App.I18n.I18nKey), type: 'primary' },
    { value: 'dept2', label: $t('page.manage.user.orgEnum.dept2' as App.I18n.I18nKey), type: 'info' }
  ]
});

/**
 * 字典工具钩子
 *
 * @returns 字典操作函数
 */
export function useDict() {
  /**
   * 获取字典选项
   *
   * @param type 字典类型
   * @returns 字典选项数组
   */
  const dictOptions = (type: keyof ReturnType<typeof getDictData>) => {
    const dictData = getDictData();
    return dictData[type].map(item => ({
      value: item.value,
      label: item.label
    }));
  };

  /**
   * 获取字典标签
   *
   * @param type 字典类型
   * @param value 字典值
   * @returns 字典标签
   */
  const dictLabel = (type: keyof ReturnType<typeof getDictData>, value: string | number | null | undefined) => {
    if (value === undefined || value === null) return '';
    const dictData = getDictData();
    const valueStr = String(value);
    const item = dictData[type]?.find(dict => dict.value === valueStr);
    return item ? item.label : '';
  };

  /**
   * 获取字典类型
   *
   * @param type 字典类型
   * @param value 字典值
   * @returns 字典标签类型
   */
  const dcitType = (type: keyof ReturnType<typeof getDictData>, value: string | number | null | undefined) => {
    if (value === undefined || value === null) return '';
    const dictData = getDictData();
    const valueStr = String(value);
    const item = dictData[type]?.find(dict => dict.value === valueStr);
    return item ? (item.type as 'success' | 'error' | 'info' | 'warning' | 'primary') : '';
  };

  /**
   * 渲染字典标签
   *
   * @param type 字典类型
   * @param value 字典值
   * @returns 渲染后的标签VNode
   */
  const dictTag = (type: keyof ReturnType<typeof getDictData>, value: string | number | null | undefined) => {
    if (value === undefined || value === null) return '';
    const dictData = getDictData();
    const valueStr = String(value);
    const item = dictData[type]?.find(dict => dict.value === valueStr);

    if (!item) return valueStr;

    return h(
      NTag,
      {
        type: item.type as 'success' | 'error' | 'info' | 'warning' | 'primary'
      },
      { default: () => item.label }
    );
  };

  return {
    dictOptions,
    dictLabel,
    dcitType,
    dictTag
  };
}

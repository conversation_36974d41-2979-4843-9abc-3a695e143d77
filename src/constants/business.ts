/** 启用状态记录 */
export const enableStatusRecord: Record<Api.Common.EnableStatus, string> = {
  1: 'common.enableStatus.enable',
  2: 'common.enableStatus.disable'
};

/** 启用状态选项 */
export const enableStatusOptions: CommonType.Option<Api.Common.EnableStatus>[] = [
  {
    label: 'common.enableStatus.enable',
    value: 1
  },
  {
    label: 'common.enableStatus.disable',
    value: 2
  }
];

/** 用户性别记录 */
export const userGenderRecord: Record<Api.SystemManage.UserGender, string> = {
  1: 'common.userGender.male',
  2: 'common.userGender.female'
};

/** 用户性别选项 */
export const userGenderOptions: CommonType.Option<Api.SystemManage.UserGender>[] = [
  {
    label: 'common.userGender.male',
    value: 1
  },
  {
    label: 'common.userGender.female',
    value: 2
  }
];

/** 菜单类型记录 */
export const menuTypeRecord: Record<string, string> = {
  '1': 'directory',
  '2': 'menu'
};

/** 菜单类型选项 */
export const menuTypeOptions: CommonType.Option<string>[] = [
  {
    label: 'directory',
    value: '1'
  },
  {
    label: 'menu',
    value: '2'
  }
];

/** 菜单图标类型选项 */
export const menuIconTypeOptions: CommonType.Option<string>[] = [
  {
    label: 'page.manage.menu.iconTypeEnum.iconify',
    value: '1'
  },
  {
    label: 'page.manage.menu.iconTypeEnum.local',
    value: '2'
  }
];

import type { MicroserviceConfigMap } from '@/utils/microservice';

/**
 * 微服务配置
 *
 * 🎯 最简单的配置方案：
 *
 * - 在这里直接修改微服务配置
 * - 无需复杂的环境变量
 * - TypeScript 类型安全
 * - 集中化管理
 *
 * 📖 配置字段说明：
 *
 * - name: 微服务名称，用于标识服务，必须与服务注册名一致
 * - prefix: API路径前缀，网关路由时会在URL前添加此前缀
 * - description: 服务描述，用于文档和调试时的说明
 *
 * 🔗 请求路径示例： 开发环境: /proxy-auth-service/login → http://gateway:8080/auth/login 生产环境: 直接请求 →
 * https://api.domain.com/auth/login
 */
export const microservicesConfig: MicroserviceConfigMap = {
  'auth-service': {
    name: 'auth-service', // 服务名称：认证授权服务，处理用户登录、权限验证等
    prefix: '/auth', // 路径前缀：所有认证相关的API都以/auth开头
    description: 'Auth Service' // 服务描述：认证授权服务
  },
  'jt808-service': {
    name: 'jt808-service', // 服务名称：JT808协议服务，处理车载终端通信
    prefix: '/jt808', // 路径前缀：所有JT808相关的API都以/jt808开头
    description: 'JT808 Service' // 服务描述：JT808协议服务
  }
  // 🚀 添加新服务示例：
  // 'vehicle-service': {
  //   name: 'vehicle-service',    // 服务名称：车辆管理服务
  //   prefix: '/vehicle',         // 路径前缀：/vehicle/list, /vehicle/add 等
  //   description: 'Vehicle Management Service' // 服务描述：车辆管理服务
  // }
};

/**
 * 获取微服务配置
 *
 * 简单直接：直接返回配置，无需复杂逻辑
 */
export function getMicroservicesConfig(): MicroserviceConfigMap {
  return microservicesConfig;
}

import type { CustomRoute, ElegantConstRoute, ElegantRoute } from '@elegant-router/types';
import { generatedRoutes } from '../elegant/routes';
import { layouts, views } from '../elegant/imports';
import { transformElegantRoutesToVueRoutes } from '../elegant/transform';

/**
 * custom routes
 *
 * @link https://github.com/soybeanjs/elegant-router?tab=readme-ov-file#custom-route
 */
const customRoutes: CustomRoute[] = [];

/** create routes when the auth route mode is static */
export function createStaticRoutes() {
  const constantRoutes: ElegantRoute[] = [];

  const authRoutes: ElegantRoute[] = [];

  [...customRoutes, ...generatedRoutes].forEach(item => {
    if (item.meta?.constant) {
      constantRoutes.push(item);
    } else {
      authRoutes.push(item);
    }
  });

  return {
    constantRoutes,
    authRoutes
  };
}

/**
 * Get auth vue routes
 *
 * @param routes Elegant routes
 */
export function getAuthVueRoutes(routes: ElegantConstRoute[]) {
  try {
    // 验证routes是否为有效数组
    if (!Array.isArray(routes)) {
      console.error('getAuthVueRoutes: routes参数不是数组', routes);
      return [];
    }

    // 过滤掉无效的路由（必须有name或routeName，且必须有component）
    const validRoutes = routes.filter(route => {
      const hasName = Boolean((route as any).routeName || route.name);
      const hasComponent = Boolean(route.component);

      if (!hasName || !hasComponent) {
        console.warn('发现无效路由，将被过滤:', route);
      }

      return hasName && hasComponent;
    });

    return transformElegantRoutesToVueRoutes(validRoutes, layouts, views);
  } catch (error) {
    console.error('转换路由时出错:', error);
    // 返回空数组，避免整个应用崩溃
    return [];
  }
}

import { computed, nextTick, ref, shallowRef } from 'vue';
import type { RouteMeta, RouteRecordRaw } from 'vue-router';
import { defineStore } from 'pinia';
import { useBoolean } from '@sa/hooks';
import type { ElegantConstRoute, LastLevelRouteKey, RouteKey, RouteMap } from '@elegant-router/types';
import { SetupStoreId } from '@/constants/app';
import { router } from '@/router';
import { fetchGetConstantRoutes, fetchGetUserRoutes, fetchIsRouteExist } from '@/service/api';
import { createStaticRoutes, getAuthVueRoutes } from '@/router/routes';
import { getRouteName, getRoutePath } from '@/router/elegant/transform';
import { useAuthStore } from '../auth';
import { useTabStore } from '../tab';
import {
  filterAuthRoutesByRoles,
  getBreadcrumbsByRoute,
  getCacheRouteNames,
  getGlobalMenusByAuthRoutes,
  getSelectedMenuKeyPathByKey,
  isRouteExistByRouteName,
  sortRoutesByOrder,
  transformMenuToSearchMenus,
  updateLocaleOfGlobalMenus
} from './shared';

export const useRouteStore = defineStore(SetupStoreId.Route, () => {
  const authStore = useAuthStore();
  const tabStore = useTabStore();
  const { bool: isInitConstantRoute, setBool: setIsInitConstantRoute } = useBoolean();
  const { bool: isInitAuthRoute, setBool: setIsInitAuthRoute } = useBoolean();

  /**
   * 路由模式 Auth route mode
   *
   * 建议在开发环境使用静态模式，在生产环境使用动态模式 如果在开发环境使用静态模式，权限路由将由插件 "@elegant-router/vue" 自动生成 It recommends to use static mode in the
   * development environment, and use dynamic mode in the production environment, if use static mode in development
   * environment, the auth routes will be auto generated by plugin "@elegant-router/vue"
   */
  const authRouteMode = ref(import.meta.env.VITE_AUTH_ROUTE_MODE);

  /** 首页路由键 Home route key */
  const routeHome = ref(import.meta.env.VITE_ROUTE_HOME);

  /**
   * 设置首页路由 Set route home
   *
   * @param routeKey 路由键 Route key
   */
  function setRouteHome(routeKey: LastLevelRouteKey) {
    routeHome.value = routeKey;
  }

  /** 常量路由 constant routes */
  const constantRoutes = shallowRef<ElegantConstRoute[]>([]);

  /**
   * 添加常量路由
   *
   * @param routes 路由配置数组
   */
  function addConstantRoutes(routes: ElegantConstRoute[]) {
    const constantRoutesMap = new Map<string, ElegantConstRoute>([]);

    routes.forEach(route => {
      const routeName = (route as any).routeName || route.name;
      constantRoutesMap.set(routeName, route);
    });

    constantRoutes.value = Array.from(constantRoutesMap.values());
  }

  /** 权限路由 auth routes */
  const authRoutes = shallowRef<ElegantConstRoute[]>([]);

  /**
   * 添加权限路由
   *
   * @param routes 路由配置数组
   */
  function addAuthRoutes(routes: ElegantConstRoute[]) {
    const authRoutesMap = new Map<string, ElegantConstRoute>([]);

    routes.forEach(route => {
      // 确保路由有合法的名称
      const routeName = (route as any).routeName || route.name;
      if (typeof routeName === 'string') {
        authRoutesMap.set(routeName, route);
      }
    });

    authRoutes.value = Array.from(authRoutesMap.values());
  }

  // 移除路由的函数集合
  const removeRouteFns: (() => void)[] = [];

  /** 全局菜单 Global menus */
  const menus = ref<App.Global.Menu[]>([]);

  /** 搜索菜单 Search menus */
  const searchMenus = computed(() => transformMenuToSearchMenus(menus.value));

  /**
   * 获取全局菜单 Get global menus
   *
   * @param routes 路由配置
   */
  function getGlobalMenus(routes: ElegantConstRoute[]) {
    menus.value = getGlobalMenusByAuthRoutes(routes);
  }

  /** 根据当前语言更新全局菜单 Update global menus by locale */
  function updateGlobalMenusByLocale() {
    menus.value = updateLocaleOfGlobalMenus(menus.value);
  }

  /** 缓存路由 Cache routes */
  const cacheRoutes = ref<RouteKey[]>([]);

  /**
   * 排除缓存的路由 Exclude cache routes
   *
   * 用于重置路由缓存 for reset route cache
   */
  const excludeCacheRoutes = ref<RouteKey[]>([]);

  /**
   * 获取需要缓存的路由 Get cache routes
   *
   * @param routes Vue路由配置
   */
  function getCacheRoutes(routes: RouteRecordRaw[]) {
    cacheRoutes.value = getCacheRouteNames(routes);
  }

  /**
   * 重置路由缓存 Reset route cache
   *
   * @default router.currentRoute.value.name 当前路由名
   * @param routeKey 路由键
   */
  async function resetRouteCache(routeKey?: RouteKey) {
    const routeName = routeKey || (router.currentRoute.value.name as RouteKey);

    excludeCacheRoutes.value.push(routeName);

    await nextTick();

    excludeCacheRoutes.value = [];
  }

  /** 全局面包屑 Global breadcrumbs */
  const breadcrumbs = computed(() => getBreadcrumbsByRoute(router.currentRoute.value, menus.value));

  /** 重置存储 Reset store */
  async function resetStore() {
    try {
      const routeStore = useRouteStore();
      routeStore.$reset();
      resetVueRoutes();
      // 重置存储后，需要重新初始化常量路由
      // after reset store, need to re-init constant route
      await initConstantRoute();
      tabStore.initHomeTab();
      // 强制导航到登录页
      if (!authStore.isLogin) {
        setTimeout(() => {
          router.push({ name: 'login' });
        }, 100);
      }
    } catch {
      // 确保系统可用性
      // 重置Vue路由
      resetVueRoutes();
      // 添加基本登录路由
      const staticRoutes = createStaticRoutes();
      const loginRoute = staticRoutes.constantRoutes.find(route => route.name === 'login');
      if (loginRoute) {
        const loginVueRoutes = getAuthVueRoutes([loginRoute]);
        addRoutesToVueRouter(loginVueRoutes);
        // 添加根路由重定向到登录
        const rootRoute: RouteRecordRaw = {
          name: 'root',
          path: '/',
          redirect: '/login'
        };
        router.addRoute(rootRoute);
        // 强制导航到登录页
        setTimeout(() => {
          router.push({ name: 'login' });
        }, 100);
      }
    }
  }

  /** 重置Vue路由 Reset vue routes */
  function resetVueRoutes() {
    removeRouteFns.forEach(fn => fn());
    removeRouteFns.length = 0;
  }

  /** 初始化常量路由 init constant route */
  async function initConstantRoute() {
    if (isInitConstantRoute.value) return;

    try {
      const staticRoute = createStaticRoutes();

      if (authRouteMode.value === 'static') {
        addConstantRoutes(staticRoute.constantRoutes);
      } else {
        const { data, error } = await fetchGetConstantRoutes();

        if (!error) {
          try {
            const transformedRoutes = (data as any[]).map(route => {
              // 将"Y"转为true, "N"转为false
              const transformBoolean = (value: string | null | undefined): boolean => {
                if (value === 'Y') return true;
                if (value === 'N') return false;
                return Boolean(value);
              };

              // 构建meta对象
              const meta: Partial<RouteMeta> = {
                title: route.name || ''
              };

              // 为特定路由添加constant字段
              const constantRouteNames = ['login', '403', '404', '500'];
              if (constantRouteNames.includes(route.routeName)) {
                meta.constant = true;
              }

              // 添加i18n键
              if (route.i18nKey) meta.i18nKey = route.i18nKey as App.I18n.I18nKey;

              // 添加图标
              if (route.icon) meta.icon = route.icon;

              // 添加排序
              if (route.sort !== undefined && route.sort !== null) {
                meta.order = Number(route.sort);
              }

              // 添加固定标签索引
              if (route.fixedIndexInTab !== undefined && route.fixedIndexInTab !== null) {
                meta.fixedIndexInTab = Number(route.fixedIndexInTab);
              }

              // 添加缓存设置
              if (route.keepAlive) {
                meta.keepAlive = transformBoolean(route.keepAlive);
              }

              // 添加隐藏设置
              if (route.hide) {
                meta.hideInMenu = transformBoolean(route.hide);
              }

              // 处理查询参数
              let queryParams = [];
              if (route.query && typeof route.query === 'string') {
                try {
                  queryParams = JSON.parse(route.query);
                } catch {
                  queryParams = [];
                }
              }
              // 返回转换后的路由对象
              const transformedRoute: ElegantConstRoute = {
                name: route.routeName,
                path: route.routePath,
                component: route.component,
                meta: meta as RouteMeta
              };
              // 添加id字段
              if (route.id) (transformedRoute as any).id = route.id;
              // 添加查询参数
              if (queryParams.length > 0) (transformedRoute as any).query = queryParams;
              // 添加子路由
              if (route.children && route.children.length > 0) {
                transformedRoute.children = route.children.map(child => transformChildRoute(child));
              }
              return transformedRoute;
            });

            addConstantRoutes(transformedRoutes);
          } catch {
            // 出错时使用静态路由
            addConstantRoutes(staticRoute.constantRoutes);
          }
        } else {
          // 如果获取常量路由失败，使用静态常量路由
          addConstantRoutes(staticRoute.constantRoutes);
        }
      }

      handleConstantAndAuthRoutes();

      // 确保根路由重定向到适当的页面
      try {
        // 检查是否已登录，决定重定向目标
        const redirectTarget = authStore.isLogin ? routeHome.value || 'home' : 'login';

        // 使用简单的根路由配置，避免使用getAuthVueRoutes
        const rootRoute: RouteRecordRaw = {
          name: 'root',
          path: '/',
          redirect: getRoutePath(redirectTarget) || (authStore.isLogin ? '/home' : '/login')
        };

        // 尝试先移除已存在的根路由
        try {
          router.removeRoute('root');
        } catch {
          // 忽略错误
        }

        // 直接添加根路由
        router.addRoute(rootRoute);
      } catch {
        // 出错时默认重定向到login
        try {
          const basicRootRoute: RouteRecordRaw = {
            name: 'root',
            path: '/',
            redirect: '/login'
          };
          router.addRoute(basicRootRoute);
        } catch {
          // 添加一个基本的根路由重定向到/home，确保应用不会崩溃
          const basicRootRoute: RouteRecordRaw = {
            name: 'root',
            path: '/',
            redirect: '/home'
          };
          router.addRoute(basicRootRoute);
        }
      }

      setIsInitConstantRoute(true);
      tabStore.initHomeTab();

      // 强制导航到登录页
      if (!authStore.isLogin) {
        setTimeout(() => {
          router.push({ name: 'login' });
        }, 100);
      }
    } catch {
      // 确保至少能访问登录页
      try {
        const loginRoute = createStaticRoutes().constantRoutes.find(route => route.name === 'login');
        if (loginRoute) {
          const [loginVueRoute] = getAuthVueRoutes([loginRoute]);
          router.addRoute(loginVueRoute);

          const rootRoute: RouteRecordRaw = {
            name: 'root',
            path: '/',
            redirect: '/login'
          };
          router.addRoute(rootRoute);
        }
      } catch {
        // 添加一个基本的根路由重定向到/home，确保应用不会崩溃
        const basicRootRoute: RouteRecordRaw = {
          name: 'root',
          path: '/',
          redirect: '/home'
        };
        router.addRoute(basicRootRoute);
      }
    }
  }

  /**
   * 转换子路由的函数
   *
   * @param childRoute 后端返回的子路由数据
   * @returns 转换后的路由对象
   */
  function transformChildRoute(childRoute: any): ElegantConstRoute {
    // 转换布尔值
    const transformBoolean = (value: string | null | undefined): boolean => {
      if (value === 'Y') return true;
      if (value === 'N') return false;
      return Boolean(value);
    };

    // 构建meta对象
    const meta: Partial<RouteMeta> = {
      title: childRoute.name || ''
    };

    // 为特定路由添加constant字段
    const constantRouteNames = ['login', '403', '404', '500'];
    if (constantRouteNames.includes(childRoute.routeName)) {
      meta.constant = true;
    }

    // 添加i18n键
    if (childRoute.i18nKey) meta.i18nKey = childRoute.i18nKey as App.I18n.I18nKey;

    // 添加图标
    if (childRoute.icon) meta.icon = childRoute.icon;

    // 添加排序
    if (childRoute.sort !== undefined && childRoute.sort !== null) {
      meta.order = Number(childRoute.sort);
    }

    // 添加固定标签索引
    if (childRoute.fixedIndexInTab !== undefined && childRoute.fixedIndexInTab !== null) {
      meta.fixedIndexInTab = Number(childRoute.fixedIndexInTab);
    }

    // 添加缓存设置
    if (childRoute.keepAlive) {
      meta.keepAlive = transformBoolean(childRoute.keepAlive);
    }

    // 添加隐藏设置
    if (childRoute.hide) {
      meta.hideInMenu = transformBoolean(childRoute.hide);
    }

    // 处理查询参数
    let queryParams = [];
    if (childRoute.query && typeof childRoute.query === 'string') {
      try {
        queryParams = JSON.parse(childRoute.query);
      } catch {
        queryParams = [];
      }
    }

    // 返回转换后的路由对象
    const transformedChildRoute: ElegantConstRoute = {
      name: childRoute.routeName,
      path: childRoute.routePath,
      component: childRoute.component,
      meta: meta as RouteMeta
    };

    // 添加id字段
    if (childRoute.id) (transformedChildRoute as any).id = childRoute.id;

    // 添加查询参数
    if (queryParams.length > 0) (transformedChildRoute as any).query = queryParams;

    // 添加子路由
    if (childRoute.children && childRoute.children.length > 0) {
      transformedChildRoute.children = childRoute.children.map(child => transformChildRoute(child));
    }

    return transformedChildRoute;
  }

  /**
   * 从不同格式的响应中提取路由数据
   *
   * @param routeData 原始响应数据
   * @returns 路由数组和首页路由
   */
  function extractRoutesFromResponse(routeData: any): { routes: any[]; home: LastLevelRouteKey } {
    let routes: any[] = [];
    let home: LastLevelRouteKey = 'home';

    // 简化逻辑，使用条件判断提前返回
    // 处理新的API响应格式: { code, msg, data: { menus: [] } }
    if (routeData.code === 200 && routeData.data && Array.isArray(routeData.data.menus)) {
      routes = routeData.data.menus;
    }
    // 检查API格式 data.data.menus（嵌套的返回格式）
    else if (routeData.data?.code === 200 && Array.isArray(routeData.data?.data?.menus)) {
      routes = routeData.data.data.menus;
    }
    // 检查后端返回的标准格式
    else if (Array.isArray(routeData.routes)) {
      // 标准API格式，直接使用
      routes = routeData.routes || [];
      if (typeof routeData.home === 'string') {
        home = routeData.home as LastLevelRouteKey;
      }
    }
    // 检查是否直接是menus数组
    else if (Array.isArray(routeData.menus)) {
      routes = routeData.menus;
    }
    // 检查是否本身就是一个数组
    else if (Array.isArray(routeData)) {
      // 如果data直接是数组，将其视为routes数组
      routes = routeData;
    }
    // 单个对象
    else if (routeData) {
      // 如果data是单个对象，将其转换为数组
      routes = [routeData];
    }

    // 如果没有指定首页，尝试从路由中推断
    if (home === 'home' && routes.length > 0) {
      const firstRoute = routes[0];
      // 使用as any来避免类型检查错误
      const routeName = (firstRoute as any).routeName || firstRoute.name;
      if (typeof routeName === 'string') {
        home = routeName as LastLevelRouteKey;
      }
    }

    return { routes, home };
  }

  /**
   * 验证并处理路由数据
   *
   * @param routeData 原始路由数据
   * @returns 处理后的路由和首页路由名
   */
  function validateAndProcessRoutes(routeData: any): { routes: any[]; home: LastLevelRouteKey } {
    // 如果routeData完全为空，直接返回空数组和默认home
    if (!routeData) {
      return { routes: [], home: 'home' };
    }

    // 从响应中提取路由数据
    const { routes: extractedRoutes, home } = extractRoutesFromResponse(routeData);

    // 转换为标准路由格式
    const transformedRoutes = extractedRoutes.map(route => transformMenuToRoute(route));

    // 验证每个路由对象，移除无效路由
    const validRoutes = transformedRoutes.filter(route => {
      // 为null或undefined的路由无效
      if (!route) return false;

      // 必须有name
      const hasValidName = Boolean(route.name);
      // 必须有component
      const hasComponent = Boolean(route.component);

      if (!hasValidName || !hasComponent) {
        // 过滤无效路由
        return false;
      }

      return hasValidName && hasComponent;
    });

    // 确保每个路由都有children属性（即使为空数组）和meta对象
    const processedRoutes = validRoutes.map(route => {
      if (!route.children) {
        route.children = [];
      }
      if (!route.meta) {
        route.meta = {
          title: route.name as string
        };
      } else if (!route.meta.title) {
        route.meta.title = route.name as string;
      }
      return route;
    });

    return { routes: processedRoutes, home };
  }

  /** 初始化权限路由 Init auth route */
  async function initAuthRoute() {
    // 检查用户信息是否已初始化
    // check if user info is initialized
    if (!authStore.userInfo.userId) {
      await authStore.initUserInfo();
    }

    if (authRouteMode.value === 'static') {
      initStaticAuthRoute();
    } else {
      await initDynamicAuthRoute();
    }

    tabStore.initHomeTab();
  }

  /** 初始化静态权限路由 Init static auth route */
  function initStaticAuthRoute() {
    const { authRoutes: staticAuthRoutes } = createStaticRoutes();

    if (authStore.isStaticSuper) {
      addAuthRoutes(staticAuthRoutes);
    } else {
      const filteredAuthRoutes = filterAuthRoutesByRoles(staticAuthRoutes, authStore.userInfo.roles);

      addAuthRoutes(filteredAuthRoutes);
    }

    handleConstantAndAuthRoutes();

    setIsInitAuthRoute(true);
  }

  /** 初始化动态权限路由 Init dynamic auth route */
  async function initDynamicAuthRoute() {
    const { data: responseData, error } = await fetchGetUserRoutes();

    // 使用any类型避免类型检查错误
    const data = responseData as any;

    if (!error) {
      try {
        // 处理和验证路由数据
        const { routes, home } = validateAndProcessRoutes(data);

        if (routes.length === 0) {
          // 添加空权限路由，不包含home页面
          addAuthRoutes([]);

          // 处理路由
          handleConstantAndAuthRoutes();

          // 设置首页为404页面
          setRouteHome('404');

          // 更新根路由重定向到404
          handleUpdateRootRouteRedirect('404');
        } else {
          // 添加获取到的权限路由
          addAuthRoutes(routes);

          // 处理路由
          handleConstantAndAuthRoutes();

          // 设置首页
          setRouteHome(home);

          // 更新根路由重定向
          handleUpdateRootRouteRedirect(home);
        }

        setIsInitAuthRoute(true);
      } catch {
        // 出错时尝试使用默认路由
        try {
          const staticRoutes = createStaticRoutes();
          const homeRoute = staticRoutes.authRoutes.find(route => route.name === 'home');

          if (homeRoute) {
            addAuthRoutes([homeRoute]);
            handleConstantAndAuthRoutes();
            setRouteHome('home');
            handleUpdateRootRouteRedirect('home');
          }

          setIsInitAuthRoute(true);
        } catch {
          authStore.resetStore();
        }
      }
    } else {
      // 如果获取用户路由失败，尝试使用默认路由
      try {
        const staticRoutes = createStaticRoutes();
        const homeRoute = staticRoutes.authRoutes.find(route => route.name === 'home');

        if (homeRoute) {
          addAuthRoutes([homeRoute]);
          handleConstantAndAuthRoutes();
          setRouteHome('home');
          handleUpdateRootRouteRedirect('home');
          setIsInitAuthRoute(true);
        } else {
          authStore.resetStore();
        }
      } catch {
        authStore.resetStore();
      }
    }
  }

  /** 将布尔值转换为Y/N格式 */
  function transformBooleanValue(value: string | null | undefined): boolean {
    if (value === 'Y') return true;
    if (value === 'N') return false;
    return Boolean(value);
  }

  /** 处理菜单项的元数据 */
  function processMenuMeta(menu: any): RouteMeta {
    // 构建meta对象
    const meta: Partial<RouteMeta> = {
      title: menu.name || ''
    };

    // 添加i18n键
    if (menu.i18nKey) meta.i18nKey = menu.i18nKey as App.I18n.I18nKey;

    // 添加图标
    if (menu.icon) {
      meta.icon = menu.icon;

      // 处理图标类型
      if (menu.iconType === '1') {
        // 外部图标库，如Iconify
        meta.icon = menu.icon;
      } else if (menu.iconType === '2') {
        // 本地图标
        meta.localIcon = menu.icon;
      }
    }

    // 添加排序
    if (menu.sort !== undefined && menu.sort !== null) {
      meta.order = Number(menu.sort);
    }

    // 添加固定标签索引
    if (menu.fixedIndexInTab !== undefined && menu.fixedIndexInTab !== null) {
      meta.fixedIndexInTab = Number(menu.fixedIndexInTab);
    }

    // 添加缓存设置
    if (menu.keepAlive) {
      meta.keepAlive = transformBooleanValue(menu.keepAlive);
    }

    // 添加隐藏设置
    if (menu.hide) {
      meta.hideInMenu = transformBooleanValue(menu.hide);
    }

    // 添加多标签设置
    if (menu.multiTab) {
      meta.multiTab = transformBooleanValue(menu.multiTab);
    }

    // 添加外部链接
    if (menu.href) meta.href = menu.href;

    // 添加iframe URL
    if (menu.iframeUrl) meta.iframeUrl = menu.iframeUrl;

    return meta as RouteMeta;
  }

  /** 处理菜单项的查询参数 */
  function processMenuQuery(menu: any): any[] {
    let queryParams = [];
    if (menu.query && typeof menu.query === 'string') {
      try {
        queryParams = JSON.parse(menu.query);
      } catch {
        queryParams = [];
      }
    }
    return queryParams;
  }

  /**
   * 将菜单项转换为路由对象
   *
   * @param menu 后端返回的菜单项
   * @returns 转换后的路由对象
   */
  function transformMenuToRoute(menu: any): ElegantConstRoute {
    // 如果已经是标准格式，直接返回
    if (menu.meta && menu.name && !menu.routeName) {
      if (menu.children) {
        menu.children = menu.children.map((child: any) => transformMenuToRoute(child));
      }
      return menu as ElegantConstRoute;
    }

    // 处理元数据
    const meta = processMenuMeta(menu);

    // 处理查询参数
    const queryParams = processMenuQuery(menu);

    // 只有有 iframeUrl 字段且 component 包含 'iframe-page' 的菜单才强制 routeName 为 'iframe-page'
    let routeName = menu.routeName || menu.name;
    if (menu.iframeUrl && menu.component && menu.component.includes('iframe-page')) {
      routeName = 'iframe-page';
    }

    // 创建路由对象
    const transformedRoute: ElegantConstRoute = {
      name: routeName,
      path: menu.routePath || menu.path || `/${routeName}`,
      component: menu.component || '',
      meta
    };

    // 添加id字段
    if (menu.id) (transformedRoute as any).id = menu.id;

    // 添加查询参数
    if (queryParams.length > 0) (transformedRoute as any).query = queryParams;

    // 添加子路由
    if (menu.children && menu.children.length > 0) {
      transformedRoute.children = menu.children.map((child: any) => transformMenuToRoute(child));
    } else {
      transformedRoute.children = [];
    }

    return transformedRoute;
  }

  /** 处理常量和权限路由 handle constant and auth routes */
  function handleConstantAndAuthRoutes() {
    try {
      // 确保路由数组不为空
      if (constantRoutes.value.length === 0 && authRoutes.value.length === 0) {
        return;
      }

      // 合并并排序路由
      const allRoutes = [...constantRoutes.value, ...authRoutes.value];

      const sortRoutes = sortRoutesByOrder(allRoutes);

      // 转换为Vue路由
      const vueRoutes = getAuthVueRoutes(sortRoutes);

      // 重置并添加路由
      resetVueRoutes();
      addRoutesToVueRouter(vueRoutes);
      // 处理全局菜单和缓存路由
      getGlobalMenus(sortRoutes);
      getCacheRoutes(vueRoutes);
    } catch {
      // 在出错时使用基本的常量路由
      const staticRoutes = createStaticRoutes().constantRoutes;

      resetVueRoutes();

      // 尝试添加基本路由（登录、404等）
      const basicRoutes = getAuthVueRoutes(
        staticRoutes.filter(route => route.meta?.constant === true || route.name === 'login' || route.name === '404')
      );
      addRoutesToVueRouter(basicRoutes);
    }
  }

  /**
   * 将路由添加到Vue Router Add routes to vue router
   *
   * @param routes Vue路由配置
   */
  function addRoutesToVueRouter(routes: RouteRecordRaw[]) {
    routes.forEach(route => {
      const removeFn = router.addRoute(route);
      addRemoveRouteFn(removeFn);
    });
  }

  /**
   * 添加移除路由的函数 Add remove route fn
   *
   * @param fn 移除路由的函数
   */
  function addRemoveRouteFn(fn: () => void) {
    removeRouteFns.push(fn);
  }

  /**
   * 当路由模式为动态时，更新根路由重定向 Update root route redirect when auth route mode is dynamic
   *
   * @param redirectKey 重定向的路由键
   */
  function handleUpdateRootRouteRedirect(redirectKey: LastLevelRouteKey) {
    try {
      // 用本地变量保存redirectKey，避免修改参数
      let actualRedirectKey = redirectKey;

      // 检查redirectKey是否有效
      if (!actualRedirectKey) {
        actualRedirectKey = 'home' as LastLevelRouteKey;
      }

      const redirect = getRoutePath(actualRedirectKey);

      // 确保路由可用，直接使用基本的根路由配置
      const basicRootRoute: RouteRecordRaw = {
        name: 'root',
        path: '/',
        redirect: redirect || '/home'
      };

      // 尝试先移除已存在的根路由
      router.removeRoute('root');

      // 直接添加基本根路由
      try {
        router.addRoute(basicRootRoute);
      } catch {
        // 忽略错误
      }
    } catch {
      // 添加一个基本的根路由重定向到/home，确保应用不会崩溃
      const basicRootRoute: RouteRecordRaw = {
        name: 'root',
        path: '/',
        redirect: '/home'
      };

      router.addRoute(basicRootRoute);
    }
  }

  /**
   * 判断权限路由是否存在 Get is auth route exist
   *
   * @param routePath 路由路径
   */
  async function getIsAuthRouteExist(routePath: RouteMap[RouteKey]) {
    const routeName = getRouteName(routePath);

    if (!routeName) {
      return false;
    }

    if (authRouteMode.value === 'static') {
      const { authRoutes: staticAuthRoutes } = createStaticRoutes();
      return isRouteExistByRouteName(routeName, staticAuthRoutes);
    }

    const { data } = await fetchIsRouteExist(routeName);

    return data;
  }

  /**
   * 获取选中的菜单键路径 Get selected menu key path
   *
   * @param selectedKey 选中的菜单键
   */
  function getSelectedMenuKeyPath(selectedKey: string) {
    return getSelectedMenuKeyPathByKey(selectedKey, menus.value);
  }

  /** 当用户已登录时的路由切换处理 On route switch when logged in */
  async function onRouteSwitchWhenLoggedIn() {
    await authStore.initUserInfo();
  }

  /** 当用户未登录时的路由切换处理 On route switch when not logged in */
  async function onRouteSwitchWhenNotLoggedIn() {
    // 一些不需要登录的全局初始化逻辑
    // some global init logic if it does not need to be logged in
  }

  return {
    resetStore,
    routeHome,
    menus,
    searchMenus,
    updateGlobalMenusByLocale,
    cacheRoutes,
    excludeCacheRoutes,
    resetRouteCache,
    breadcrumbs,
    initConstantRoute,
    isInitConstantRoute,
    initAuthRoute,
    isInitAuthRoute,
    setIsInitAuthRoute,
    getIsAuthRouteExist,
    getSelectedMenuKeyPath,
    onRouteSwitchWhenLoggedIn,
    onRouteSwitchWhenNotLoggedIn
  };
});

import { computed, nextTick, ref } from 'vue';
import { defineStore } from 'pinia';
import { DEFAULT_DEEPSEEK_CONFIG, DeepSeekApi } from '@/service/api/ai';
import type { AiMessage, DeepSeekConfig } from '@/types/ai';

export const useAiStore = defineStore('ai', () => {
  // 状态
  const isVisible = ref(false);
  const isMinimized = ref(false);
  const position = ref({ x: window.innerWidth - 400, y: 100 });
  const isConnected = ref(false);
  const isLoading = ref(false);
  const messages = ref<AiMessage[]>([]);
  const config = ref<DeepSeekConfig>({ ...DEFAULT_DEEPSEEK_CONFIG });

  // API 实例
  let apiClient: DeepSeekApi | null = null;

  // 计算属性
  const hasApiKey = computed(() => Boolean(config.value.apiKey));
  const canSendMessage = computed(() => hasApiKey.value && !isLoading.value);

  // 初始化 API 客户端
  const initApiClient = () => {
    if (hasApiKey.value) {
      apiClient = new DeepSeekApi(config.value);
    }
  };

  // 加载本地配置
  const loadConfig = () => {
    const savedConfig = localStorage.getItem('ai-assistant-config');
    if (savedConfig) {
      try {
        const parsed = JSON.parse(savedConfig);
        config.value = { ...DEFAULT_DEEPSEEK_CONFIG, ...parsed };
      } catch {}
    }

    const savedMessages = localStorage.getItem('ai-assistant-messages');
    if (savedMessages) {
      try {
        const parsed = JSON.parse(savedMessages);
        messages.value = parsed;
      } catch {}
    }

    initApiClient();
  };

  // 保存配置
  const saveConfig = () => {
    localStorage.setItem('ai-assistant-config', JSON.stringify(config.value));
  };

  // 保存消息历史
  const saveMessages = () => {
    localStorage.setItem('ai-assistant-messages', JSON.stringify(messages.value));
  };

  // 更新配置
  const updateConfig = (newConfig: Partial<DeepSeekConfig>) => {
    config.value = { ...config.value, ...newConfig };
    saveConfig();
    initApiClient();

    // 如果配置了API Key，重置连接状态为待测试
    if (config.value.apiKey) {
      isConnected.value = false;
    }
  };

  // 测试连接
  const testConnection = async (): Promise<boolean> => {
    if (!apiClient || !hasApiKey.value) return false;

    try {
      isLoading.value = true;
      const result = await apiClient.testConnection();
      isConnected.value = result;
      return result;
    } catch {
      isConnected.value = false;
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  // 发送消息
  const sendMessage = async (content: string): Promise<void> => {
    try {
      if (!apiClient || !canSendMessage.value) {
        return;
      }

      // 添加用户消息
      const userMessage: AiMessage = {
        id: `user-${Date.now()}`,
        role: 'user',
        content,
        timestamp: Date.now()
      };
      messages.value = [...messages.value, userMessage];

      // 添加助手消息占位符
      let assistantMessage: AiMessage = {
        id: `assistant-${Date.now()}`,
        role: 'assistant',
        content: '',
        timestamp: Date.now(),
        isStreaming: true
      };
      messages.value = [...messages.value, assistantMessage];

      isLoading.value = true;

      try {
        // 使用流式响应
        const stream = apiClient.sendMessageStream(messages.value.slice(0, -1));
        let hasContent = false;
        let contentBuffer = '';

        for await (const chunk of stream) {
          const choice = chunk.choices?.[0];
          if (choice?.delta?.content) {
            contentBuffer += choice.delta.content;
            hasContent = true;

            // 更新助手消息内容
            assistantMessage = {
              ...assistantMessage,
              content: contentBuffer
            };

            // 替换整个数组以触发响应式更新
            const updatedMessages = [...messages.value];
            updatedMessages[updatedMessages.length - 1] = assistantMessage;
            messages.value = updatedMessages;
          }

          if (choice?.finish_reason) {
            // 最终更新 - 标记流式完成
            assistantMessage = {
              ...assistantMessage,
              isStreaming: false
            };

            const finalMessages = [...messages.value];
            finalMessages[finalMessages.length - 1] = assistantMessage;
            messages.value = finalMessages;
            break;
          }
        }

        if (!hasContent) {
          // 如果流式失败，尝试非流式
          const response = await apiClient.sendMessage(messages.value.slice(0, -1));
          assistantMessage = {
            ...assistantMessage,
            content: response.choices[0].message.content || '无响应内容',
            isStreaming: false
          };

          const fallbackMessages = [...messages.value];
          fallbackMessages[fallbackMessages.length - 1] = assistantMessage;
          messages.value = fallbackMessages;
        }

        // 如果发送成功，自动设置连接状态为已连接
        if (!isConnected.value) {
          isConnected.value = true;
        }
      } catch (apiError) {
        assistantMessage = {
          ...assistantMessage,
          content: `抱歉，发送消息时出现错误：${(apiError as Error).message}`,
          isStreaming: false
        };

        const errorMessages = [...messages.value];
        errorMessages[errorMessages.length - 1] = assistantMessage;
        messages.value = errorMessages;
      } finally {
        isLoading.value = false;
        await nextTick();
        saveMessages();
      }
    } catch {}
  };

  // 清空消息历史
  const clearMessages = () => {
    messages.value = [];
    saveMessages();
  };

  // 显示/隐藏助手
  const toggleVisibility = () => {
    isVisible.value = !isVisible.value;
  };

  // 最小化/还原
  const toggleMinimize = () => {
    isMinimized.value = !isMinimized.value;
  };

  // 更新位置
  const updatePosition = (newPosition: { x: number; y: number }) => {
    position.value = newPosition;
  };

  // 返回状态和方法
  return {
    // 状态
    isVisible,
    isMinimized,
    position,
    isConnected,
    isLoading,
    messages,
    config,

    // 计算属性
    hasApiKey,
    canSendMessage,

    // 方法
    loadConfig,
    updateConfig,
    testConnection,
    sendMessage,
    clearMessages,
    toggleVisibility,
    toggleMinimize,
    updatePosition
  };
});

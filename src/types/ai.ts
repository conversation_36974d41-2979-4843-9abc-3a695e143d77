/** AI 消息角色类型 */
export type MessageRole = 'user' | 'assistant' | 'system';

/** DeepSeek 模型类型 */
export type DeepSeekModel = 'deepseek-chat' | 'deepseek-coder' | 'deepseek-reasoner';

/** DeepSeek 模型配置 */
export const DEEPSEEK_MODELS = {
  'deepseek-chat': {
    name: 'DeepSeek Chat',
    description: '通用对话模型，适合日常聊天和问答'
  },
  'deepseek-coder': {
    name: 'DeepSeek Coder',
    description: '专业编程模型，擅长代码生成和技术问题'
  },
  'deepseek-reasoner': {
    name: 'DeepSeek Reasoner',
    description: '推理模型，擅长逻辑分析和复杂问题解决'
  }
} as const;

/** AI 消息接口 */
export interface AiMessage {
  id: string;
  role: MessageRole;
  content: string;
  timestamp: number;
  isStreaming?: boolean;
}

/** DeepSeek API 配置 */
export interface DeepSeekConfig {
  apiKey: string;
  baseUrl: string;
  model: DeepSeekModel;
  maxTokens: number;
  temperature: number;
}

/** AI 助手状态 */
export interface AiAssistantState {
  isVisible: boolean;
  isMinimized: boolean;
  position: { x: number; y: number };
  isConnected: boolean;
  isLoading: boolean;
  messages: AiMessage[];
  config: DeepSeekConfig;
}

/** API 响应接口 */
export interface DeepSeekResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

/** 流式响应接口 */
export interface DeepSeekStreamChunk {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    delta: {
      role?: string;
      content?: string;
    };
    finish_reason?: string;
  }>;
}

<script setup lang="ts">
import { GLOBAL_HEADER_MENU_ID } from '@/constants/app';
import { useRouteStore } from '@/store/modules/route';
import { useRouterPush } from '@/hooks/common/router';
import { useMenu } from '../../../context';

defineOptions({
  name: 'HorizontalMenu'
});

const routeStore = useRouteStore();
const { routerPushByKey, routerPushByKeyWithMetaQuery } = useRouterPush();
const { selectedKey } = useMenu();

function handleMenuClick(key: string) {
  const menu = routeStore.menus.find(item => item.key === key);
  if (menu && menu.routeKey === 'iframe-page' && menu.iframeUrl) {
    routerPushByKey('iframe-page', { params: { url: menu.iframeUrl } });
  } else {
    routerPushByKeyWithMetaQuery(key as any);
  }
}
</script>

<template>
  <Teleport :to="`#${GLOBAL_HEADER_MENU_ID}`">
    <NMenu
      mode="horizontal"
      :value="selectedKey"
      :options="routeStore.menus"
      :indent="18"
      responsive
      @update:value="handleMenuClick"
    />
  </Teleport>
</template>

<style scoped></style>

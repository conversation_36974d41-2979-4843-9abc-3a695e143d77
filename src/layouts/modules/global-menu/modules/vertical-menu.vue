<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { SimpleScrollbar } from '@sa/materials';
import { GLOBAL_SIDER_MENU_ID } from '@/constants/app';
import { useAppStore } from '@/store/modules/app';
import { useThemeStore } from '@/store/modules/theme';
import { useRouteStore } from '@/store/modules/route';
import { useRouterPush } from '@/hooks/common/router';
import { useMenu } from '../../../context';

defineOptions({
  name: 'VerticalMenu'
});

const route = useRoute();
const appStore = useAppStore();
const themeStore = useThemeStore();
const routeStore = useRouteStore();
const { routerPushByKey, routerPushByKeyWithMetaQuery } = useRouterPush();
const { selectedKey } = useMenu();

const inverted = computed(() => !themeStore.darkMode && themeStore.sider.inverted);

const expandedKeys = ref<string[]>([]);

function updateExpandedKeys() {
  if (appStore.siderCollapse || !selectedKey.value) {
    expandedKeys.value = [];
    return;
  }
  expandedKeys.value = routeStore.getSelectedMenuKeyPath(selectedKey.value);
}

watch(
  () => route.name,
  () => {
    updateExpandedKeys();
  },
  { immediate: true }
);

function findMenuByKey(menus, key) {
  for (const menu of menus) {
    if (menu.key === key) return menu;
    if (menu.children) {
      const found = findMenuByKey(menu.children, key);
      if (found) return found;
    }
  }
  return undefined;
}

function handleMenuClick(key: string) {
  const menu = findMenuByKey(routeStore.menus, key);
  if (menu && menu.iframeUrl) {
    // 内嵌页面
    routerPushByKey('iframe-page', { params: { url: menu.iframeUrl } });
  } else if (menu && menu.href) {
    // 外部链接，直接新窗口打开
    window.open(menu.href, '_blank');
  } else {
    routerPushByKeyWithMetaQuery(key as any);
  }
}
</script>

<template>
  <Teleport :to="`#${GLOBAL_SIDER_MENU_ID}`">
    <SimpleScrollbar>
      <NMenu
        v-model:expanded-keys="expandedKeys"
        mode="vertical"
        :value="selectedKey"
        :collapsed="appStore.siderCollapse"
        :collapsed-width="themeStore.sider.collapsedWidth"
        :collapsed-icon-size="22"
        :options="routeStore.menus"
        :inverted="inverted"
        :indent="18"
        @update:value="handleMenuClick"
      />
    </SimpleScrollbar>
  </Teleport>
</template>

<style scoped></style>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { <PERSON><PERSON><PERSON><PERSON>, NLayoutHeader, useMessage } from 'naive-ui';
import { useI18n } from 'vue-i18n';
import { useAiStore } from '@/store/modules/ai';
import type { DeepSeekConfig } from '@/types/ai';
import MessageList from '@/components/ai/MessageList.vue';
import MessageInput from '@/components/ai/MessageInput.vue';
import ConfigForm from '@/components/ai/ConfigForm.vue';
import WelcomeCard from '@/components/ai/WelcomeCard.vue';

defineOptions({
  name: 'AiChatPage'
});

const aiStore = useAiStore();
const message = useMessage();
const { t } = useI18n();

const showConfigModal = ref(false);
const isTestingConnection = ref(false);

// 测试连接
const handleTestConnection = async () => {
  isTestingConnection.value = true;
  try {
    const success = await aiStore.testConnection();
    if (success) {
      message.success(t('ai.chat.connectionSuccess'));
    } else {
      message.error(t('ai.chat.connectionFailed'));
    }
  } finally {
    isTestingConnection.value = false;
  }
};

// 发送消息
const handleSendMessage = async (content: string) => {
  await aiStore.sendMessage(content);
};

// 保存配置
const handleSaveConfig = async (newConfig: DeepSeekConfig) => {
  aiStore.updateConfig(newConfig);
  showConfigModal.value = false;
  message.success(t('ai.chat.configSaved'));

  // 如果有 API Key，尝试连接
  if (newConfig.apiKey) {
    await handleTestConnection();
  }
};

// 清空对话
const handleClearMessages = () => {
  aiStore.clearMessages();
  message.success(t('ai.chat.historyCleared'));
};

// 开始聊天
const handleStartChat = () => {
  try {
    // 添加一条初始欢迎消息
    aiStore.messages = [
      {
        id: `welcome-${Date.now()}`,
        role: 'assistant',
        content: t('ai.chat.welcomeMessage'),
        timestamp: Date.now()
      }
    ];
    message.success(t('ai.chat.chatStarted'));
  } catch (error) {
    message.error('处理开始聊天时出错:', error);
  }
};

// 处理新对话
const handleNewChat = () => {
  // 清空当前消息
  aiStore.clearMessages();
  message.success(t('ai.chat.newChatCreated'));
};

// 点击遮罩关闭弹窗
const maskClosableHandler = (e: MouseEvent) => {
  // 只有点击遮罩层时才关闭
  if ((e.target as HTMLElement).classList.contains('simple-modal-overlay')) {
    showConfigModal.value = false;
  }
};

onMounted(() => {
  // 加载配置
  aiStore.loadConfig();
  // 主页面不自动测试连接，由浮动助手负责
});
</script>

<template>
  <div class="ai-chat-page">
    <!-- 主聊天区域 -->
    <div class="chat-main">
      <!-- 头部工具栏 -->
      <NLayoutHeader class="chat-header" bordered>
        <div class="header-content">
          <div class="header-left">
            <i class="i-carbon-watson-health text-2xl text-blue-500" />
            <div class="header-info">
              <h1 class="chat-title">{{ t('ai.chat.title') }}</h1>
              <span class="chat-subtitle">
                {{ aiStore.isConnected ? t('ai.chat.connected') : t('ai.chat.disconnected') }} ·
                {{ aiStore.config.model }} · {{ aiStore.messages.length }} {{ t('ai.chat.messages') }}
              </span>
            </div>
          </div>

          <div class="header-actions">
            <NButton size="small" quaternary @click="handleNewChat">
              {{ t('ai.chat.newChat') }}
            </NButton>

            <NButton size="small" quaternary :loading="isTestingConnection" @click="showConfigModal = true">
              {{ t('ai.chat.config') }}
            </NButton>
            <NButton size="small" quaternary :disabled="aiStore.messages.length === 0" @click="handleClearMessages">
              {{ t('ai.chat.clear') }}
            </NButton>
          </div>
        </div>
      </NLayoutHeader>

      <!-- 聊天内容区域 -->
      <div class="chat-body">
        <!-- 配置提示 -->
        <div v-if="!aiStore.hasApiKey" class="config-prompt">
          <i class="i-carbon-warning text-4xl text-orange-500" />
          <h3>{{ t('ai.chat.configureFirst') }}</h3>
          <p>{{ t('ai.chat.configureDescription') }}</p>
          <NButton type="primary" size="large" @click="showConfigModal = true">{{ t('ai.chat.configureNow') }}</NButton>
        </div>

        <!-- 欢迎页面 -->
        <div v-else-if="aiStore.messages.length === 0" class="welcome-section">
          <WelcomeCard @start-chat="handleStartChat" @example="handleSendMessage" />
        </div>

        <!-- 聊天界面 -->
        <div v-else class="chat-interface">
          <MessageList :messages="aiStore.messages" class="message-container" />
        </div>
      </div>

      <!-- 输入框始终显示在底部 -->
      <div v-if="aiStore.hasApiKey" class="input-section">
        <MessageInput
          :loading="aiStore.isLoading"
          :disabled="!aiStore.canSendMessage"
          :placeholder="t('ai.chat.inputPlaceholder')"
          @send="handleSendMessage"
        />
      </div>
    </div>

    <!-- 配置弹窗 -->
    <Teleport to="body">
      <div v-if="showConfigModal" class="simple-modal-overlay" @click="maskClosableHandler">
        <div class="simple-modal" @click.stop>
          <div class="simple-modal-header">
            <h3>{{ t('ai.config.title') }}</h3>
            <button class="close-btn" @click="showConfigModal = false">
              <span class="close-icon">×</span>
            </button>
          </div>
          <div class="simple-modal-body">
            <ConfigForm
              :config="aiStore.config"
              :is-testing="isTestingConnection"
              @save="handleSaveConfig"
              @test="handleTestConnection"
            />
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<style scoped>
.ai-chat-page {
  height: 100%;
  background: rgb(var(--layout-bg-color));
  display: flex;
  flex-direction: column;
}

.chat-main {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: rgb(var(--container-bg-color));
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: relative;
}

.chat-header {
  background: linear-gradient(135deg, rgb(var(--container-bg-color)) 0%, rgb(var(--layout-bg-color)) 100%);
  border-bottom: 1px solid rgba(var(--base-text-color), 0.1);
  padding: 20px 30px;
  flex-shrink: 0;
  position: sticky;
  top: 0;
  z-index: 10;
  width: 100%;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.chat-title {
  font-size: 24px;
  font-weight: 700;
  color: rgb(var(--base-text-color));
  margin: 0;
  letter-spacing: -0.5px;
}

.chat-subtitle {
  font-size: 14px;
  color: rgba(var(--base-text-color), 0.65);
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-actions :deep(.n-button) {
  color: rgba(var(--base-text-color), 0.8);
  font-weight: 500;
}

.header-actions :deep(.n-button:hover) {
  color: rgb(var(--base-text-color));
}

.chat-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: linear-gradient(to bottom, rgb(var(--layout-bg-color)) 0%, rgb(var(--container-bg-color)) 100%);
  position: relative;
  overflow: hidden;
  min-height: 0;
}

.config-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 40px 30px;
  text-align: center;
  background: linear-gradient(135deg, rgba(var(--base-text-color), 0.05) 0%, rgba(var(--base-text-color), 0.1) 100%);
  margin: 20px;
  border-radius: 16px;
  border: 2px dashed rgba(var(--base-text-color), 0.2);
}

.config-prompt i {
  margin-bottom: 20px;
}

.config-prompt h3 {
  font-size: 28px;
  font-weight: 700;
  color: rgb(var(--base-text-color));
  margin: 0 0 12px 0;
  letter-spacing: -0.5px;
}

.config-prompt p {
  font-size: 16px;
  color: rgba(var(--base-text-color), 0.7);
  line-height: 1.6;
  margin: 0 0 32px 0;
  max-width: 400px;
}

.welcome-section {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 20px;
}

.chat-interface {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  min-height: 0;
}

.message-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  min-height: 0;
}

.input-section {
  position: sticky;
  bottom: 0;
  border-top: 1px solid rgba(var(--base-text-color), 0.1);
  background: rgb(var(--container-bg-color));
  z-index: 20;
  flex-shrink: 0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.config-modal {
  border-radius: 20px;
  overflow: hidden;
}

.config-modal :deep(.n-card) {
  border-radius: 20px;
}

.config-modal :deep(.n-card__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 30px;
  border-bottom: none;
}

.config-modal :deep(.n-card__content) {
  padding: 30px;
}

.modal-width {
  width: 600px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-main {
    margin: 10px;
    border-radius: 12px;
  }

  .chat-header {
    padding: 16px 20px;
  }

  .chat-title {
    font-size: 20px;
  }

  .header-actions {
    gap: 8px;
  }

  .config-prompt {
    margin: 20px;
    padding: 40px 20px 140px 20px;
  }

  .config-prompt h3 {
    font-size: 24px;
  }

  .config-prompt p {
    font-size: 14px;
  }

  .input-section {
    padding: 10px 0;
  }
}

/* 简易模态框样式 */
.simple-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.simple-modal {
  background: rgb(var(--container-bg-color));
  border-radius: 12px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  width: 400px;
  max-width: 90%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: modal-animation 0.3s ease;
}

.simple-modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.simple-modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.close-icon {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
  text-shadow: 0px 0px 3px rgba(0, 0, 0, 0.3);
  color: #ffffff;
}

.simple-modal-body {
  padding: 20px;
  overflow-y: auto;
  max-height: 80vh;
}

@keyframes modal-animation {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>

<script setup lang="ts">
import { computed, onActivated, onMounted } from 'vue';

interface Props {
  url: string;
}

const props = defineProps<Props>();

// 自动 decode url，确保 iframe src 是完整外链
const realUrl = computed(() => decodeURIComponent(props.url));

onMounted(() => {});

onActivated(() => {});
</script>

<template>
  <div class="h-full">
    <iframe id="iframePage" class="size-full" :src="realUrl"></iframe>
  </div>
</template>

<style scoped></style>

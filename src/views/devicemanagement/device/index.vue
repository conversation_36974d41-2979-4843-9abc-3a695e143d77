<script setup lang="tsx">
import { ref } from 'vue';
import { N<PERSON>utton, NPopconfirm, NTag } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { fetchBatchDeleteDevice, fetchDeleteDevice, fetchGetDevicePage, fetchUpdateDeviceStatus } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { useAuth } from '@/hooks/business/auth';
import { $t } from '@/locales';
import TableHeaderOperation from '@/components/advanced/table-header-operation.vue';
import NoPermission from '@/components/common/no-permission.vue';
import DeviceOperateDrawer from './modules/device-operate-drawer.vue';
import DeviceSearch from './modules/device-search.vue';
import DeviceDetailModal from './modules/device-detail-modal.vue';

defineOptions({
  name: 'DeviceManagePage'
});

const appStore = useAppStore();

const { hasAuth } = useAuth();

const {
  columns,
  columnChecks,
  data,
  loading,
  getData,
  getDataByPage,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetDevicePage,
  apiParams: {
    current: 1,
    size: 10,
    mobileNo: null,
    plateNo: null,
    deviceName: null,
    status: null,
    isOnline: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48,
      fixed: 'left'
    },
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'deviceId',
      title: $t('page.manage.device.deviceId'),
      align: 'center',
      minWidth: 120
    },
    {
      key: 'mobileNo',
      title: $t('page.manage.device.mobileNo'),
      align: 'center',
      minWidth: 120
    },
    {
      key: 'plateNo',
      title: $t('page.manage.device.plateNo'),
      align: 'center',
      minWidth: 100
    },
    {
      key: 'deviceName',
      title: $t('page.manage.device.deviceName'),
      align: 'center',
      minWidth: 120
    },
    {
      key: 'deviceType',
      title: $t('page.manage.device.deviceType'),
      align: 'center',
      minWidth: 100
    },
    {
      key: 'manufacturer',
      title: $t('page.manage.device.manufacturer'),
      align: 'center',
      minWidth: 100
    },
    {
      key: 'status',
      title: $t('page.manage.device.status'),
      align: 'center',
      width: 100,
      render: row => {
        const statusMap = {
          0: { type: 'error', text: $t('page.manage.device.statusDisabled') },
          1: { type: 'success', text: $t('page.manage.device.statusEnabled') },
          2: { type: 'warning', text: $t('page.manage.device.statusMaintenance') }
        };
        const status = statusMap[row.status as keyof typeof statusMap];
        return status ? <NTag type={status.type as any}>{status.text}</NTag> : '-';
      }
    },
    {
      key: 'isOnline',
      title: $t('page.manage.device.isOnline'),
      align: 'center',
      width: 100,
      render: row => {
        const onlineMap = {
          0: { type: 'error', text: $t('page.manage.device.offlineStatus') },
          1: { type: 'success', text: $t('page.manage.device.onlineStatus') }
        };
        const online = onlineMap[row.isOnline as keyof typeof onlineMap];
        return online ? <NTag type={online.type as any}>{online.text}</NTag> : '-';
      }
    },
    {
      key: 'lastOnlineTime',
      title: $t('page.manage.device.lastOnlineTime'),
      align: 'center',
      minWidth: 160
    },
    // 只有当用户有任意操作权限时才显示操作列
    ...(hasAuth('jt808:device:view') ||
    hasAuth('jt808:device:update') ||
    hasAuth('jt808:device:status') ||
    hasAuth('jt808:device:delete')
      ? [
          {
            key: 'operate',
            title: $t('common.operate'),
            align: 'center',
            width: 280,
            fixed: 'right',
            render: row => (
              <div class="flex-center gap-8px">
                {hasAuth('jt808:device:view') && (
                  <NButton type="primary" quaternary size="small" onClick={() => handleView(row)}>
                    {$t('common.view')}
                  </NButton>
                )}
                {hasAuth('jt808:device:update') && (
                  <NButton type="primary" quaternary size="small" onClick={() => handleEdit(row.id)}>
                    {$t('common.edit')}
                  </NButton>
                )}
                {hasAuth('jt808:device:status') && (
                  <NPopconfirm onPositiveClick={() => handleStatusChange(row.id, row.status === 1 ? 0 : 1)}>
                    {{
                      default: () => $t('page.manage.device.confirmStatusChange'),
                      trigger: () => (
                        <NButton type={row.status === 1 ? 'warning' : 'success'} quaternary size="small">
                          {row.status === 1
                            ? $t('page.manage.device.disableDevice')
                            : $t('page.manage.device.enableDevice')}
                        </NButton>
                      )
                    }}
                  </NPopconfirm>
                )}
                {hasAuth('jt808:device:delete') && (
                  <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
                    {{
                      default: () => $t('page.manage.device.confirmDelete'),
                      trigger: () => (
                        <NButton type="error" quaternary size="small">
                          {$t('common.delete')}
                        </NButton>
                      )
                    }}
                  </NPopconfirm>
                )}
              </div>
            )
          }
        ]
      : [])
  ]
});

// 先定义useTableOperate，确保变量可用
const { drawerVisible, operateType, editingData, handleAdd, checkedRowKeys, onDeleted, onBatchDeleted, openDrawer } =
  useTableOperate(data as any, getData);

// 详情弹窗相关
const { bool: detailVisible, setTrue: openDetail } = useBoolean();
const detailData = ref<Api.Device.DeviceVO | null>(null);

function handleView(row: Api.Device.DeviceVO) {
  detailData.value = row;
  openDetail();
}

function handleEdit(id: number) {
  // 这里需要将DeviceVO转换为符合TableData的类型
  const findItem = data.value.find(item => item.id === id);
  if (findItem) {
    // 使用类型断言来处理类型不匹配问题
    (editingData as any).value = findItem;
    operateType.value = 'edit';
    openDrawer();
  }
}

async function handleDelete(id: number) {
  const { error } = await fetchDeleteDevice(id);
  if (!error) {
    await onDeleted();
  }
}

async function handleBatchDelete() {
  const { error } = await fetchBatchDeleteDevice(checkedRowKeys.value.map(key => Number(key)));
  if (!error) {
    await onBatchDeleted();
  }
}

async function handleStatusChange(id: number, status: number) {
  const { error } = await fetchUpdateDeviceStatus(id, status);
  if (!error) {
    window.$message?.success($t('page.manage.device.statusUpdateSuccess'));
    await getData();
  }
}
</script>

<template>
  <div
    v-if="hasAuth('jt808:device:view')"
    class="min-h-500px flex-col-stretch gap-8px overflow-hidden lt-sm:overflow-auto"
  >
    <DeviceSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper" content-class="flex-col">
      <TableHeaderOperation
        v-model:columns="columnChecks"
        :checked-row-keys="checkedRowKeys"
        :loading="loading"
        add-auth="jt808:device:add"
        delete-auth="jt808:device:delete"
        @add="handleAdd"
        @delete="handleBatchDelete"
        @refresh="getData"
      />
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        remote
        striped
        size="small"
        class="sm:h-full"
        :data="data"
        :scroll-x="1200"
        :columns="columns as any"
        :flex-height="!appStore.isMobile"
        :loading="loading"
        :single-line="false"
        :row-key="row => row.id"
        :pagination="mobilePagination"
        @update:page="getDataByPage"
      />
    </NCard>
    <DeviceOperateDrawer
      v-model:visible="drawerVisible"
      :operate-type="operateType"
      :row-data="editingData as any"
      @submitted="getData"
    />
    <DeviceDetailModal v-model:visible="detailVisible" :data="detailData" />
  </div>
  <NoPermission v-else :description="$t('page.manage.device.noPermissionDesc')" />
</template>

<style scoped></style>

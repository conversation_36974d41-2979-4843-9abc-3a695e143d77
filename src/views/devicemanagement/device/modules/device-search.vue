<script setup lang="ts">
import { $t } from '@/locales';

defineOptions({
  name: 'DeviceSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const model = defineModel<Api.Device.DeviceQueryParams>('model', { required: true });

async function reset() {
  emit('reset');
}

async function search() {
  emit('search');
}

// 设备状态选项
const statusOptions = [
  { label: $t('page.manage.device.statusDisabled'), value: 0 },
  { label: $t('page.manage.device.statusEnabled'), value: 1 },
  { label: $t('page.manage.device.statusMaintenance'), value: 2 }
];

// 在线状态选项
const onlineOptions = [
  { label: $t('page.manage.device.offlineStatus'), value: 0 },
  { label: $t('page.manage.device.onlineStatus'), value: 1 }
];
</script>

<template>
  <NCard :title="$t('common.search')" :bordered="false" size="small" class="card-wrapper">
    <NForm :model="model" label-placement="left" :label-width="120">
      <NGrid responsive="screen" item-responsive>
        <!--
 <NFormItemGi span="24 s:12 m:6" :label="$t('page.manage.device.deviceId')" path="deviceId" class="pr-24px">
          <NInput
            v-model:value="model.deviceId"
            :placeholder="$t('page.manage.device.deviceIdPlaceholder')"
            clearable
          />
        </NFormItemGi> 
-->
        <NFormItemGi span="24 s:12 m:6" :label="$t('page.manage.device.mobileNo')" path="mobileNo" class="pr-24px">
          <NInput
            v-model:value="model.mobileNo"
            :placeholder="$t('page.manage.device.mobileNoPlaceholder')"
            clearable
          />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" :label="$t('page.manage.device.plateNo')" path="plateNo" class="pr-24px">
          <NInput v-model:value="model.plateNo" :placeholder="$t('page.manage.device.plateNoPlaceholder')" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" :label="$t('page.manage.device.deviceName')" path="deviceName" class="pr-24px">
          <NInput
            v-model:value="model.deviceName"
            :placeholder="$t('page.manage.device.deviceNamePlaceholder')"
            clearable
          />
        </NFormItemGi>
        <!--
 <NFormItemGi span="24 s:12 m:6" :label="$t('page.manage.device.deviceType')" path="deviceType" class="pr-24px">
          <NInput
            v-model:value="model.deviceType"
            :placeholder="$t('page.manage.device.deviceTypePlaceholder')"
            clearable
          />
        </NFormItemGi> 
-->
        <!--
 <NFormItemGi
          span="24 s:12 m:6"
          :label="$t('page.manage.device.manufacturer')"
          path="manufacturer"
          class="pr-24px"
        >
          <NInput
            v-model:value="model.manufacturer"
            :placeholder="$t('page.manage.device.manufacturerPlaceholder')"
            clearable
          />
        </NFormItemGi> 
-->
        <NFormItemGi span="24 s:12 m:6" :label="$t('page.manage.device.status')" path="status" class="pr-24px">
          <NSelect
            v-model:value="model.status"
            :placeholder="$t('page.manage.device.statusPlaceholder')"
            :options="statusOptions"
            clearable
          />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" :label="$t('page.manage.device.isOnline')" path="isOnline" class="pr-24px">
          <NSelect
            v-model:value="model.isOnline"
            :placeholder="$t('page.manage.device.onlineDesc')"
            :options="onlineOptions"
            clearable
          />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:6" class="pr-24px">
          <NSpace class="w-full" justify="end">
            <NButton @click="reset">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              {{ $t('common.reset') }}
            </NButton>
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </NSpace>
        </NFormItemGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>

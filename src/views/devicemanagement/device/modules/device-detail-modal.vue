<script setup lang="ts">
import { computed } from 'vue';
import { $t } from '@/locales';

defineOptions({
  name: 'DeviceDetailModal'
});

interface Props {
  /** the detail data */
  data?: Api.Device.DeviceVO | null;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const props = defineProps<Props>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const title = computed(() => $t('page.manage.device.deviceDetail'));

// 获取状态描述
function getStatusDesc(status?: number) {
  const statusMap = {
    0: $t('page.manage.device.statusDisabled'),
    1: $t('page.manage.device.statusEnabled'),
    2: $t('page.manage.device.statusMaintenance')
  };
  return status !== undefined ? statusMap[status as keyof typeof statusMap] || '-' : '-';
}

// 获取在线状态描述
function getOnlineDesc(isOnline?: number) {
  const onlineMap = {
    0: $t('page.manage.device.offlineStatus'),
    1: $t('page.manage.device.onlineStatus')
  };
  return isOnline !== undefined ? onlineMap[isOnline as keyof typeof onlineMap] || '-' : '-';
}

function closeModal() {
  visible.value = false;
}
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" class="w-800px">
    <template #header>
      <div class="flex items-center gap-12px">
        <SvgIcon icon="mdi:information-outline" class="text-icon" />
        {{ title }}
      </div>
    </template>

    <div v-if="data" class="space-y-16px">
      <!-- 基本信息 -->
      <NCard :title="$t('page.manage.device.title')" size="small">
        <NDescriptions :column="2" label-placement="left" label-style="width: 120px; font-weight: 600;">
          <NDescriptionsItem :label="$t('page.manage.device.deviceId')">
            {{ data.deviceId || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.device.mobileNo')">
            {{ data.mobileNo || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.device.plateNo')">
            {{ data.plateNo || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.device.deviceName')">
            {{ data.deviceName || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.device.deviceType')">
            {{ data.deviceType || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.device.manufacturer')">
            {{ data.manufacturer || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.device.deviceModel')">
            {{ data.deviceModel || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.device.protocolVersion')">
            {{ data.protocolVersion || '-' }}
          </NDescriptionsItem>
        </NDescriptions>
      </NCard>

      <!-- 状态信息 -->
      <NCard title="状态信息" size="small">
        <NDescriptions :column="2" label-placement="left" label-style="width: 120px; font-weight: 600;">
          <NDescriptionsItem :label="$t('page.manage.device.status')">
            <NTag :type="data.status === 1 ? 'success' : data.status === 0 ? 'error' : 'warning'">
              {{ getStatusDesc(data.status) }}
            </NTag>
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.device.isOnline')">
            <NTag :type="data.isOnline === 1 ? 'success' : 'error'">
              {{ getOnlineDesc(data.isOnline) }}
            </NTag>
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.device.lastOnlineTime')">
            {{ data.lastOnlineTime || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.device.registerTime')">
            {{ data.registerTime || '-' }}
          </NDescriptionsItem>
        </NDescriptions>
      </NCard>

      <!-- 系统信息 -->
      <NCard title="系统信息" size="small">
        <NDescriptions :column="2" label-placement="left" label-style="width: 120px; font-weight: 600;">
          <NDescriptionsItem :label="$t('page.manage.device.createTime')">
            {{ data.createTime || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.device.updateTime')">
            {{ data.updateTime || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.device.createBy')">
            {{ data.createBy || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.device.updateBy')">
            {{ data.updateBy || '-' }}
          </NDescriptionsItem>
        </NDescriptions>
      </NCard>
    </div>

    <template #footer>
      <NSpace justify="end">
        <NButton @click="closeModal">{{ $t('common.close') }}</NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>

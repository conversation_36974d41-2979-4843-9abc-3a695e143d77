<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import type { FormRules } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { fetchAddDevice, fetchUpdateDevice } from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'DeviceOperateDrawer'
});

export type OperateType = NaiveUI.TableOperateType;

interface Props {
  /** the type of operation */
  operateType: OperateType;
  /** the edit row data */
  rowData?: Api.Device.DeviceVO | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const { bool: submitLoading, setTrue: startSubmitLoading, setFalse: endSubmitLoading } = useBoolean();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: $t('page.manage.device.addDevice'),
    edit: $t('page.manage.device.editDevice')
  };
  return titles[props.operateType];
});

type Model = Pick<
  Api.Device.DeviceDTO,
  | 'id'
  | 'deviceId'
  | 'mobileNo'
  | 'plateNo'
  | 'deviceName'
  | 'deviceType'
  | 'manufacturer'
  | 'deviceModel'
  | 'protocolVersion'
  | 'status'
  | 'driverId'
>;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    id: undefined,
    deviceId: '',
    mobileNo: '',
    plateNo: '',
    deviceName: '',
    deviceType: '',
    manufacturer: '',
    deviceModel: '',
    protocolVersion: undefined,
    status: 1,
    driverId: undefined
  };
}

type RuleKey = Extract<keyof Model, 'deviceId' | 'mobileNo' | 'status'>;

const rules: Record<RuleKey, FormRules[string]> = {
  deviceId: defaultRequiredRule,
  mobileNo: defaultRequiredRule,
  status: defaultRequiredRule
};

// 设备状态选项
const statusOptions = [
  { label: $t('page.manage.device.statusDisabled'), value: 0 },
  { label: $t('page.manage.device.statusEnabled'), value: 1 },
  { label: $t('page.manage.device.statusMaintenance'), value: 2 }
];

// 获取当前登录用户名
function getCurrentUserName() {
  const authStore = useAuthStore();
  return authStore.userInfo.userName || 'system';
}

function handleInitModel() {
  Object.assign(model, createDefaultModel());

  if (props.operateType === 'edit' && props.rowData) {
    // 将DeviceVO转换为DeviceDTO格式
    const deviceData = {
      id: props.rowData.id,
      deviceId: props.rowData.deviceId,
      mobileNo: props.rowData.mobileNo,
      plateNo: props.rowData.plateNo,
      deviceName: props.rowData.deviceName,
      deviceType: props.rowData.deviceType,
      manufacturer: props.rowData.manufacturer,
      deviceModel: props.rowData.deviceModel,
      protocolVersion: props.rowData.protocolVersion,
      status: props.rowData.status,
      driverId: props.rowData.driverId
    };
    Object.assign(model, deviceData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();
  startSubmitLoading();

  const { operateType } = props;
  const currentUser = getCurrentUserName();

  // 根据操作类型构建不同的提交数据
  let submitData: any;

  if (operateType === 'add') {
    // 新增时包含创建人和更新人
    submitData = {
      ...model,
      createBy: currentUser,
      updateBy: currentUser
    };
  } else {
    // 更新时只包含更新人
    submitData = {
      ...model,
      updateBy: currentUser
    };
  }

  const functions: Record<OperateType, () => Promise<any>> = {
    add: () => fetchAddDevice(submitData),
    edit: () => fetchUpdateDevice(submitData)
  };

  const { error } = await functions[operateType]();

  if (!error) {
    window.$message?.success(
      $t(operateType === 'add' ? 'page.manage.device.addDeviceSuccess' : 'page.manage.device.editDeviceSuccess')
    );
    closeDrawer();
    emit('submitted');
  }

  endSubmitLoading();
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="640">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
        <NGrid :cols="24" :x-gap="18">
          <NFormItemGi :span="24" :label="$t('page.manage.device.deviceId')" path="deviceId">
            <NInput v-model:value="model.deviceId" :placeholder="$t('page.manage.device.deviceIdPlaceholder')" />
          </NFormItemGi>
          <NFormItemGi :span="24" :label="$t('page.manage.device.mobileNo')" path="mobileNo">
            <NInput v-model:value="model.mobileNo" :placeholder="$t('page.manage.device.mobileNoPlaceholder')" />
          </NFormItemGi>
          <NFormItemGi :span="24" :label="$t('page.manage.device.plateNo')" path="plateNo">
            <NInput v-model:value="model.plateNo" :placeholder="$t('page.manage.device.plateNoPlaceholder')" />
          </NFormItemGi>
          <NFormItemGi :span="24" :label="$t('page.manage.device.deviceName')" path="deviceName">
            <NInput v-model:value="model.deviceName" :placeholder="$t('page.manage.device.deviceNamePlaceholder')" />
          </NFormItemGi>
          <NFormItemGi :span="24" :label="$t('page.manage.device.deviceType')" path="deviceType">
            <NInput v-model:value="model.deviceType" :placeholder="$t('page.manage.device.deviceTypePlaceholder')" />
          </NFormItemGi>
          <NFormItemGi :span="24" :label="$t('page.manage.device.manufacturer')" path="manufacturer">
            <NInput
              v-model:value="model.manufacturer"
              :placeholder="$t('page.manage.device.manufacturerPlaceholder')"
            />
          </NFormItemGi>
          <NFormItemGi :span="24" :label="$t('page.manage.device.deviceModel')" path="deviceModel">
            <NInput v-model:value="model.deviceModel" :placeholder="$t('page.manage.device.deviceModelPlaceholder')" />
          </NFormItemGi>
          <NFormItemGi :span="24" :label="$t('page.manage.device.protocolVersion')" path="protocolVersion">
            <NInputNumber
              v-model:value="model.protocolVersion"
              :placeholder="$t('page.manage.device.protocolVersionPlaceholder')"
              class="w-full"
            />
          </NFormItemGi>
          <NFormItemGi :span="24" :label="$t('page.manage.device.status')" path="status">
            <NSelect
              v-model:value="model.status"
              :placeholder="$t('page.manage.device.statusPlaceholder')"
              :options="statusOptions"
            />
          </NFormItemGi>
        </NGrid>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" :loading="submitLoading" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>

<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
import { NButton, NSpace, NTag, NTooltip } from 'naive-ui';
import { CoordinateConverter } from '@/utils/coordinate-converter';

defineOptions({
  name: 'LocationMap'
});

interface Props {
  devices: Api.Location.DeviceLocationVO[];
  selectedDevice: Api.Location.DeviceLocationVO | null;
  autoFitView?: boolean; // 是否自动适应视野
}

const props = withDefaults(defineProps<Props>(), {
  autoFitView: true // 默认自动适应视野
});

interface Emits {
  (e: 'select-device', device: Api.Location.DeviceLocationVO): void;
}

const emit = defineEmits<Emits>();

// 地图相关状态
const mapContainer = ref<HTMLDivElement>();
const mapLoading = ref(false);
const mapError = ref<string>('');
let map: any = null;
const markers = new Map<string, any>();

// 从环境变量获取百度地图API Key
const BAIDU_MAP_API_KEY = import.meta.env.VITE_BAIDU_MAP_API_KEY;

// 创建汽车图标HTML
function createVehicleIconHTML(device: Api.Location.DeviceLocationVO): string {
  // 获取车辆状态
  const now = new Date();
  const deviceTime = new Date(device.deviceTime);
  const diffMinutes = (now.getTime() - deviceTime.getTime()) / (1000 * 60);
  const speed = device.speedKph || 0;

  let status = 'driving';
  if (diffMinutes <= 5) {
    status = speed > 0 ? 'driving' : 'parked';
  } else if (diffMinutes <= 30) {
    status = 'offline';
  } else {
    status = 'lost';
  }

  // 获取对应颜色
  let color = '#1afa29'; // 绿色
  switch (status) {
    case 'driving':
      color = '#1afa29'; // 绿色
      break;
    case 'parked':
      color = '#1296db'; // 蓝色
      break;
    case 'offline':
      color = '#FFD700'; // 黄色
      break;
    case 'lost':
      color = '#FF4444'; // 红色
      break;
    default:
      color = '#1afa29'; // 默认绿色
  }

  const rotation = device.direction || 0;

  return `
    <div style="
      width: 24px;
      height: 24px;
      transform: rotate(${rotation}deg);
      transition: transform 0.3s ease;
    ">
      <svg viewBox="0 0 1024 1024" width="24" height="24">
        <path d="M227.581524 252.875132a12.772415 50.526171 51.83 1 0 79.445388-62.450027 12.772415 50.526171 51.83 1 0-79.445388 62.450027Z" fill="#EFC825"/>
        <path d="M742.01325 231.82218a50.526171 12.772415 38.17 1 0 15.786623-20.082849 50.526171 12.772415 38.17 1 0-15.786623 20.082849Z" fill="#EFC825"/>
        <path d="M718.396697 136.176482c0 6.386207 0 12.772415-1.314807 18.782963a121.713601 121.713601 0 0 0-80.015423-92.412178 496.245884 496.245884 0 0 0-257.890082 0 121.713601 121.713601 0 0 0-79.639763 92.787837c0-6.386207-0.939148-12.772415-1.502638-18.782963A121.150112 121.150112 0 0 1 379.176385 17.468156 496.245884 496.245884 0 0 1 637.066467 17.092496a120.962282 120.962282 0 0 1 81.33023 119.083986zM717.08189 913.039834a121.713601 121.713601 0 0 1-79.639763 93.914815 496.245884 496.245884 0 0 1-257.890083 0 121.337941 121.337941 0 0 1-79.639763-93.914815 102.742808 102.742808 0 0 1-0.939148-26.483978c0-8.827993 1.502637-18.782963 2.066126-26.671808a120.774452 120.774452 0 0 0 79.076274 89.970393 496.245884 496.245884 0 0 0 257.890083 0 121.150112 121.150112 0 0 0 79.076274-89.970393c0 8.827993 1.314807 17.843815 1.878296 26.671808a102.367149 102.367149 0 0 1-1.878296 26.483978z" fill="${color}"/>
        <path d="M703.370327 534.938788q0 162.47263 13.148074 325.320919a121.150112 121.150112 0 0 1-79.451934 89.970393 496.245884 496.245884 0 0 1-257.890082 0A120.774452 120.774452 0 0 1 300.10011 860.259707q12.208926-162.66046 12.960245-325.320919v-1.314808-49.211363q0-164.538756-13.523733-329.077513a100.113193 100.113193 0 0 1 3.193103-12.396755 119.647475 119.647475 0 0 1 76.44666-80.391082 496.245884 496.245884 0 0 1 257.890082 0 119.647475 119.647475 0 0 1 76.44666 80.391082c1.314807 4.132252 2.253956 8.264504 3.193104 12.396755q-12.584585 164.538756-13.523734 329.077513v49.211363z" fill="${color}"/>
        <path d="M314.187333 475.021135l37.565926 39.256393a1209.43499 1209.43499 0 0 0-14.462882-180.316445 383.548105 383.548105 0 0 0-33.058015-112.697778z" fill="#273333"/>
        <path d="M363.210866 391.24912h6.198378v384.675083h-6.198378zM647.960586 391.24912h6.198378v384.675083h-6.198378z" fill="#EFC825"/>
        <path d="M363.210866 771.040633h290.948098v4.695741H363.210866z" fill="#EFC825"/>
      </svg>
    </div>
  `;
}

// 加载百度地图API
function loadBaiduMapAPI(): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!BAIDU_MAP_API_KEY) {
      const errorMsg = 'API Key未配置，请检查环境变量 VITE_BAIDU_MAP_API_KEY';
      reject(new Error(errorMsg));
      return;
    }

    // 检查是否已经加载
    if (window.BMap) {
      resolve();
      return;
    }

    // 检查是否已有加载中的脚本
    const existingScript = document.querySelector('script[src*="api.map.baidu.com"]');
    if (existingScript) {
      const checkReady = () => {
        if (window.BMap) {
          resolve();
        } else {
          setTimeout(checkReady, 100);
        }
      };
      checkReady();
      return;
    }

    // 创建script标签
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = `https://api.map.baidu.com/api?v=3.0&ak=${BAIDU_MAP_API_KEY}&callback=initBaiduMapSimple`;

    // 设置全局回调
    (window as any).initBaiduMapSimple = () => {
      resolve();
    };

    script.onerror = () => {
      const errorMsg = '百度地图API脚本加载失败，可能是网络问题或API Key无效';
      reject(new Error(errorMsg));
    };

    // 设置超时
    const timeout = setTimeout(() => {
      const errorMsg = '百度地图API加载超时（10秒）';
      reject(new Error(errorMsg));
    }, 10000);

    script.onload = () => {
      clearTimeout(timeout);
    };

    // 添加到页面
    document.head.appendChild(script);
  });
}

// 初始化地图
async function initMap() {
  if (!mapContainer.value) {
    return;
  }

  // 检查容器尺寸
  const rect = mapContainer.value.getBoundingClientRect();

  // 调试：检查整个高度传递链条
  let currentElement: HTMLElement | null = mapContainer.value;
  let level = 0;

  while (currentElement && level < 10) {
    currentElement = currentElement.parentElement;
    level += 1;
  }

  if (rect.width === 0 || rect.height === 0) {
    // 暂时不重试，看看调试信息
  }

  mapLoading.value = true;
  mapError.value = '';

  try {
    // 加载百度地图API
    await loadBaiduMapAPI();

    // 创建地图实例
    map = new window.BMap.Map(mapContainer.value);

    // 设置中心点和缩放级别（北京天安门）
    const point = new window.BMap.Point(116.404, 39.915);
    map.centerAndZoom(point, 12);

    // 启用地图功能
    map.enableScrollWheelZoom(true);
    map.enableDragging();
    map.enableDoubleClickZoom();

    // 添加控件
    map.addControl(new window.BMap.NavigationControl());
    map.addControl(new window.BMap.ScaleControl());

    // 强制重新计算地图尺寸
    setTimeout(() => {
      if (map) {
        map.getViewport();
      }
    }, 100);

    // 如果有设备数据，立即添加标记
    if (props.devices.length > 0) {
      addDeviceMarkers();
      fitView();
    }
  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : '地图初始化失败';
    mapError.value = errorMsg;

    // 显示错误信息给用户
    window.$message?.error(errorMsg);
  } finally {
    mapLoading.value = false;
  }
}

// 添加设备标记
function addDeviceMarkers() {
  if (!map || !props.devices.length) return;

  // 清除现有标记
  clearMarkers();

  props.devices.forEach(device => {
    if (device.lng && device.lat) {
      // 转换WGS84坐标到BD09坐标系（适配百度地图）
      const convertedPoint = CoordinateConverter.wgs84ToBd09(device.lng, device.lat);
      const point = new window.BMap.Point(convertedPoint.lng, convertedPoint.lat);

      // 创建自定义汽车图标
      const iconDiv = document.createElement('div');
      iconDiv.innerHTML = createVehicleIconHTML(device);
      iconDiv.style.cursor = 'pointer';

      // 创建自定义覆盖物
      const customOverlay = new window.BMap.Label('', {
        position: point,
        offset: new window.BMap.Size(-12, -12)
      });

      customOverlay.setContent(iconDiv.innerHTML);
      customOverlay.setStyle({
        border: 'none',
        background: 'transparent',
        fontSize: '0'
      });

      // 添加点击事件
      customOverlay.addEventListener('click', () => {
        emit('select-device', device);

        // 创建信息窗口
        const infoContent = `
          <div style="padding: 8px; min-width: 200px;">
            <div style="font-weight: bold; margin-bottom: 8px; color: #1890ff;">
              🚗 ${device.plateNo || device.deviceId}
            </div>
            <div style="font-size: 12px; line-height: 1.5;">
              <div><strong>速度:</strong> ${(device.speedKph || 0).toFixed(1)} km/h</div>
              <div><strong>方向:</strong> ${device.direction || 0}°</div>
              <div><strong>原始坐标(WGS84):</strong> ${device.lng?.toFixed(6)}, ${device.lat?.toFixed(6)}</div>
              <div><strong>地图坐标(BD09):</strong> ${convertedPoint.lng.toFixed(6)}, ${convertedPoint.lat.toFixed(6)}</div>
              ${device.address ? `<div><strong>地址:</strong> ${device.address}</div>` : ''}
              <div><strong>更新:</strong> ${new Date(device.deviceTime).toLocaleString()}</div>
            </div>
          </div>
        `;
        const infoWindow = new window.BMap.InfoWindow(infoContent);
        map.openInfoWindow(infoWindow, point);
      });

      // 添加到地图
      map.addOverlay(customOverlay);

      // 保存标记引用
      markers.set(device.deviceId, customOverlay);
    }
  });
}

// 清除标记
function clearMarkers() {
  markers.forEach(marker => {
    if (map) {
      map.removeOverlay(marker);
    }
  });
  markers.clear();
}

// 定位到设备
function locateToDevice(device: Api.Location.DeviceLocationVO) {
  if (!map || !device.lng || !device.lat) return;

  // 转换WGS84坐标到BD09坐标系
  const convertedPoint = CoordinateConverter.wgs84ToBd09(device.lng, device.lat);
  const point = new window.BMap.Point(convertedPoint.lng, convertedPoint.lat);
  map.panTo(point);
  map.setZoom(15);
}

// 适应地图视野
function fitView() {
  if (!map || markers.size === 0) return;

  const points: any[] = [];
  markers.forEach(marker => {
    points.push(marker.getPosition());
  });

  if (points.length > 0) {
    map.setViewport(points);
  }
}

// 手动适应视野（用户主动点击）
function handleFitView() {
  fitView();
}

// 清理地图
function destroyMap() {
  if (map) {
    clearMarkers();
    map = null;
  }
}

// 监听设备数据变化
watch(
  () => props.devices,
  (newDevices, oldDevices) => {
    nextTick(() => {
      addDeviceMarkers();

      // 只有在初次加载或者明确需要自动适应视野时才调用fitView
      const isInitialLoad = !oldDevices || oldDevices.length === 0;
      if (props.autoFitView && isInitialLoad && newDevices.length > 0) {
        fitView();
      }
    });
  },
  { deep: true }
);

// 监听选中设备变化
watch(
  () => props.selectedDevice,
  device => {
    if (device) {
      locateToDevice(device);
    }
  }
);

// 窗口大小改变时重新计算地图尺寸
function handleResize() {
  if (map) {
    setTimeout(() => {
      map.getViewport();
    }, 100);
  }
}

onMounted(() => {
  // 延迟初始化，确保DOM完全渲染
  setTimeout(() => {
    nextTick(() => {
      initMap();
    });
  }, 200);

  // 监听窗口大小改变
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  destroyMap();
  window.removeEventListener('resize', handleResize);
});
</script>

<template>
  <div class="map-card-wrapper map-height h-full w-full flex flex-col">
    <!-- 地图工具栏 -->
    <div class="mb-12px flex items-center justify-between">
      <NSpace>
        <NTag type="info" size="small">
          <template #icon>
            <SvgIcon icon="mdi:map-marker-multiple" />
          </template>
          设备: {{ devices.length }}
        </NTag>
      </NSpace>

      <NSpace>
        <NTooltip>
          <template #trigger>
            <NButton size="small" quaternary @click="handleFitView">
              <template #icon>
                <SvgIcon icon="mdi:fit-to-screen-outline" />
              </template>
            </NButton>
          </template>
          适应视野
        </NTooltip>
      </NSpace>
    </div>

    <!-- 地图容器 -->
    <div class="map-wrapper relative overflow-hidden border border-gray-200 rounded-6px">
      <div ref="mapContainer" class="map-container">
        <!-- 地图将在这里渲染 -->
      </div>

      <!-- 地图加载状态 -->
      <div v-if="mapLoading" class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80">
        <div class="text-center">
          <div class="mb-8px text-24px">🗺️</div>
          <div class="text-14px text-gray-600">正在加载百度地图...</div>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="mapError" class="absolute inset-0 flex items-center justify-center bg-white">
        <div class="text-center">
          <div class="mb-16px text-48px">❌</div>
          <div class="mb-8px text-16px font-600">地图加载失败</div>
          <div class="mb-16px text-12px text-gray-500">{{ mapError }}</div>
          <NButton type="primary" @click="initMap">重新加载</NButton>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.map-card-wrapper {
  padding: 16px;
  background: white;
  border-radius: 6px;
}

/* 添加这个类来替代内联样式 */
.map-height {
  height: 100%;
}

/* 确保地图容器有正确的尺寸 */
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 地图包装器 - 关键修复 */
.map-wrapper {
  flex: 1;
  min-height: 0;
  height: 100%;
}

/* 地图外层容器 */
.flex-1-hidden {
  flex: 1 1 0%;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

/* 百度地图容器样式 */
:deep(.BMap_mask) {
  background: transparent !important;
}

:deep(.anchorBL) {
  display: none !important;
}
</style>

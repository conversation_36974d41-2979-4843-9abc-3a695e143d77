<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { NButton, NDatePicker, NEmpty, NModal, NSlider, NSpin } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { fetchGetDeviceTrack } from '@/service/api/location';
import TrackReplayMap from './track-replay-map.vue';

defineOptions({
  name: 'TrackPlayer'
});

interface Props {
  device: Api.Location.DeviceLocationVO | null;
}

const props = defineProps<Props>();

const visible = defineModel<boolean>('visible', { default: false });

// 数据状态
const { bool: loading, setTrue: startLoading, setFalse: stopLoading } = useBoolean();
const trackData = ref<Api.Location.DeviceLocationVO[]>([]);

// 时间选择
const dateRange = ref<[number, number] | null>(null);

// 播放控制
const { bool: playing, setTrue: startPlay, setFalse: stopPlay } = useBoolean();
const currentIndex = ref(0);
const playSpeed = ref(1); // 播放速度倍数
let playTimer: NodeJS.Timeout | null = null;

// 计算属性
const title = computed(() => {
  return props.device ? `${props.device.plateNo || props.device.deviceId} - 轨迹回放` : '轨迹回放';
});

const currentPoint = computed(() => {
  return trackData.value[currentIndex.value] || null;
});

const trackStats = computed(() => {
  if (!trackData.value.length) return null;

  const totalDistance = calculateTotalDistance();
  const duration = calculateDuration();
  const maxSpeed = Math.max(...trackData.value.map(p => p.speedKph || 0));

  return {
    totalPoints: trackData.value.length,
    totalDistance: totalDistance.toFixed(2),
    duration: formatDuration(duration),
    maxSpeed: maxSpeed.toFixed(1)
  };
});

// 获取轨迹数据
async function getTrackData() {
  if (!props.device || !dateRange.value) return;
  startLoading();
  try {
    const [startTime, endTime] = dateRange.value;
    const params: Api.Location.DeviceTrackParams = {
      deviceId: props.device.deviceId,
      startTime: new Date(startTime).toISOString().slice(0, 19).replace('T', ' '),
      endTime: new Date(endTime).toISOString().slice(0, 19).replace('T', ' '),
      limit: 1000
    };
    const { data, error } = await fetchGetDeviceTrack(params);
    if (!error && data && data.length > 0) {
      // 过滤有效的位置数据
      const validData = data.filter(item => item.lat && item.lng && item.deviceTime);
      if (validData.length > 0) {
        trackData.value = validData.sort((a, b) => new Date(a.deviceTime).getTime() - new Date(b.deviceTime).getTime());
        currentIndex.value = 0;
        window.$message?.success(`成功获取 ${trackData.value.length} 个轨迹点`);
      } else {
        window.$message?.warning('获取的轨迹数据中没有有效的位置信息');
        trackData.value = [];
      }
    } else {
      window.$message?.warning('未获取到轨迹数据，请检查时间范围或设备状态');
      trackData.value = [];
    }
  } catch {
    window.$message?.error('获取轨迹数据失败，请稍后重试');
    trackData.value = [];
  } finally {
    stopLoading();
  }
}

// 计算两点间距离（单位：公里）
function calculateDistance(coords: { lat1: number; lng1: number; lat2: number; lng2: number }): number {
  const { lat1, lng1, lat2, lng2 } = coords;
  const R = 6371; // 地球半径（公里）
  const dLat = ((lat2 - lat1) * Math.PI) / 180;
  const dLng = ((lng2 - lng1) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) * Math.cos((lat2 * Math.PI) / 180) * Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

// 计算总距离（简单的直线距离计算）
function calculateTotalDistance(): number {
  if (trackData.value.length < 2) return 0;

  let totalDistance = 0;
  for (let i = 1; i < trackData.value.length; i += 1) {
    const prev = trackData.value[i - 1];
    const curr = trackData.value[i];
    totalDistance += calculateDistance({
      lat1: prev.lat,
      lng1: prev.lng,
      lat2: curr.lat,
      lng2: curr.lng
    });
  }
  return totalDistance;
}

// 计算总时长（分钟）
function calculateDuration(): number {
  if (trackData.value.length < 2) return 0;

  const startTime = new Date(trackData.value[0].deviceTime).getTime();
  const endTime = new Date(trackData.value[trackData.value.length - 1].deviceTime).getTime();
  return (endTime - startTime) / (1000 * 60);
}

// 格式化时长
function formatDuration(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const mins = Math.floor(minutes % 60);
  return hours > 0 ? `${hours}小时${mins}分钟` : `${mins}分钟`;
}

// 播放控制
function togglePlay() {
  if (playing.value) {
    stopPlayback();
  } else {
    startPlayback();
  }
}

function startPlayback() {
  if (!trackData.value.length) return;

  startPlay();
  playTimer = setInterval(() => {
    if (currentIndex.value < trackData.value.length - 1) {
      currentIndex.value += 1;
    } else {
      stopPlayback();
    }
  }, 1000 / playSpeed.value);
}

function stopPlayback() {
  stopPlay();
  if (playTimer) {
    clearInterval(playTimer);
    playTimer = null;
  }
}

// 重置播放
function resetPlay() {
  stopPlayback();
  currentIndex.value = 0;
}

// 下一个轨迹点
function handleStepForward() {
  if (currentIndex.value < trackData.value.length - 1) {
    currentIndex.value += 1;
  }
}

// 上一个轨迹点
function handleStepBackward() {
  if (currentIndex.value > 0) {
    currentIndex.value -= 1;
  }
}

// 设置默认时间范围（最近24小时）
function setDefaultDateRange() {
  const now = new Date();
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  dateRange.value = [yesterday.getTime(), now.getTime()];
}

// 关闭弹窗
function closeModal() {
  stopPlayback();
  trackData.value = [];
  currentIndex.value = 0;
}

// 处理弹窗关闭事件（包括X按钮和ESC键）
function handleModalClose(show: boolean) {
  if (!show) {
    closeModal();
  }
}

// 监听弹窗显示状态
watch(visible, show => {
  if (show && props.device) {
    setDefaultDateRange();
  } else if (!show) {
    stopPlayback();
  }
});

// 监听播放速度变化，重新启动定时器
watch(playSpeed, () => {
  if (playing.value) {
    // 如果正在播放，重新启动定时器以应用新的播放速度
    stopPlayback();
    startPlayback();
  }
});
</script>

<template>
  <NModal
    v-model:show="visible"
    :title="title"
    preset="card"
    class="h-800px w-1200px"
    :mask-closable="true"
    :closable="true"
    @update:show="handleModalClose"
  >
    <template #header>
      <div class="flex items-center gap-12px">
        <SvgIcon icon="mdi:map-marker-path" class="text-primary" />
        {{ title }}
      </div>
    </template>

    <div class="h-full flex flex-col gap-16px">
      <!-- 时间选择 -->
      <div>
        <div class="mb-8px text-14px font-600">选择时间范围</div>
        <div class="flex items-center gap-12px">
          <NDatePicker
            v-model:value="dateRange"
            type="datetimerange"
            clearable
            class="flex-1"
            :shortcuts="{
              最近1小时: () => [Date.now() - 3600000, Date.now()],
              最近6小时: () => [Date.now() - 6 * 3600000, Date.now()],
              最近24小时: () => [Date.now() - 24 * 3600000, Date.now()],
              最近3天: () => [Date.now() - 3 * 24 * 3600000, Date.now()]
            }"
          />
          <NButton type="primary" :loading="loading" :disabled="!dateRange" @click="getTrackData">查询轨迹</NButton>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div v-if="trackData.length > 0" class="min-h-0 flex flex-1 gap-16px">
        <!-- 左侧地图区域 -->
        <div class="min-h-0 flex-1">
          <TrackReplayMap :track-data="trackData" :current-index="currentIndex" :playing="playing" />
        </div>

        <!-- 右侧控制面板 -->
        <div class="w-300px flex flex-col gap-16px">
          <!-- 轨迹统计信息 -->
          <div v-if="trackStats" class="rounded-6px bg-gray-50 p-12px">
            <div class="mb-8px text-center text-14px font-600">轨迹统计</div>
            <div class="grid grid-cols-2 gap-12px text-center">
              <div>
                <div class="text-16px text-primary font-600">{{ trackStats.totalPoints }}</div>
                <div class="text-11px text-gray-500">轨迹点</div>
              </div>
              <div>
                <div class="text-16px text-success font-600">{{ trackStats.totalDistance }}</div>
                <div class="text-11px text-gray-500">总距离(km)</div>
              </div>
              <div>
                <div class="text-16px text-warning font-600">{{ trackStats.duration }}</div>
                <div class="text-11px text-gray-500">总时长</div>
              </div>
              <div>
                <div class="text-16px text-error font-600">{{ trackStats.maxSpeed }}</div>
                <div class="text-11px text-gray-500">最高速度(km/h)</div>
              </div>
            </div>
          </div>

          <!-- 播放控制 -->
          <div class="flex flex-col gap-12px">
            <div class="text-center text-14px font-600">轨迹回放</div>

            <!-- 进度条 -->
            <div>
              <NSlider v-model:value="currentIndex" :min="0" :max="trackData.length - 1" :step="1" :tooltip="false" />
              <div class="mt-4px flex justify-between text-10px text-gray-500">
                <span>{{ trackData[0]?.deviceTime.slice(11, 19) }}</span>
                <span>{{ currentIndex + 1 }} / {{ trackData.length }}</span>
                <span>{{ trackData[trackData.length - 1]?.deviceTime.slice(11, 19) }}</span>
              </div>
            </div>

            <!-- 播放控制按钮 -->
            <div class="grid grid-cols-2 gap-8px">
              <NButton :type="playing ? 'warning' : 'primary'" size="small" @click="togglePlay">
                <template #icon>
                  <SvgIcon :icon="playing ? 'mdi:pause' : 'mdi:play'" />
                </template>
                {{ playing ? '暂停' : '播放' }}
              </NButton>

              <NButton size="small" @click="resetPlay">
                <template #icon>
                  <SvgIcon icon="mdi:restart" />
                </template>
                重置
              </NButton>

              <NButton :disabled="currentIndex <= 0" size="small" @click="handleStepBackward">
                <template #icon>
                  <SvgIcon icon="mdi:step-backward" />
                </template>
                上一点
              </NButton>

              <NButton :disabled="currentIndex >= trackData.length - 1" size="small" @click="handleStepForward">
                <template #icon>
                  <SvgIcon icon="mdi:step-forward" />
                </template>
                下一点
              </NButton>
            </div>

            <!-- 播放速度控制 -->
            <div class="flex flex-col gap-8px">
              <div class="text-center text-12px text-gray-500">
                播放速度:
                <span
                  class="font-600"
                  :class="{
                    'text-blue-600': playSpeed === 1,
                    'text-green-600': playSpeed < 1,
                    'text-orange-600': playSpeed > 1 && playSpeed <= 2,
                    'text-red-600': playSpeed > 2
                  }"
                >
                  {{ playSpeed }}x
                </span>
              </div>

              <!-- 快速速度选择按钮 -->
              <div class="grid grid-cols-4 gap-4px">
                <NButton size="tiny" :type="playSpeed === 0.5 ? 'primary' : 'default'" @click="playSpeed = 0.5">
                  0.5x
                </NButton>
                <NButton size="tiny" :type="playSpeed === 1 ? 'primary' : 'default'" @click="playSpeed = 1">1x</NButton>
                <NButton size="tiny" :type="playSpeed === 2 ? 'primary' : 'default'" @click="playSpeed = 2">2x</NButton>
                <NButton size="tiny" :type="playSpeed === 5 ? 'primary' : 'default'" @click="playSpeed = 5">5x</NButton>
              </div>

              <NSlider v-model:value="playSpeed" :min="0.5" :max="5" :step="0.5" />
            </div>
          </div>

          <!-- 当前点信息 -->
          <div v-if="currentPoint" class="rounded-6px bg-blue-50 p-12px">
            <div class="mb-8px text-center text-12px font-600">当前位置</div>
            <div class="text-11px space-y-6px">
              <div class="flex justify-between">
                <span class="text-gray-500">时间:</span>
                <span>{{ currentPoint.deviceTime.slice(11, 19) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">速度:</span>
                <span>{{ currentPoint.speedKph?.toFixed(1) || 0 }} km/h</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">经度:</span>
                <span>{{ currentPoint.lng?.toFixed(6) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">纬度:</span>
                <span>{{ currentPoint.lat?.toFixed(6) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">方向:</span>
                <span>{{ currentPoint.direction || 0 }}°</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!loading" class="flex flex-1 items-center justify-center">
        <NEmpty description="请选择时间范围并查询轨迹数据">
          <template #icon>
            <SvgIcon icon="mdi:map-marker-path" class="text-40px text-gray-400" />
          </template>
        </NEmpty>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex flex-1 items-center justify-center">
        <NSpin size="large" />
      </div>
    </div>
  </NModal>
</template>

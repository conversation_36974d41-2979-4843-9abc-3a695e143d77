<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
import { NButton, NSpace, NTag } from 'naive-ui';

defineOptions({
  name: 'TrackReplayMap'
});

interface Props {
  trackData: Api.Location.DeviceLocationVO[];
  currentIndex: number;
  playing: boolean;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'fit-view'): void;
}

const emit = defineEmits<Emits>();

// 地图相关
const mapContainer = ref<HTMLDivElement>();
let map: any = null;
let trackPolyline: any = null;
let currentMarker: any = null;
let startMarker: any = null;
let endMarker: any = null;

// 从环境变量获取百度地图API Key
const BAIDU_MAP_API_KEY = import.meta.env.VITE_BAIDU_MAP_API_KEY;

// 加载百度地图API
function loadBaiduMapAPI(): Promise<void> {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载
    if (window.BMap) {
      resolve();
      return;
    }

    // 检查是否正在加载
    if ((window as any).baiduMapLoading) {
      // 等待加载完成
      const checkLoaded = () => {
        if (window.BMap) {
          resolve();
        } else {
          setTimeout(checkLoaded, 100);
        }
      };
      checkLoaded();
      return;
    }

    // 标记正在加载
    (window as any).baiduMapLoading = true;

    // 创建script标签
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = `https://api.map.baidu.com/api?v=3.0&ak=${BAIDU_MAP_API_KEY}&callback=initBaiduMapTrack`;

    // 设置全局回调
    (window as any).initBaiduMapTrack = () => {
      (window as any).baiduMapLoading = false;
      resolve();
    };

    // 错误处理
    script.onerror = () => {
      (window as any).baiduMapLoading = false;
      reject(new Error('百度地图API加载失败'));
    };

    document.head.appendChild(script);
  });
}

// 初始化地图
async function initMap() {
  if (!mapContainer.value || map) return; // 避免重复初始化

  try {
    await loadBaiduMapAPI();

    // 创建地图实例
    map = new window.BMap.Map(mapContainer.value);

    // 设置默认中心点和缩放级别（北京市区）
    const centerPoint = new window.BMap.Point(116.404, 39.915);
    map.centerAndZoom(centerPoint, 11);

    // 启用地图功能
    map.enableScrollWheelZoom(true);
    map.enableDragging();
    map.enableDoubleClickZoom();

    // 添加控件
    map.addControl(new window.BMap.NavigationControl());
    map.addControl(new window.BMap.ScaleControl());

    // 如果有轨迹数据，立即显示
    if (props.trackData.length > 0) {
      showTrackData();
    }
  } catch {
    // 显示用户友好的错误信息
    window.$message?.error('地图初始化失败，请刷新页面重试');
  }
}

// 显示轨迹数据
function showTrackData() {
  if (!map || !props.trackData.length) return;

  // 清除现有覆盖物
  clearOverlays();

  // 创建轨迹点数组
  const points = props.trackData.map(item => new window.BMap.Point(item.lng, item.lat));

  // 创建轨迹线
  trackPolyline = new window.BMap.Polyline(points, {
    strokeColor: '#1890ff',
    strokeWeight: 4,
    strokeOpacity: 0.8
  });
  map.addOverlay(trackPolyline);

  // 添加起点标记
  if (points.length > 0) {
    // 创建起点标记（使用HTML方式避免SVG编码问题）
    const startIconDiv = document.createElement('div');
    startIconDiv.innerHTML = `
      <div style="
        width: 20px;
        height: 20px;
        background: #52c41a;
        border: 2px solid #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 10px;
        font-weight: bold;
        box-shadow: 0 2px 6px rgba(0,0,0,0.3);
      ">起</div>
    `;

    startMarker = new window.BMap.Label('', {
      position: points[0],
      offset: new window.BMap.Size(-10, -10)
    });
    startMarker.setContent(startIconDiv.innerHTML);
    startMarker.setStyle({
      border: 'none',
      background: 'transparent',
      fontSize: '0'
    });
    map.addOverlay(startMarker);

    // 添加终点标记
    if (points.length > 1) {
      const endIconDiv = document.createElement('div');
      endIconDiv.innerHTML = `
        <div style="
          width: 20px;
          height: 20px;
          background: #f5222d;
          border: 2px solid #fff;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 10px;
          font-weight: bold;
          box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        ">终</div>
      `;

      endMarker = new window.BMap.Label('', {
        position: points[points.length - 1],
        offset: new window.BMap.Size(-10, -10)
      });
      endMarker.setContent(endIconDiv.innerHTML);
      endMarker.setStyle({
        border: 'none',
        background: 'transparent',
        fontSize: '0'
      });
      map.addOverlay(endMarker);
    }

    // 始终以轨迹的第一个点（起点）为中心
    if (points.length === 1) {
      // 只有一个点，使用较高的缩放级别
      map.centerAndZoom(points[0], 16);
    } else {
      // 多个点，以起点为中心，使用中等缩放级别
      map.centerAndZoom(points[0], 14);
    }
  }

  // 显示当前播放点
  showCurrentPoint();
}

// 显示当前播放点
function showCurrentPoint() {
  if (!map || !props.trackData.length || props.currentIndex < 0 || props.currentIndex >= props.trackData.length) {
    if (currentMarker) {
      map.removeOverlay(currentMarker);
      currentMarker = null;
    }
    return;
  }

  const currentPoint = props.trackData[props.currentIndex];
  const point = new window.BMap.Point(currentPoint.lng, currentPoint.lat);

  // 移除之前的当前点标记
  if (currentMarker) {
    map.removeOverlay(currentMarker);
  }

  // 创建当前播放点标记（使用自定义HTML）
  const iconDiv = document.createElement('div');
  iconDiv.innerHTML = `
    <div style="
      width: 16px;
      height: 16px;
      background: #ff4d4f;
      border: 2px solid #fff;
      border-radius: 50%;
      box-shadow: 0 2px 8px rgba(255, 77, 79, 0.6);
      animation: pulse 1.5s infinite;
      position: relative;
    ">
      <style>
        @keyframes pulse {
          0% { transform: scale(1); box-shadow: 0 2px 8px rgba(255, 77, 79, 0.6); }
          50% { transform: scale(1.3); box-shadow: 0 2px 12px rgba(255, 77, 79, 0.8); }
          100% { transform: scale(1); box-shadow: 0 2px 8px rgba(255, 77, 79, 0.6); }
        }
      </style>
    </div>
  `;

  currentMarker = new window.BMap.Label('', {
    position: point,
    offset: new window.BMap.Size(-8, -8)
  });

  currentMarker.setContent(iconDiv.innerHTML);
  currentMarker.setStyle({
    border: 'none',
    background: 'transparent',
    fontSize: '0'
  });

  map.addOverlay(currentMarker);

  // 如果正在播放，智能跟随当前点
  if (props.playing) {
    // 检查当前点是否在可视区域内
    const bounds = map.getBounds();
    if (!bounds.containsPoint(point)) {
      // 如果当前点不在可视区域内，平滑移动到该点
      map.panTo(point);
    }
  }
}

// 清除所有覆盖物
function clearOverlays() {
  if (!map) return;

  if (trackPolyline) {
    map.removeOverlay(trackPolyline);
    trackPolyline = null;
  }
  if (currentMarker) {
    map.removeOverlay(currentMarker);
    currentMarker = null;
  }
  if (startMarker) {
    map.removeOverlay(startMarker);
    startMarker = null;
  }
  if (endMarker) {
    map.removeOverlay(endMarker);
    endMarker = null;
  }
}

// 适应视野（以第一个点为中心）
function fitView() {
  if (!map || !props.trackData.length) return;

  const points = props.trackData.map(item => new window.BMap.Point(item.lng, item.lat));

  if (points.length === 1) {
    // 只有一个点，以该点为中心
    map.centerAndZoom(points[0], 16);
  } else {
    // 多个点，先以第一个点为中心，然后调整缩放以显示更多轨迹
    map.centerAndZoom(points[0], 14);

    // 计算轨迹范围，调整缩放级别以显示更多轨迹点
    setTimeout(() => {
      const bounds = new window.BMap.Bounds();
      points.forEach(point => bounds.extend(point));

      // 获取合适的缩放级别，但不要太小
      const viewport = map.getViewport(points);
      const zoom = Math.max(10, Math.min(16, viewport.zoom));

      // 保持以第一个点为中心，只调整缩放级别
      map.centerAndZoom(points[0], zoom);
    }, 300);
  }
}

// 手动适应视野
function handleFitView() {
  fitView();
  emit('fit-view');
}

// 监听轨迹数据变化
watch(
  () => props.trackData,
  newData => {
    nextTick(() => {
      if (newData.length > 0 && map) {
        showTrackData();
      }
    });
  },
  { deep: true }
);

// 监听当前播放点变化
watch(
  () => props.currentIndex,
  () => {
    nextTick(() => {
      showCurrentPoint();
    });
  }
);

// 清理地图
function destroyMap() {
  if (map) {
    clearOverlays();
    map = null;
  }
}

onMounted(() => {
  // 组件挂载时初始化地图
  setTimeout(() => {
    nextTick(() => {
      initMap();
    });
  }, 100);
});

onUnmounted(() => {
  destroyMap();
});
</script>

<template>
  <div class="track-replay-map h-full w-full flex flex-col">
    <!-- 地图工具栏 -->
    <div class="mb-8px flex items-center justify-between">
      <NSpace size="small">
        <NTag v-if="trackData.length > 0" type="info" size="small">
          <template #icon>
            <SvgIcon icon="mdi:map-marker-path" />
          </template>
          轨迹: {{ trackData.length }} 点
        </NTag>
        <NTag v-if="currentIndex >= 0" type="warning" size="small">
          当前: {{ currentIndex + 1 }} / {{ trackData.length }}
        </NTag>
      </NSpace>

      <NButton size="tiny" :disabled="!trackData.length" @click="handleFitView">
        <template #icon>
          <SvgIcon icon="mdi:fit-to-page-outline" />
        </template>
        适应视野
      </NButton>
    </div>

    <!-- 地图容器 -->
    <div class="relative flex-1 overflow-hidden rounded-6px bg-gray-100">
      <!-- 地图容器（始终存在，但内容根据数据状态显示） -->
      <div ref="mapContainer" class="h-full w-full"></div>

      <!-- 无数据提示 -->
      <div v-if="!trackData.length" class="absolute inset-0 z-10 flex items-center justify-center bg-gray-50">
        <div class="text-center text-gray-400">
          <SvgIcon icon="mdi:map-marker-path" class="mb-8px text-48px" />
          <div class="text-14px">请先查询轨迹数据</div>
          <div class="mt-4px text-12px">选择时间范围后点击"查询轨迹"</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.track-replay-map {
  min-height: 400px;
}
</style>

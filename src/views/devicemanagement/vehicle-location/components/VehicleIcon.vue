<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  device: Api.Location.DeviceLocationVO;
  size?: number;
}

const props = withDefaults(defineProps<Props>(), {
  size: 24
});

// 获取车辆状态
const vehicleStatus = computed(() => {
  const now = new Date();
  const deviceTime = new Date(props.device.deviceTime);
  const diffMinutes = (now.getTime() - deviceTime.getTime()) / (1000 * 60);
  const speed = props.device.speedKph || 0;

  if (diffMinutes <= 5) {
    return speed > 0 ? 'driving' : 'parked'; // 在线行驶 / 在线停车
  } else if (diffMinutes <= 30) {
    return 'offline'; // 离线
  }
  return 'lost'; // 失联
});

// 获取车辆状态对应的颜色
const statusColor = computed(() => {
  switch (vehicleStatus.value) {
    case 'driving':
      return '#1afa29'; // 绿色 - 在线行驶
    case 'parked':
      return '#1296db'; // 蓝色 - 在线停车
    case 'offline':
      return '#FFD700'; // 黄色 - 离线
    case 'lost':
      return '#FF4444'; // 红色 - 失联
    default:
      return '#1afa29';
  }
});

// 获取旋转角度（根据车辆方向）
const rotation = computed(() => {
  return props.device.direction || 0;
});

// 获取状态描述
const statusText = computed(() => {
  switch (vehicleStatus.value) {
    case 'driving':
      return '在线行驶';
    case 'parked':
      return '在线停车';
    case 'offline':
      return '离线';
    case 'lost':
      return '失联';
    default:
      return '未知';
  }
});
</script>

<template>
  <div
    class="vehicle-icon"
    :title="`${device.plateNo || device.deviceId} - ${statusText}`"
    :style="{
      width: `${size}px`,
      height: `${size}px`,
      transform: `rotate(${rotation}deg)`
    }"
  >
    <svg viewBox="0 0 1024 1024" :width="size" :height="size" class="vehicle-svg">
      <!-- 车辆主体 -->
      <path
        d="M227.581524 252.875132a12.772415 50.526171 51.83 1 0 79.445388-62.450027 12.772415 50.526171 51.83 1 0-79.445388 62.450027Z"
        fill="#EFC825"
      />
      <path
        d="M742.01325 231.82218a50.526171 12.772415 38.17 1 0 15.786623-20.082849 50.526171 12.772415 38.17 1 0-15.786623 20.082849Z"
        fill="#EFC825"
      />
      <path
        d="M718.396697 136.176482c0 6.386207 0 12.772415-1.314807 18.782963a121.713601 121.713601 0 0 0-80.015423-92.412178 496.245884 496.245884 0 0 0-257.890082 0 121.713601 121.713601 0 0 0-79.639763 92.787837c0-6.386207-0.939148-12.772415-1.502638-18.782963A121.150112 121.150112 0 0 1 379.176385 17.468156 496.245884 496.245884 0 0 1 637.066467 17.092496a120.962282 120.962282 0 0 1 81.33023 119.083986zM717.08189 913.039834a121.713601 121.713601 0 0 1-79.639763 93.914815 496.245884 496.245884 0 0 1-257.890083 0 121.337941 121.337941 0 0 1-79.639763-93.914815 102.742808 102.742808 0 0 1-0.939148-26.483978c0-8.827993 1.502637-18.782963 2.066126-26.671808a120.774452 120.774452 0 0 0 79.076274 89.970393 496.245884 496.245884 0 0 0 257.890083 0 121.150112 121.150112 0 0 0 79.076274-89.970393c0 8.827993 1.314807 17.843815 1.878296 26.671808a102.367149 102.367149 0 0 1-1.878296 26.483978z"
        :fill="statusColor"
      />
      <path
        d="M703.370327 534.938788q0 162.47263 13.148074 325.320919a121.150112 121.150112 0 0 1-79.451934 89.970393 496.245884 496.245884 0 0 1-257.890082 0A120.774452 120.774452 0 0 1 300.10011 860.259707q12.208926-162.66046 12.960245-325.320919v-1.314808-49.211363q0-164.538756-13.523733-329.077513a100.113193 100.113193 0 0 1 3.193103-12.396755 119.647475 119.647475 0 0 1 76.44666-80.391082 496.245884 496.245884 0 0 1 257.890082 0 119.647475 119.647475 0 0 1 76.44666 80.391082c1.314807 4.132252 2.253956 8.264504 3.193104 12.396755q-12.584585 164.538756-13.523734 329.077513v49.211363z"
        :fill="statusColor"
      />

      <!-- 车辆细节 -->
      <path
        d="M314.187333 475.021135l37.565926 39.256393a1209.43499 1209.43499 0 0 0-14.462882-180.316445 383.548105 383.548105 0 0 0-33.058015-112.697778z"
        fill="#273333"
      />
      <path
        d="M355.322022 518.973269l-42.073837-43.200815-9.391482-261.083186 3.756593 6.949696a387.680357 387.680357 0 0 1 33.245844 112.697778 1180.509227 1180.509227 0 0 1 14.462882 179.940786z m-37.565926-44.891282l34.372822 35.4998a1119.088938 1119.088938 0 0 0-15.589859-175.245045 416.418291 416.418291 0 0 0-28.925763-104.057615z"
        fill="#273333"
      />
      <path
        d="M363.210866 391.24912h6.198378v384.675083h-6.198378zM647.960586 391.24912h6.198378v384.675083h-6.198378z"
        fill="#EFC825"
      />
      <path d="M363.210866 771.040633h290.948098v4.695741H363.210866z" fill="#EFC825" />
    </svg>
  </div>
</template>

<style scoped>
.vehicle-icon {
  display: inline-block;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.vehicle-icon:hover {
  transform: scale(1.1) rotate(var(--rotation));
}

.vehicle-svg {
  display: block;
}
</style>

<script setup lang="tsx">
import { ref } from 'vue';
import { NCard, NEmpty, NGrid, NGridItem } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { useAuth } from '@/hooks/business/auth';
import { $t } from '@/locales';
import NoPermission from '@/components/common/no-permission.vue';
import OrgUnitTree from './modules/org-utils-tree.vue';
import UserPageTable from './modules/user-page-table.vue';

defineOptions({
  name: 'UserPage'
});

const { hasAuth } = useAuth();

const { bool: userItemVisible } = useBoolean();

// 组织树选中的数据
const orgUnitsData = ref<Api.SystemManage.OrgUnitsTree>({
  id: -1,
  code: '',
  name: '',
  children: [],
  parentId: -1,
  level: 0,
  ancestors: '',
  sort: 0,
  status: null,
  createBy: '',
  createTime: '',
  updateBy: '',
  updateTime: ''
});

/** org units handle select */
const handleSelect = (visible: boolean, item: Api.SystemManage.OrgUnitsTree) => {
  userItemVisible.value = visible;
  orgUnitsData.value = item;
};
</script>

<template>
  <div v-if="hasAuth('sys:user:view')" class="flex overflow-hidden">
    <NGrid
      :x-gap="8"
      :y-gap="8"
      item-responsive
      responsive="screen"
      cols="1 s:1 m:5 l:5 xl:5 2xl:5"
      class="h-full-hidden"
    >
      <NGridItem span="1" class="h-full-hidden"><OrgUnitTree @select="handleSelect" /></NGridItem>
      <NGridItem span="4" class="h-full overflow-auto">
        <UserPageTable v-if="userItemVisible" :org-units="orgUnitsData" />
        <NCard v-else :bordered="false" size="small" class="h-full">
          <NEmpty :description="$t('page.manage.user.selectTreeIsEmptyTip')" class="h-full justify-center" />
        </NCard>
      </NGridItem>
    </NGrid>
  </div>
  <NoPermission v-else :description="$t('page.manage.user.noPermissionDesc')" />
</template>

<style scoped></style>

export type OperateType = NaiveUI.TableOperateType | 'addChild';

/**
 * transform orgUnitsTree to NaiveUI.TreeOption
 *
 * @file src/views/manage/user/modules/shared.ts
 * @param orgUnitsTree
 * @param orgUnitsIds
 */
export function extractOptionsFromTree(
  orgUnitsTree: Api.SystemManage.OrgUnitsTree[],
  orgUnitsIds: string[]
): CommonType.Option[] {
  const principalOptions: CommonType.Option[] = [];

  function traverse(node: Api.SystemManage.OrgUnitsTree) {
    if (orgUnitsIds.includes(String(node.id))) {
      principalOptions.push({ value: String(node.id), label: node.name });
    }

    if (node.children) {
      node.children.forEach(traverse);
    }
  }

  orgUnitsTree.forEach(traverse);

  return principalOptions;
}

/**
 * collect ids from item
 *
 * @param item orgUnitsTree
 * @returns ids string (comma separated)
 */
export function collectIdsFromItem(item: Api.SystemManage.OrgUnitsTree): string {
  // 如果是"未分配"节点（ID为-1），直接返回-1
  if (item.id === -1) {
    return '-1';
  }

  const idsSet: Set<number> = new Set();

  function traverse(node: Api.SystemManage.OrgUnitsTree) {
    // 确保ID是数字类型
    idsSet.add(Number(node.id));
    if (node.children) {
      node.children.forEach(traverse);
    }
  }

  traverse(item);

  return Array.from(idsSet).join(',');
}

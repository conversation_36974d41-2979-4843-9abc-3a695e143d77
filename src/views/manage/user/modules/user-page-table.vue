<script setup lang="tsx">
import { computed, reactive, ref, watch } from 'vue';
import { NButton, NModal } from 'naive-ui';
import { fetchBatchDeleteUser, fetchDeleteUser, fetchGetUserManageList, fetchResetUserPassword } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { useAuth } from '@/hooks/business/auth';
import { useButtonAuthDropdown } from '@/hooks/common/button-auth-dropdown';
import { useDict } from '@/hooks/business/dict';
import { $t } from '@/locales';
import UserOperateDrawer from './user-operate-drawer.vue';
import UserSearch from './user-search.vue';
import { collectIdsFromItem } from './shared';
import { useBoolean } from '~/packages/hooks';

defineOptions({
  name: 'UserPageListTable'
});

interface Props {
  orgUnits: Api.SystemManage.OrgUnitsTree;
}

const props = defineProps<Props>();

const orgIds = computed(() => collectIdsFromItem(props.orgUnits));

const appStore = useAppStore();

const { hasAuth } = useAuth();

const { dictTag } = useDict();

const { bool: _ } = useBoolean();

// 存储重置密码后的结果
const resetPasswordResult = ref('');
const showResetPassword = ref(false);

type ButtonDropdownKey = 'delete' | 'resetPassword';

/** operation options */
const options: CommonType.ButtonDropdown<ButtonDropdownKey, Api.SystemManage.User>[] = [
  {
    key: 'delete',
    label: $t('common.delete'),
    show: hasAuth('sys:user:delete'),
    handler: (_key, row) => handleDelete(row.id)
  },
  {
    key: 'resetPassword',
    label: $t('page.manage.user.resetPwd'),
    show: hasAuth('sys:user:resetPassword'),
    handler: (_key, row) => handleResetPassword(row.id)
  }
];

const { renderDropdown } = useButtonAuthDropdown(options);

/** api params */
const apiParams = reactive({
  current: 1,
  size: 10,
  userName: null,
  realName: null,
  userEmail: null,
  orgIds: orgIds.value
});

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  updateSearchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetUserManageList,
  apiParams,
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48,
      fixed: 'left'
    },
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'userName',
      title: $t('page.manage.user.userName'),
      align: 'center',
      minWidth: 100
    },
    {
      key: 'realName',
      title: $t('page.manage.user.realName'),
      align: 'center',
      minWidth: 100
    },
    {
      key: 'userGender',
      title: $t('page.manage.user.gender'),
      align: 'center',
      width: 100,
      render: row => dictTag('gender', row.userGender)
    },
    {
      key: 'userPhone',
      title: $t('page.manage.user.phone'),
      align: 'center',
      width: 120
    },
    {
      key: 'userEmail',
      title: $t('page.manage.user.email'),
      align: 'center',
      minWidth: 250,
      ellipsis: {
        tooltip: true
      }
    },
    {
      key: 'status',
      title: $t('page.manage.user.status'),
      align: 'center',
      width: 100,
      render: row => dictTag('status', row.status)
    },
    // 只有当用户有编辑、删除或重置密码权限时才显示操作列
    ...(hasAuth('sys:user:update') || hasAuth('sys:user:delete') || hasAuth('sys:user:resetPassword')
      ? [
          {
            key: 'operate',
            title: $t('common.operate'),
            align: 'center',
            fixed: 'right',
            width: 150,
            render: row => (
              <div class="flex-center gap-8px">
                {hasAuth('sys:user:update') && (
                  <NButton type="primary" quaternary size="small" onClick={() => edit(row.id)}>
                    {$t('common.edit')}
                  </NButton>
                )}
                {renderDropdown(row)}
              </div>
            )
          }
        ]
      : [])
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys } = useTableOperate(
  data as any,
  getData
);

/** edit User */
function edit(id: string) {
  handleEdit(id);
}

/** delete User - 简化处理 */
async function handleDelete(id: string | number) {
  const { error, data: result } = await fetchDeleteUser(Number(id));
  if (!error && result) {
    // 直接显示成功消息
    window.$message?.success($t('common.deleteSuccess'));
    // 直接刷新数据，不使用onDeleted
    getData();
  } else if (error) {
    window.$message?.error(error.message || $t('common.deleteError'));
  }
}

/** batch delete User - 简化处理 */
async function handleBatchDelete() {
  if (checkedRowKeys.value.length === 0) {
    window.$message?.warning($t('page.manage.user.pleaseSelectUser'));
    return;
  }

  // 将字符串ID转换为数字ID
  const ids = checkedRowKeys.value.map(id => Number(id));

  const { error, data: result } = await fetchBatchDeleteUser(ids);
  if (!error && result) {
    // 直接显示成功消息
    window.$message?.success($t('common.deleteSuccess'));
    // 清空选中项
    checkedRowKeys.value = [];
    // 直接刷新数据，不使用onBatchDeleted
    getData();
  } else if (error) {
    window.$message?.error(error.message || $t('common.deleteError'));
  }
}

/** reset user password - 简化处理 */
async function handleResetPassword(id: string | number) {
  const { error } = await fetchResetUserPassword(String(id));
  if (!error) {
    resetPasswordResult.value = '123456';
    showResetPassword.value = true;
  }
}

watch(
  orgIds,
  () => {
    updateSearchParams({ orgIds: orgIds.value });
    apiParams.orgIds = orgIds.value;

    // 立即获取数据，即使是"未分配"节点(ID为-1)也需要获取数据
    getDataByPage();
  },
  { immediate: true }
);
</script>

<template>
  <div class="h-full flex-col-stretch gap-8px overflow-hidden lt-sm:overflow-auto">
    <UserSearch v-model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper" content-class="flex-col">
      <TableHeaderOperation
        v-model:columns="columnChecks"
        :checked-row-keys="checkedRowKeys"
        :loading="loading"
        add-auth="sys:user:add"
        delete-auth="sys:user:delete"
        @add="handleAdd"
        @delete="handleBatchDelete"
        @refresh="getData"
      />
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        remote
        striped
        size="small"
        class="sm:h-full"
        :data="data"
        :scroll-x="962"
        :columns="columns as any"
        :flex-height="!appStore.isMobile"
        :loading="loading"
        :single-line="false"
        :row-key="row => row.id"
        :pagination="mobilePagination"
      />
    </NCard>

    <!-- 重置密码结果弹窗 -->
    <NModal v-model:show="showResetPassword" preset="dialog" :title="$t('page.manage.user.resetPassword')">
      <template #default>
        <p>{{ $t('page.manage.user.newPassword') }}: {{ resetPasswordResult }}</p>
      </template>
      <template #action>
        <NButton type="primary" @click="showResetPassword = false">{{ $t('common.confirm') }}</NButton>
      </template>
    </NModal>

    <UserOperateDrawer
      v-model:visible="drawerVisible"
      :operate-type="operateType"
      :row-data="editingData as any"
      @submitted="getDataByPage"
    />
  </div>
</template>

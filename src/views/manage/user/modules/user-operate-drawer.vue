<script setup lang="ts">
import { computed, reactive, shallowRef, watch } from 'vue';
import { fetchAddUser, fetchGetAllRoles, fetchGetOrgUnitsTree, fetchGetUserById, fetchUpdateUser } from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import { useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';
import { sha256 } from '@/utils/crypto';
import { $t } from '@/locales';

defineOptions({
  name: 'UserOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.SystemManage.User | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const { dictOptions } = useDict();

const visible = defineModel<boolean>('visible', {
  default: false
});

// 角色选项列表
const roleOptions = shallowRef<{ label: string; value: string }[]>([]);

// 组织单位树数据
const orgUnitsTree = shallowRef<Api.SystemManage.OrgUnitsTree[]>([]);

// 组织单位选项列表
const orgUnitsOptions = shallowRef<{ label: string; value: string }[]>([]);

// 转换为树选择器需要的格式
const treeSelectOptions = shallowRef<any[]>([]);

const { formRef, validate, restoreValidation } = useNaiveForm();

// 创建简单的必填规则
const createRequiredRule = (message: string) => ({
  required: true,
  message,
  trigger: 'blur'
});

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: $t('page.manage.user.addUser'),
    edit: $t('page.manage.user.editUser')
  };
  return titles[props.operateType];
});

type Model = Api.SystemManage.UserEdit;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    id: '',
    userName: '',
    userGender: '0',
    realName: '',
    userPhone: '',
    userEmail: '',
    status: '1',
    userRoles: [],
    userOrgUnits: [],
    password: ''
  };
}

// 恢复合理的验证规则，使用国际化
const customRules = {
  userName: [
    {
      validator: (_rule: any, value: string) => {
        if (!value || value.trim() === '') {
          return Promise.reject($t('form.userName.required'));
        }
        // 用户名验证：4-16位，包含中文、字母、数字、下划线、连字符
        if (!/^[\u4E00-\u9FA5a-zA-Z0-9_-]{1,16}$/.test(value)) {
          return Promise.reject($t('form.userName.invalid'));
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  status: [createRequiredRule($t('form.required'))],
  realName: [
    {
      validator: (_rule: any, value: string) => {
        if (!value || value.trim() === '') {
          return Promise.reject($t('form.required'));
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  // 手机号验证：允许为空，如果有值则验证格式
  userPhone: [
    {
      validator: (_rule: any, value: string) => {
        if (!value) return Promise.resolve(); // 允许为空
        // 手机号验证：1开头的11位数字
        if (!/^1\d{10}$/.test(value)) {
          return Promise.reject($t('form.phone.invalid'));
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  // 邮箱验证：允许为空，如果有值则验证格式
  userEmail: [
    {
      validator: (_rule: any, value: string) => {
        if (!value) return Promise.resolve(); // 允许为空
        // 邮箱验证：基本的邮箱格式
        if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
          return Promise.reject($t('form.email.invalid'));
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  // 密码验证：必填，至少6位
  password: [
    {
      validator: (_rule: any, value: string) => {
        if (!value || value.trim() === '') {
          return Promise.reject($t('form.pwd.required'));
        }
        if (value.length < 6) {
          return Promise.reject($t('form.pwd.invalid'));
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ]
};

const isAdd = computed(() => props.operateType === 'add');

// 动态计算验证规则，新增时需要验证密码，编辑时不需要
const dynamicRules = computed(() => {
  const baseRules = { ...customRules };

  // 编辑时移除密码验证
  if (!isAdd.value) {
    delete baseRules.password;
  }

  return baseRules;
});

async function handleInitModel() {
  Object.assign(model, createDefaultModel());

  // 获取所有角色列表和组织单位树
  await Promise.all([fetchAllRoles(), fetchOrgUnitsTree()]);

  if (props.operateType === 'edit' && props.rowData) {
    const { error, data } = await fetchGetUserById(props.rowData?.id.toString());
    if (!error) {
      // 将API返回的User类型转换为UserEdit类型
      model.id = data.id.toString();
      model.userName = data.userName;
      model.userGender = data.userGender ? data.userGender.toString() : '0';
      model.realName = data.realName;
      model.userPhone = data.userPhone;
      model.userEmail = data.userEmail;
      model.status = data.status ? data.status.toString() : '1';

      // 处理用户角色，提取角色ID
      if (data.userRoles && Array.isArray(data.userRoles)) {
        try {
          // 尝试从角色对象中提取ID
          model.userRoles = data.userRoles.map(role => {
            // 使用类型断言处理可能的对象类型
            if (typeof role === 'object' && role !== null) {
              const roleObj = role as unknown as { id: number };
              return roleObj.id;
            }
            // 直接返回数字ID
            return Number(role);
          });
        } catch {
          model.userRoles = [];
        }
      } else {
        model.userRoles = [];
      }

      // 处理用户组织单位 - 从对象数组中提取ID
      if (data.userOrgUnits && Array.isArray(data.userOrgUnits)) {
        try {
          // 从组织单位对象中提取ID
          model.userOrgUnits = data.userOrgUnits.map(unit => {
            // 使用类型断言处理可能的对象类型
            if (typeof unit === 'object' && unit !== null) {
              const orgUnit = unit as unknown as { id: string | number };
              return Number(orgUnit.id);
            }
            // 如果直接是ID，则转换为数字
            return Number(unit);
          });
        } catch {
          model.userOrgUnits = [];
        }
      } else {
        model.userOrgUnits = [];
      }
    }
  }
}

// 获取所有角色列表
async function fetchAllRoles() {
  const { error, data } = await fetchGetAllRoles();
  if (!error && data) {
    // 设置角色选项，使用字符串ID作为选项值
    roleOptions.value = data.map(role => ({
      label: role.roleName,
      value: String(role.id)
    }));
  }
}

// 获取组织单位树
async function fetchOrgUnitsTree() {
  const { error, data } = await fetchGetOrgUnitsTree();
  if (!error && data) {
    // 存储组织单位树数据
    if (Array.isArray(data)) {
      orgUnitsTree.value = data;
    } else {
      // 如果返回的不是数组，可能是单个对象，尝试包装为数组
      orgUnitsTree.value = [data];
    }

    // 将树转换为扁平的选项列表（保留以兼容旧代码）
    orgUnitsOptions.value = convertTreeToOptions(orgUnitsTree.value);

    // 转换为树选择器需要的格式
    treeSelectOptions.value = convertToTreeSelectOptions(orgUnitsTree.value);
  }
}

// 将组织单位树转换为扁平的选项列表
function convertTreeToOptions(tree: Api.SystemManage.OrgUnitsTree[]): { label: string; value: string }[] {
  const options: { label: string; value: string }[] = [];

  function traverse(node: Api.SystemManage.OrgUnitsTree) {
    options.push({
      label: node.name,
      value: String(node.id) // 保持表单控件使用字符串值
    });

    if (node.children && node.children.length > 0) {
      node.children.forEach(traverse);
    }
  }

  tree.forEach(traverse);

  return options;
}

// 将组织单位树转换为树选择器需要的格式
function convertToTreeSelectOptions(tree: Api.SystemManage.OrgUnitsTree[]) {
  function convert(node: Api.SystemManage.OrgUnitsTree) {
    const result: any = {
      key: String(node.id),
      label: node.name
    };

    if (node.children && node.children.length > 0) {
      result.children = node.children.map(convert);
    }

    return result;
  }

  return tree.map(convert);
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  try {
    await validate();
    // request
    const func = isAdd.value ? fetchAddUser : fetchUpdateUser;

    // 获取当前登录用户名
    const currentUser = getCurrentUserName();

    // 基础数据结构
    const baseData = {
      userName: model.userName,
      userGender: model.userGender === '0' ? null : (Number(model.userGender) as Api.SystemManage.UserGender),
      realName: model.realName,
      userPhone: model.userPhone || null, // 允许为空
      userEmail: model.userEmail || null, // 允许为空
      userRoles: model.userRoles ? model.userRoles.map(id => Number(id)) : [],
      status: model.status === '0' ? null : (Number(model.status) as Api.Common.EnableStatus),
      userOrgUnits: model.userOrgUnits ? model.userOrgUnits.map(id => Number(id)) : [],
      updateBy: currentUser
    };

    // 根据操作类型添加不同字段
    let userData: any;

    if (isAdd.value) {
      // 新增用户 - 对密码进行SHA-256加密
      userData = {
        ...baseData,
        password: model.password ? sha256(model.password) : '', // 加密密码
        createBy: currentUser
      };
    } else {
      // 编辑用户
      userData = {
        ...baseData,
        id: Number(model.id)
      };
    }

    const { error } = await func(userData);

    if (error) {
      // 如果有错误，显示错误提示
      window.$message?.error(error.message || '操作失败');
      return;
    }

    // 成功操作的提示
    window.$message?.success(isAdd.value ? '添加成功' : '更新成功');
    closeDrawer();
    emit('submitted');
  } catch (err: any) {
    // 只捕获验证错误，API错误已在上面处理
    // 检查是否为验证错误，避免重复显示API错误
    if (err.name === 'ValidationError') {
      window.$message?.error('表单验证失败，请检查输入');
    }
  }
}

// 获取当前登录用户名
function getCurrentUserName() {
  // 从authStore中获取当前登录用户信息
  const authStore = useAuthStore();
  return authStore.userInfo.userName || 'system';
}

// 处理角色选择变化
function handleRoleChange(values: string[]) {
  // 将字符串ID转换为数字ID
  model.userRoles = values ? values.map(id => Number(id)) : [];
}

// 处理组织单位选择变化
function handleOrgUnitsChange(values: string[]) {
  // 将字符串ID转换为数字ID
  model.userOrgUnits = values ? values.map(id => Number(id)) : [];
}

// 用于NSelect和NTreeSelect的值转换函数，将数字数组转换为字符串数组用于显示
function numberToStringArray(values: number[]): string[] {
  return values.map(v => String(v));
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="360">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="dynamicRules as any">
        <NFormItem :label="$t('page.manage.user.userName')" path="userName">
          <NInput
            v-model:value="model.userName"
            :placeholder="$t('page.manage.user.form.userName')"
            :disabled="!isAdd"
          />
        </NFormItem>
        <NFormItem v-if="isAdd" :label="$t('page.manage.user.password')" path="password">
          <NInput v-model:value="model.password" type="password" :placeholder="$t('page.manage.user.form.password')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.gender')" path="userGender">
          <NRadioGroup v-model:value="model.userGender">
            <NRadio v-for="item in dictOptions('gender')" :key="item.value" :value="item.value" :label="item.label" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.realName')" path="realName">
          <NInput v-model:value="model.realName" :placeholder="$t('page.manage.user.form.realName')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.phone')" path="userPhone">
          <NInput v-model:value="model.userPhone" :placeholder="$t('page.manage.user.form.phone')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.email')" path="userEmail">
          <NInput v-model:value="model.userEmail" :placeholder="$t('page.manage.user.form.email')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.status')" path="status">
          <NRadioGroup v-model:value="model.status">
            <NRadio v-for="item in dictOptions('status')" :key="item.value" :value="item.value" :label="item.label" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.userRole')" path="userRoles">
          <NSelect
            :value="numberToStringArray(model.userRoles)"
            multiple
            :placeholder="$t('page.manage.user.form.userRole')"
            :options="roleOptions"
            :on-update:value="values => handleRoleChange(values)"
          />
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.userOrgUnits')" path="userOrgUnits">
          <NTreeSelect
            :value="numberToStringArray(model.userOrgUnits)"
            multiple
            :placeholder="$t('page.manage.user.form.userOrgUnits')"
            :options="treeSelectOptions"
            cascade
            check-strategy="child"
            :on-update:value="values => handleOrgUnitsChange(values)"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton quaternary @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>

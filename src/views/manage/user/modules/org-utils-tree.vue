<script setup lang="ts">
import type { Ref } from 'vue';
import { h, nextTick, onMounted, ref, shallowRef } from 'vue';
import type { TreeOption } from 'naive-ui';
import { NButton, NButtonGroup, NFlex, NPopconfirm, NTree } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { fetchDeleteOrgUnits, fetchGetOrgUnits, fetchGetOrgUnitsTree } from '@/service/api';
import { useAuth } from '@/hooks/business/auth';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { $t } from '@/locales';
import OrgUnitsOperateDrawer from '@/views/manage/org/modules/org-units-operate-drawer.vue';
import type { OperateType } from './shared';

defineOptions({
  name: 'UserOrgUnitsTree'
});

const name = ref<string>('');

type Emits = {
  (e: 'select', visible: boolean, item: Api.SystemManage.OrgUnitsTree): void;
};

const emit = defineEmits<Emits>();

const { hasAuth } = useAuth();

const operateType = ref<OperateType>('add');

const orgUnitsTreeData = shallowRef<TreeOption[]>([]);

const { bool: visible, setTrue: openDrawer } = useBoolean();

const { setBool: setUserItemVisible } = useBoolean();

const editingData: Ref<Api.SystemManage.OrgUnits | null> = ref(null);

// 声明一个新的响应式变量来存储展开的keys
const expandedKeys = ref<(string | number)[]>([-1]);

// 将组织单位树转换为Tree组件所需的格式
function convertToTreeOptions(data: Api.SystemManage.OrgUnitsTree[]): TreeOption[] {
  if (!data || data.length === 0) {
    return [];
  }

  const result: TreeOption[] = [];

  for (const item of data) {
    // 创建基本节点结构
    const treeNode: TreeOption = {
      key: item.id,
      label: item.name
    };

    // 处理子节点
    if (item.children && Array.isArray(item.children) && item.children.length > 0) {
      // 递归处理子节点
      const childNodes = convertToTreeOptions(item.children);

      if (childNodes.length > 0) {
        treeNode.children = childNodes;
      }
    }

    // 保存原始数据以便后续使用
    for (const key in item) {
      if (key !== 'children') {
        (treeNode as any)[key] = item[key as keyof typeof item];
      }
    }

    result.push(treeNode);
  }

  return result;
}

/** init */
const init = async () => {
  try {
    window.$loadingBar?.start();

    // 直接调用API
    const result = await fetchGetOrgUnitsTree();
    if (!result.error && result.data) {
      // 转换数据格式
      let orgData: Api.SystemManage.OrgUnitsTree[] = [];
      if (Array.isArray(result.data)) {
        orgData = result.data;
      } else {
        orgData = [result.data];
      }
      // 深度打印前三级节点结构
      const printStructure = (items: Api.SystemManage.OrgUnitsTree[], level = 0, maxLevel = 3) => {
        if (level >= maxLevel) return;
        items.forEach(item => {
          if (item.children && Array.isArray(item.children) && item.children.length > 0) {
            printStructure(item.children, level + 1, maxLevel);
          }
        });
      };

      printStructure(orgData);

      // 转换为树组件所需格式
      const treeData = convertToTreeOptions(orgData);

      // 检查转换后的树节点是否保留了子节点
      for (const node of treeData) {
        if (node.children && node.children.length > 0) {
          break;
        }
      }
      // 添加"未分配"节点
      treeData.unshift({
        key: -1,
        label: '未分配',
        id: -1,
        parentId: -1,
        name: '未分配',
        code: 'unassigned',
        level: 0,
        ancestors: '',
        sort: 0,
        status: null
      });

      // 更新树数据
      orgUnitsTreeData.value = treeData;

      // 展开所有节点
      const allKeys = treeData.map(node => node.key);
      expandedKeys.value = allKeys;

      window.$loadingBar?.finish();
    } else {
      window.$loadingBar?.error();
      // 使用nextTick延迟消息显示
      nextTick(() => {});
    }
  } catch {
    window.$loadingBar?.error();
    // 使用nextTick延迟消息显示
    nextTick(() => {});
  }
};

/** add */
const handleAdd = () => {
  operateType.value = 'add';
  openDrawer();
};

/** add child */
const handleAddChild = async (item: Api.SystemManage.OrgUnitsTree) => {
  const { error, data } = await fetchGetOrgUnits(item.id);
  if (!error && data) {
    operateType.value = 'addChild';
    editingData.value = { ...data };
    openDrawer();
  }
};

/** edit */
const handleEdit = async (item: Api.SystemManage.OrgUnitsTree) => {
  const { error, data } = await fetchGetOrgUnits(item.id);
  if (!error && data) {
    operateType.value = 'edit';
    editingData.value = { ...data };
    openDrawer();
  }
};

/** delete */
const handleDelete = async (item: Api.SystemManage.OrgUnitsTree) => {
  try {
    const result = await fetchDeleteOrgUnits(Number(item.id));

    // 从response中获取响应数据
    const responseData = result.response?.data;

    if (!result.error && responseData && responseData.code === 200) {
      // 使用nextTick延迟消息显示
      nextTick(() => {});
    }
    // 错误消息已由全局错误处理器显示，此处不需要重复显示
    await init();
  } catch {}
};

/** render suffix */
function renderSuffix({ option }: { option: TreeOption }) {
  // 已经将原始数据附加到TreeOption上
  const item = option as TreeOption & Api.SystemManage.OrgUnitsTree;
  if (item.id === -1) return null;

  return h(
    NButtonGroup,
    {},
    {
      default: () => [
        hasAuth('sys:org:units:add') &&
          h(
            NButton,
            {
              size: 'tiny',
              quaternary: true,
              onClick: event => {
                event.stopPropagation();
                handleAddChild(item);
              },
              title: $t('page.manage.orgUnits.addChildOrgUnits')
            },
            { icon: () => h(SvgIcon, { icon: 'ic:round-playlist-add' }) }
          ),
        hasAuth('sys:org:units:update') &&
          h(
            NButton,
            {
              size: 'tiny',
              quaternary: true,
              onClick: event => {
                event.stopPropagation();
                handleEdit(item);
              },
              title: $t('common.edit')
            },
            { icon: () => h(SvgIcon, { icon: 'ic:round-edit' }) }
          ),
        hasAuth('sys:org:units:delete') &&
          h(
            NPopconfirm,
            {
              onPositiveClick: () => handleDelete(item)
            },
            {
              default: () => $t('common.confirmDelete'),
              trigger: () =>
                h(
                  NButton,
                  {
                    size: 'tiny',
                    quaternary: true,
                    onClick: event => {
                      event.stopPropagation();
                    },
                    title: $t('common.delete')
                  },
                  { icon: () => h(SvgIcon, { icon: 'ic:round-delete' }) }
                )
            }
          )
      ]
    }
  );
}

/** tree select handle */
function handleSelectKeys(node: TreeOption | null, action: string) {
  // 处理节点选择
  if (action === 'select') {
    if (node) {
      const nodeData = node as TreeOption & Api.SystemManage.OrgUnitsTree;

      setUserItemVisible(true);
      emit('select', true, nodeData);
    } else {
      setUserItemVisible(false);
      emit('select', false, {
        id: -1,
        parentId: -1,
        key: -1,
        label: $t('page.manage.user.unassigned'),
        name: $t('page.manage.user.unassigned'),
        code: 'unassigned',
        level: 0,
        ancestors: '',
        sort: 0,
        status: null
      } as unknown as TreeOption & Api.SystemManage.OrgUnitsTree);
    }
  }

  // // 处理节点展开
  // if (action === 'expand') {
  //   if (node) {
  //     const nodeData = node as TreeOption & Api.SystemManage.OrgUnitsTree;

  //     // 检查节点是否有children属性
  //     if (nodeData.children && nodeData.children.length > 0) {
  //       console.log(`展开的节点 ${nodeData.name} 有 ${nodeData.children.length} 个子节点`);
  //     } else {
  //       console.log(`展开的节点 ${nodeData.name} 没有子节点`);
  //     }
  //   }
  // }
}

// 处理展开的节点
function handleExpandedKeys(keys: (string | number)[]) {
  expandedKeys.value = keys;
}

onMounted(() => init());
</script>

<template>
  <div class="h-full-hidden">
    <NCard
      :title="$t('page.manage.orgUnits.orgStructure')"
      :bordered="false"
      size="small"
      class="h-full sm:flex-1-hidden"
      content-class="h-full-hidden"
    >
      <template #header-extra>
        <NFlex>
          <NButton v-if="hasAuth('sys:org:units:add')" ghost type="primary" @click="handleAdd()">
            {{ $t('common.add') }}
          </NButton>
          <NButton quaternary :title="$t('common.refresh')" @click="init">
            <template #icon>
              <SvgIcon icon="ic:round-refresh" />
            </template>
          </NButton>
        </NFlex>
      </template>
      <div class="h-full flex flex-col">
        <NInput v-model:value="name" class="mb-2" clearable :placeholder="$t('common.keywordSearch')" />
        <NTree
          :pattern="name"
          :data="orgUnitsTreeData"
          block-line
          expand-on-click
          selectable
          key-field="key"
          label-field="label"
          children-field="children"
          :expanded-keys="expandedKeys"
          :show-irrelevant-nodes="false"
          :render-suffix="renderSuffix"
          class="p-tree my-3 flex-col-stretch"
          :empty-text="$t('common.noData')"
          @update-selected-keys="(_key, _option, { node, action }) => handleSelectKeys(node, action)"
          @update-expanded-keys="handleExpandedKeys"
        />
      </div>
    </NCard>
    <OrgUnitsOperateDrawer
      v-model:visible="visible"
      :operate-type="operateType"
      :row-data="editingData"
      @submitted="init"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep(.n-tree) {
  .n-tree-node-content__suffix {
    display: none;
  }
  .n-tree-node-content:hover .n-tree-node-content__suffix {
    display: inline-flex;
  }
}
</style>

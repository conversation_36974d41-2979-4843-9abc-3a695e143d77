<script setup lang="tsx">
import { reactive, ref } from 'vue';
import { NButton, NCard, NDataTable, NTag } from 'naive-ui';
import { fetchGetGatewayLogList } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable } from '@/hooks/common/table';
import { useAuth } from '@/hooks/business/auth';
import { $t } from '@/locales';
import TableHeaderOperation from '@/components/advanced/table-header-operation.vue';
import GatewayLogSearch from './gateway-log-search.vue';
import GatewayLogDetail from './gateway-log-detail.vue';

defineOptions({
  name: 'GatewayLogTable'
});

const appStore = useAppStore();
const { hasAuth } = useAuth();

const detailVisible = ref(false);
const selectedLogId = ref<string | null>(null);

/** api params */
const apiParams = reactive<Api.Gateway.LogQueryParams>({
  current: 1,
  size: 10,
  requestPath: '', // 文本输入框，空字符串合适
  requestMethod: null, // 下拉选择框，null 表示未选择
  targetServer: '', // 文本输入框，空字符串合适
  ip: '', // 文本输入框，空字符串合适
  userId: '', // 文本输入框，空字符串合适
  statusCode: null, // 下拉选择框，null 表示未选择
  startTime: '', // 时间字段，空字符串表示未设置
  endTime: '', // 时间字段，空字符串表示未设置
  minExecuteTime: '', // 数字输入框（字符串类型），空字符串表示未设置
  maxExecuteTime: '', // 数字输入框（字符串类型），空字符串表示未设置
  sortField: 'createTime',
  sortDirection: 'desc'
});

const { columns, columnChecks, data, getData, getDataByPage, loading, mobilePagination, searchParams } = useTable({
  apiFn: fetchGetGatewayLogList,
  apiParams,
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'userId',
      title: $t('page.manage.gateway.table.userId'),
      align: 'center',
      width: 100
    },
    {
      key: 'userName',
      title: $t('page.manage.gateway.table.userName'),
      align: 'center',
      width: 100
    },
    {
      key: 'schema',
      title: $t('page.manage.gateway.table.protocol'),
      align: 'center',
      width: 100
    },
    {
      key: 'requestMethod',
      title: $t('page.manage.gateway.table.requestMethod'),
      align: 'center',
      width: 100,
      render: (row: Api.Gateway.Log) => (
        <NTag type={row.requestMethod === 'GET' ? 'info' : 'primary'}>{row.requestMethod}</NTag>
      )
    },
    {
      key: 'referer',
      title: $t('page.manage.gateway.table.referer'),
      align: 'center',
      minWidth: 340
    },
    {
      key: 'requestPath',
      title: $t('page.manage.gateway.table.requestPath'),
      align: 'left',
      minWidth: 200,
      ellipsis: {
        tooltip: true
      }
    },
    {
      key: 'statusCode',
      title: $t('page.manage.gateway.table.responseStatus'),
      align: 'center',
      width: 100,
      render: (row: Api.Gateway.Log) => {
        const getStatusColor = (status: number) => {
          if (status >= 200 && status < 300) return 'success';
          if (status >= 300 && status < 400) return 'warning';
          if (status >= 400 && status < 500) return 'error';
          if (status >= 500) return 'error';
          return 'default';
        };
        return <NTag type={getStatusColor(row.statusCode)}>{row.statusCode}</NTag>;
      }
    },
    {
      key: 'ip',
      title: $t('page.manage.gateway.table.clientIp'),
      align: 'center',
      width: 140
    },
    {
      key: 'targetServer',
      title: $t('page.manage.gateway.table.targetServer'),
      align: 'center',
      width: 120
    },
    {
      key: 'executeTime',
      title: $t('page.manage.gateway.table.executeTime'),
      align: 'center',
      width: 100,
      render: (row: Api.Gateway.Log) => {
        const formatDuration = (duration: number) => {
          if (duration < 1000) return `${duration}ms`;
          return `${(duration / 1000).toFixed(2)}s`;
        };
        return formatDuration(row.executeTime);
      }
    },
    {
      key: 'requestTime',
      title: $t('page.manage.gateway.table.requestTime'),
      align: 'center',
      width: 180,
      render: (row: Api.Gateway.Log) => new Date(row.requestTime).toLocaleString()
    },
    ...(hasAuth('sys:gateway:log:view')
      ? [
          {
            key: 'operate',
            title: $t('common.operate'),
            align: 'center',
            fixed: 'right',
            width: 100,
            render: (row: Api.Gateway.Log) => (
              <NButton type="primary" quaternary size="small" onClick={() => viewDetail(row.id)}>
                {$t('page.manage.gateway.table.viewDetail')}
              </NButton>
            )
          }
        ]
      : [])
  ]
});

function viewDetail(id: string) {
  selectedLogId.value = id;
  detailVisible.value = true;
}

function handleSearch() {
  getDataByPage();
}

function handleReset() {
  // 直接使用 Object.assign 强制重置所有字段
  Object.assign(searchParams, {
    current: 1,
    size: 10,
    requestPath: '',
    requestMethod: null,
    targetServer: '',
    ip: '',
    userId: '',
    statusCode: null,
    startTime: '',
    endTime: '',
    minExecuteTime: '',
    maxExecuteTime: '',
    sortField: 'createTime',
    sortDirection: 'desc'
  });

  getDataByPage();
}
</script>

<template>
  <div class="h-full flex-col-stretch gap-8px overflow-hidden lt-sm:overflow-auto">
    <GatewayLogSearch v-model:model="searchParams" @reset="handleReset" @search="handleSearch" />

    <NCard
      :title="$t('page.manage.gateway.tableTitle')"
      :bordered="false"
      class="sm:flex-1-hidden card-wrapper"
      content-class="flex-col"
    >
      <TableHeaderOperation
        v-model:columns="columnChecks"
        :loading="loading"
        add-auth="sys:gateway:log:add"
        delete-auth="sys:gateway:log:delete"
        @refresh="getData"
      />
      <NDataTable
        remote
        striped
        size="small"
        class="sm:h-full"
        :data="data"
        :scroll-x="1000"
        :columns="columns as any"
        :flex-height="!appStore.isMobile"
        :loading="loading"
        :single-line="false"
        :row-key="(row: Api.Gateway.Log) => row.id"
        :pagination="mobilePagination"
      />
    </NCard>

    <GatewayLogDetail v-model:visible="detailVisible" :log-id="selectedLogId" />
  </div>
</template>

<style scoped></style>

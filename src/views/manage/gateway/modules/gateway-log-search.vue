<script setup lang="ts">
import { computed, nextTick } from 'vue';
import { NButton, NCard, NDatePicker, NForm, NFormItem, NGrid, NGridItem, NInput, NSelect } from 'naive-ui';
import dayjs from 'dayjs';
import { $t } from '@/locales';

defineOptions({
  name: 'GatewayLogSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const model = defineModel<Api.Gateway.LogQueryParams>('model', { required: true });

const methodOptions = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'DELETE', value: 'DELETE' },
  { label: 'PATCH', value: 'PATCH' },
  { label: 'HEAD', value: 'HEAD' },
  { label: 'OPTIONS', value: 'OPTIONS' }
];

const statusOptions = [
  { label: `200 - ${$t('page.manage.gateway.search.statusOptions.success')}`, value: 200 },
  { label: `400 - ${$t('page.manage.gateway.search.statusOptions.badRequest')}`, value: 400 },
  { label: `401 - ${$t('page.manage.gateway.search.statusOptions.unauthorized')}`, value: 401 },
  { label: `403 - ${$t('page.manage.gateway.search.statusOptions.forbidden')}`, value: 403 },
  { label: `404 - ${$t('page.manage.gateway.search.statusOptions.notFound')}`, value: 404 },
  { label: `500 - ${$t('page.manage.gateway.search.statusOptions.serverError')}`, value: 500 },
  { label: `502 - ${$t('page.manage.gateway.search.statusOptions.badGateway')}`, value: 502 },
  { label: `503 - ${$t('page.manage.gateway.search.statusOptions.serviceUnavailable')}`, value: 503 }
];

const timeRange = computed({
  get() {
    // 检查startTime和endTime，并确保它们不是空的
    if (
      model.value.startTime &&
      model.value.endTime &&
      model.value.startTime.trim() !== '' &&
      model.value.endTime.trim() !== ''
    ) {
      try {
        return [new Date(model.value.startTime).getTime(), new Date(model.value.endTime).getTime()] as [number, number];
      } catch {
        return null;
      }
    }
    return null;
  },
  set(value: [number, number] | null) {
    if (value && value.length === 2) {
      // 使用 dayjs 将时间戳转换为本地时间的 "YYYY-MM-DD HH:mm:ss" 格式
      model.value.startTime = dayjs(value[0]).format('YYYY-MM-DD HH:mm:ss');
      model.value.endTime = dayjs(value[1]).format('YYYY-MM-DD HH:mm:ss');
    } else {
      // 重置时设置为空字符串
      model.value.startTime = '';
      model.value.endTime = '';
    }
  }
});

async function reset() {
  // 只需将重置事件发送给parent -让parent处理实际的重置逻辑
  emit('reset');

  // 强制更新视图以确保所有输入框都被清空
  await nextTick();
}

function search() {
  emit('search');
}
</script>

<template>
  <NCard :title="$t('page.manage.gateway.searchTitle')" :bordered="false" size="small" class="card-wrapper">
    <NForm :model="model" label-placement="left" :label-width="80">
      <NGrid responsive="screen" item-responsive>
        <NGridItem span="24 s:12 m:6">
          <NFormItem :label="$t('page.manage.gateway.search.requestPath')">
            <NInput
              v-model:value="model.requestPath"
              :placeholder="$t('page.manage.gateway.search.placeholder.requestPath')"
              clearable
            />
          </NFormItem>
        </NGridItem>
        <NGridItem span="24 s:12 m:6">
          <NFormItem :label="$t('page.manage.gateway.search.requestMethod')">
            <NSelect
              v-model:value="model.requestMethod"
              :options="methodOptions"
              :placeholder="$t('page.manage.gateway.search.placeholder.requestMethod')"
              clearable
            />
          </NFormItem>
        </NGridItem>
        <NGridItem span="24 s:12 m:6">
          <NFormItem :label="$t('page.manage.gateway.search.responseStatus')">
            <NSelect
              v-model:value="model.statusCode"
              :options="statusOptions"
              :placeholder="$t('page.manage.gateway.search.placeholder.responseStatus')"
              clearable
            />
          </NFormItem>
        </NGridItem>
        <NGridItem span="24 s:12 m:6">
          <NFormItem :label="$t('page.manage.gateway.search.clientIp')">
            <NInput
              v-model:value="model.ip"
              :placeholder="$t('page.manage.gateway.search.placeholder.clientIp')"
              clearable
            />
          </NFormItem>
        </NGridItem>
        <NGridItem span="24 s:12 m:6">
          <NFormItem :label="$t('page.manage.gateway.search.targetServer')">
            <NInput
              v-model:value="model.targetServer"
              :placeholder="$t('page.manage.gateway.search.placeholder.targetServer')"
              clearable
            />
          </NFormItem>
        </NGridItem>
        <NGridItem span="24 s:12 m:6">
          <NFormItem :label="$t('page.manage.gateway.search.userId')">
            <NInput
              v-model:value="model.userId"
              :placeholder="$t('page.manage.gateway.search.placeholder.userId')"
              clearable
            />
          </NFormItem>
        </NGridItem>
        <NGridItem span="24 s:24 m:12">
          <NFormItem :label="$t('page.manage.gateway.search.timeRange')">
            <NDatePicker
              v-model:value="timeRange"
              type="datetimerange"
              clearable
              class="w-full"
              :start-placeholder="$t('page.manage.gateway.search.placeholder.startTime')"
              :end-placeholder="$t('page.manage.gateway.search.placeholder.endTime')"
            />
          </NFormItem>
        </NGridItem>
        <NGridItem span="24 s:12 m:6">
          <NFormItem :label="$t('page.manage.gateway.search.minExecuteTime')">
            <NInput
              v-model:value="model.minExecuteTime"
              :placeholder="$t('page.manage.gateway.search.placeholder.minExecuteTime')"
              clearable
            />
          </NFormItem>
        </NGridItem>
        <NGridItem span="24 s:12 m:6">
          <NFormItem :label="$t('page.manage.gateway.search.maxExecuteTime')">
            <NInput
              v-model:value="model.maxExecuteTime"
              :placeholder="$t('page.manage.gateway.search.placeholder.maxExecuteTime')"
              clearable
            />
          </NFormItem>
        </NGridItem>
        <NGridItem span="24 s:24 m:12">
          <div class="flex-y-center justify-end gap-12px">
            <NButton @click="reset">{{ $t('page.manage.gateway.search.reset') }}</NButton>
            <NButton type="primary" ghost @click="search">{{ $t('page.manage.gateway.search.search') }}</NButton>
          </div>
        </NGridItem>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { NCode, NDescriptions, NDescriptionsItem, NDrawer, NDrawerContent, NTag } from 'naive-ui';
import { fetchGetGatewayLogById } from '@/service/api';
import { $t } from '@/locales';

defineOptions({
  name: 'GatewayLogDetail'
});

interface Props {
  visible: boolean;
  logId: string | null;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const loading = ref(false);
const logDetail = ref<Api.Gateway.Log | null>(null);

const drawerVisible = computed({
  get() {
    return props.visible;
  },
  set(visible: boolean) {
    emit('update:visible', visible);
  }
});

const getStatusColor = (status: number) => {
  if (status >= 200 && status < 300) return 'success';
  if (status >= 300 && status < 400) return 'warning';
  if (status >= 400 && status < 500) return 'error';
  if (status >= 500) return 'error';
  return 'default';
};

const formatDuration = (duration: number) => {
  if (duration < 1000) return `${duration}ms`;
  return `${(duration / 1000).toFixed(2)}s`;
};

const formatJson = (jsonStr: string) => {
  try {
    return JSON.stringify(JSON.parse(jsonStr), null, 2);
  } catch {
    return jsonStr;
  }
};

async function fetchLogDetail() {
  if (!props.logId) return;

  loading.value = true;
  try {
    const { data, error } = await fetchGetGatewayLogById(props.logId);
    if (!error && data) {
      logDetail.value = data;
    }
  } finally {
    loading.value = false;
  }
}

watch(
  () => props.visible,
  visible => {
    if (visible && props.logId) {
      fetchLogDetail();
    }
  },
  { immediate: true }
);
</script>

<template>
  <NDrawer v-model:show="drawerVisible" :width="800" placement="right">
    <NDrawerContent :title="$t('page.manage.gateway.detail.title')" :native-scrollbar="false">
      <div v-if="logDetail" class="space-y-4">
        <!-- 基本信息 -->
        <NDescriptions :title="$t('page.manage.gateway.detail.basicInfo')" :column="2" bordered>
          <NDescriptionsItem :label="$t('page.manage.gateway.detail.requestId')">
            {{ logDetail.id }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.gateway.detail.targetServer')">
            {{ logDetail.targetServer }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.gateway.detail.requestTime')">
            {{ logDetail.requestTime }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.gateway.detail.responseTime')">
            {{ logDetail.responseTime }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.gateway.detail.executeTime')">
            {{ formatDuration(logDetail.executeTime) }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.gateway.detail.protocol')">
            {{ logDetail.schema }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.gateway.detail.clientIp')">
            {{ logDetail.ip }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.gateway.detail.userId')">
            {{ logDetail.userId || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.gateway.detail.userName')">
            {{ logDetail.userName || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.gateway.detail.referer')">
            <div class="max-w-300px truncate" :title="logDetail.referer">
              {{ logDetail.referer || '-' }}
            </div>
          </NDescriptionsItem>
        </NDescriptions>

        <!-- 请求信息 -->
        <NDescriptions :title="$t('page.manage.gateway.detail.requestInfo')" :column="1" bordered>
          <NDescriptionsItem :label="$t('page.manage.gateway.detail.requestMethod')">
            <NTag :type="logDetail.requestMethod === 'GET' ? 'info' : 'primary'">
              {{ logDetail.requestMethod }}
            </NTag>
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.gateway.detail.requestPath')">
            {{ logDetail.requestPath }}
          </NDescriptionsItem>

          <NDescriptionsItem :label="$t('page.manage.gateway.detail.requestBody')">
            <NCode
              v-if="logDetail.requestBody"
              :code="formatJson(logDetail.requestBody)"
              language="json"
              show-line-numbers
            />
            <span v-else>-</span>
          </NDescriptionsItem>
        </NDescriptions>

        <!-- 响应信息 -->
        <NDescriptions :title="$t('page.manage.gateway.detail.responseInfo')" :column="1" bordered>
          <NDescriptionsItem :label="$t('page.manage.gateway.detail.responseStatus')">
            <NTag :type="getStatusColor(logDetail.statusCode)">
              {{ logDetail.statusCode }}
            </NTag>
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.gateway.detail.responseStatusDesc')">
            {{ logDetail.status }}
          </NDescriptionsItem>
          <NDescriptionsItem :label="$t('page.manage.gateway.detail.responseData')">
            <NCode
              v-if="logDetail.responseData"
              :code="formatJson(logDetail.responseData)"
              language="json"
              show-line-numbers
            />
            <span v-else>-</span>
          </NDescriptionsItem>
        </NDescriptions>
      </div>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped>
.space-y-4 > * + * {
  margin-top: 1rem;
}
</style>

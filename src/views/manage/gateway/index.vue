<script setup lang="ts">
import { useAuth } from '@/hooks/business/auth';
import { $t } from '@/locales';
import NoPermission from '@/components/common/no-permission.vue';
import GatewayLogTable from './modules/gateway-log-table.vue';

defineOptions({
  name: 'GatewayLogPage'
});

const { hasAuth } = useAuth();
</script>

<template>
  <div v-if="hasAuth('sys:gateway:log:view')" class="h-full">
    <GatewayLogTable />
  </div>
  <NoPermission v-else :description="$t('page.manage.gateway.noPermissionDesc')" />
</template>

<style scoped></style>

<script setup lang="ts">
import { computed, h, reactive, ref, shallowRef, watch } from 'vue';
import type { TreeInst, TreeOption } from 'naive-ui';
import { fetchAddRoleMenu, fetchGetMenuTree, fetchGetRoleMenuIds } from '@/service/api';
import { $t } from '@/locales';

// 扩展TreeOption接口，添加我们需要的字段
interface MenuTreeOption extends TreeOption {
  menuId?: string; // 保存菜单的原始ID
}

defineOptions({
  name: 'MenuAuthModal'
});

interface Props {
  /** the roleId */
  roleId: string;
}

const props = defineProps<Props>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const title = computed(() => $t('common.edit') + $t('page.manage.role.menuAuth'));

/** menu tree data */
const tree = shallowRef<TreeOption[]>([]);

/** tree checks */
const checks = shallowRef<string[]>([]);

/** expanded keys */
const expandedKeys = ref<string[]>([]);

/** menu auth model */
const model: Api.SystemManage.RoleMenu = reactive(createDefaultModel());

// 树组件引用
const treeRef = ref<TreeInst | null>(null);

// 加载状态
const loading = ref(false);

function createDefaultModel(): Api.SystemManage.RoleMenu {
  return {
    roleId: props.roleId,
    menuIds: []
  };
}

/** 同时获取菜单树和角色权限数据 */
async function getMenuTreeAndPermissions() {
  loading.value = true;

  try {
    // 使用Promise.all同时请求菜单树和角色权限
    const [menuTreeResult, roleMenuIdsResult] = await Promise.all([
      fetchGetMenuTree(),
      fetchGetRoleMenuIds(props.roleId)
    ]);

    // 处理菜单树数据
    if (!menuTreeResult.error && menuTreeResult.data) {
      if (!Array.isArray(menuTreeResult.data)) {
        tree.value = [];
        loading.value = false;
        return;
      }

      // 转换菜单树数据
      const processedData = menuTreeResult.data.map(item => convertMenuToTreeOption(item));

      // 验证处理后的数据
      const invalidNodes = validateTreeData(processedData);
      if (invalidNodes.length > 0) {
        tree.value = [];
      } else {
        tree.value = processedData;

        // 默认收起所有节点
        setInitialExpandState();
      }

      // 处理角色权限数据
      if (!roleMenuIdsResult.error && roleMenuIdsResult.data) {
        const rawMenuIds = roleMenuIdsResult.data;

        // 过滤掉冗余的父节点ID
        checks.value = filterRedundantParentNodes(rawMenuIds, tree.value);
      } else {
        checks.value = [];
      }
    } else {
      tree.value = [];
      checks.value = [];
    }
  } catch {
    window.$message?.error?.($t('common.requestError' as App.I18n.I18nKey));
  } finally {
    loading.value = false;
  }
}

/** 验证树数据中的节点是否都有有效的key */
function validateTreeData(nodes: TreeOption[]): TreeOption[] {
  const invalidNodes: TreeOption[] = [];

  function validate(nodeList: TreeOption[]) {
    for (const node of nodeList) {
      if (!node.key) {
        invalidNodes.push(node);
      }
      if (node.children && node.children.length > 0) {
        validate(node.children);
      }
    }
  }

  validate(nodes);
  return invalidNodes;
}

/** 初始化展开状态 - 默认全部收起 */
function setInitialExpandState() {
  expandedKeys.value = [];
}

/** 将菜单数据转换为Tree组件需要的格式 */
function convertMenuToTreeOption(item: any): MenuTreeOption {
  // 直接使用原始ID作为key，不再生成随机值
  const nodeKey = item.id ? String(item.id) : '';

  // 判断节点类型和子节点情况
  const hasChildrenArray = item.children && Array.isArray(item.children) && item.children.length > 0;

  // 处理菜单显示名称
  let displayName = item.name || '未命名节点';
  if (item.i18nKey) {
    try {
      const translatedName = $t(item.i18nKey as App.I18n.I18nKey);
      if (translatedName && translatedName !== item.i18nKey) {
        displayName = translatedName;
      }
    } catch {
      // 翻译失败，使用默认名称
    }
  }

  // 创建节点对象
  const result: MenuTreeOption = {
    key: nodeKey,
    label: displayName,
    isLeaf: !hasChildrenArray,
    type: item.type || (hasChildrenArray ? '1' : '2'), // 如果没有明确类型，根据是否有子节点推断
    children: hasChildrenArray ? item.children.map((child: any) => convertMenuToTreeOption(child)) : undefined,
    menuId: item.id // 保存原始ID（虽然key和menuId相同，但为了保持一致性）
  };

  return result;
}

/** 展开全部节点 */
function expandAll() {
  // 递归获取所有非叶子节点的key
  function getAllKeys(nodes: TreeOption[]): string[] {
    let keys: string[] = [];
    for (const node of nodes) {
      if (node.key && node.children && node.children.length > 0) {
        keys.push(String(node.key));
        keys = [...keys, ...getAllKeys(node.children)];
      }
    }
    return keys;
  }

  expandedKeys.value = getAllKeys(tree.value);
}

/** 收起全部节点 */
function collapseAll() {
  expandedKeys.value = [];
}

/** 处理选中状态变化 */
function handleUpdateChecked(keys: string[]) {
  checks.value = keys;
}

/** 处理展开状态变化 */
function handleUpdateExpanded(keys: string[]) {
  expandedKeys.value = keys;
}

/** 自定义渲染节点 */
function renderLabel(renderOpts: { option: TreeOption }) {
  const { option } = renderOpts;

  if (!option.key) {
    return h('div', {}, { default: () => '无效节点' });
  }

  const hasChildren = option.children && option.children.length > 0;
  const labelText = option.label ? option.label : '未命名节点';

  return h(
    'span',
    {
      class: 'node-label',
      style: {
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
        fontWeight: hasChildren ? '500' : 'normal',
        display: 'inline-block',
        position: 'relative',
        top: '-2px'
      }
    },
    labelText
  );
}

/** 提交角色菜单权限 */
async function handleSubmit() {
  loading.value = true;

  try {
    // 获取所有选中节点及其子节点的ID
    const getAllSelectedMenuIds = () => {
      // 选中的节点ID集合
      const selectedIds = new Set(checks.value.filter(key => key && key !== ''));

      // 递归函数：收集选中节点及其所有子节点的ID
      const collectChildrenIds = (nodes: MenuTreeOption[]) => {
        for (const node of nodes) {
          // 如果节点被选中，则将其所有子节点也加入选中集合
          if (selectedIds.has(node.key as string) && node.children && node.children.length > 0) {
            // 递归遍历子节点，将其ID加入集合
            const collectChildren = (children: MenuTreeOption[]) => {
              for (const child of children) {
                selectedIds.add(child.key as string);

                if (child.children && child.children.length > 0) {
                  collectChildren(child.children as MenuTreeOption[]);
                }
              }
            };

            collectChildren(node.children as MenuTreeOption[]);
          }

          // 无论当前节点是否选中，都继续检查其子节点
          if (node.children && node.children.length > 0) {
            collectChildrenIds(node.children as MenuTreeOption[]);
          }
        }
      };

      // 创建节点ID到父节点ID的映射关系
      const nodeIdToParentIdMap = new Map<string, string>();

      // 建立节点ID到父节点ID的映射关系
      const buildParentMap = (nodes: MenuTreeOption[], parentId?: string) => {
        for (const node of nodes) {
          const nodeId = node.key as string;
          if (parentId) {
            nodeIdToParentIdMap.set(nodeId, parentId);
          }

          if (node.children && node.children.length > 0) {
            buildParentMap(node.children as MenuTreeOption[], nodeId);
          }
        }
      };

      // 递归收集所有父节点ID
      const collectParentIds = (nodeId: string, idSet: Set<string>) => {
        const parentId = nodeIdToParentIdMap.get(nodeId);
        if (parentId) {
          idSet.add(parentId);
          collectParentIds(parentId, idSet);
        }
      };

      // 开始构建父节点映射
      buildParentMap(tree.value as MenuTreeOption[]);

      // 开始收集子节点
      collectChildrenIds(tree.value as MenuTreeOption[]);

      // 为每个选中的节点收集其所有父节点
      const selectedIdArray = Array.from(selectedIds);
      for (const nodeId of selectedIdArray) {
        collectParentIds(nodeId, selectedIds);
      }

      // 转换为字符串数组
      return Array.from(selectedIds);
    };

    // 获取所有选中节点及其子节点和父节点的ID
    model.menuIds = getAllSelectedMenuIds();
    // 请求API
    const { error } = await fetchAddRoleMenu(model);
    if (!error) {
      window.$message?.success?.($t('common.modifySuccess'));
      closeModal();
    } else {
      window.$message?.error?.($t('common.modifyFailed' as App.I18n.I18nKey));
    }
  } catch {
    window.$message?.error?.($t('common.requestError' as App.I18n.I18nKey));
  } finally {
    loading.value = false;
  }
}

/** 关闭模态框 */
function closeModal() {
  visible.value = false;
}

/** 初始化数据 */
function init() {
  // 重置模型数据
  Object.assign(model, createDefaultModel());

  // 确保初始状态收起所有节点
  expandedKeys.value = [];

  // 获取菜单树和权限数据
  getMenuTreeAndPermissions();
}

// 监听模态框可见性
watch(visible, val => {
  if (val) {
    init();
  }
});

/** 设置节点属性 */
function nodeProps(info: { option: TreeOption }) {
  const { option } = info;
  const hasChildren = option.children && option.children.length > 0;

  return {
    'data-leaf': option.isLeaf === true ? 'true' : 'false',
    'data-type': option.type || '',
    'data-has-children': hasChildren ? 'true' : 'false'
  };
}

/** 过滤冗余的父子节点ID 当所有子节点都被选中时：保留父节点和所有子节点 当有任何子节点未被选中时：移除父节点，保留子节点 */
function filterRedundantParentNodes(menuIds: string[], treeData: TreeOption[]): string[] {
  // 创建一个新的结果集
  const filteredIds = new Set<string>();

  // 存储所有节点的父子关系
  const childToParentMap = new Map<string, string>();
  const parentToChildrenMap = new Map<string, Set<string>>();
  const allNodes = new Set<string>();

  // 构建父子关系映射
  function buildRelationships(nodes: TreeOption[], parentId?: string) {
    for (const node of nodes) {
      const nodeId = node.key as string;
      allNodes.add(nodeId);

      if (parentId) {
        // 记录子到父的映射
        childToParentMap.set(nodeId, parentId);

        // 记录父到子的映射
        if (!parentToChildrenMap.has(parentId)) {
          parentToChildrenMap.set(parentId, new Set<string>());
        }
        parentToChildrenMap.get(parentId)!.add(nodeId);
      }

      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        buildRelationships(node.children, nodeId);
      }
    }
  }

  // 构建所有节点的关系
  buildRelationships(treeData);

  // 获取所有后端返回的有效节点
  const selectedIds = new Set(menuIds.filter(id => id && allNodes.has(id)));

  // 需要移除的父节点ID
  const parentsToRemove = new Set<string>();

  // 第一步：检查父节点的子节点选择情况
  for (const nodeId of selectedIds) {
    // 如果当前节点是子节点，检查它的父节点
    const parentId = childToParentMap.get(nodeId);
    if (parentId && selectedIds.has(parentId)) {
      // 获取所有兄弟节点
      const siblings = parentToChildrenMap.get(parentId)!;
      // 检查是否所有子节点都被选中
      const allSiblingsSelected = Array.from(siblings).every(sibId => selectedIds.has(sibId));

      // 如果有任何子节点未被选中，标记父节点需要移除
      if (!allSiblingsSelected) {
        parentsToRemove.add(parentId);
      }
    }
  }

  // 第二步：将所有选中的节点添加到结果集，除了需要移除的父节点
  for (const nodeId of selectedIds) {
    if (!parentsToRemove.has(nodeId)) {
      filteredIds.add(nodeId);
    }
  }

  return Array.from(filteredIds);
}
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" class="w-480px">
    <NSpin :show="loading">
      <div class="flex flex-col p-2">
        <div class="mb-2 flex justify-end">
          <NButton size="small" class="mr-2" @click="expandAll">全部展开</NButton>
          <NButton size="small" @click="collapseAll">全部收起</NButton>
        </div>

        <div class="tree-container">
          <NTree
            ref="treeRef"
            key="menu-auth-tree"
            v-model:checked-keys="checks"
            v-model:expanded-keys="expandedKeys"
            :data="tree"
            checkable
            cascade
            :render-label="renderLabel"
            class="menu-tree h-400px"
            :node-props="nodeProps"
            :indent="16"
            check-strategy="parent"
            block-line
            selectable
            :expand-on-click="true"
            @update:expanded-keys="handleUpdateExpanded"
            @update:checked-keys="handleUpdateChecked"
          />
        </div>
      </div>
    </NSpin>

    <template #footer>
      <NSpace justify="end">
        <NButton quaternary @click="closeModal">
          {{ $t('common.cancel') }}
        </NButton>
        <NButton type="primary" :loading="loading" @click="handleSubmit">
          {{ $t('common.confirm') }}
        </NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped>
/* 容器基本样式 */
.tree-container {
  border: 1px solid rgba(var(--base-text-color), 0.15);
  border-radius: 4px;
  padding: 8px;
  overflow: auto;
  background-color: rgb(var(--container-bg-color));
}

/* 树基本样式 */
:deep(.menu-tree) {
  font-size: 14px;
}

/* 树节点基本样式 */
:deep(.n-tree-node) {
  position: relative;
  margin: 2px 0;
}

/* 节点内容布局 */
:deep(.n-tree-node-content) {
  display: flex;
  align-items: center;
  padding: 0;
  height: 32px;
  transition: background-color 0.15s;
  border-radius: 2px;
}

:deep(.n-tree-node-content:hover) {
  background-color: rgba(var(--base-text-color), 0.05);
}

/* 箭头样式 */
:deep(.n-tree-node-switcher) {
  display: flex;
  align-items: center;
  height: 32px;
}

:deep(.n-tree-node-switcher .n-base-icon) {
  font-size: 12px;
  color: rgba(var(--base-text-color), 0.6);
}

/* 复选框样式 */
:deep(.n-checkbox-wrapper) {
  display: flex;
  align-items: center;
  height: 32px;
}

:deep(.n-checkbox) {
  margin-right: 8px;
}

/* 文本样式 */
:deep(.n-tree-node-content__text) {
  display: flex;
  align-items: center;
  height: 32px;
}

/* 复选框状态 */
:deep(.n-checkbox--checked .n-checkbox__icon),
:deep(.n-checkbox--indeterminate .n-checkbox__icon) {
  background-color: rgb(var(--primary-color));
  border-color: rgb(var(--primary-color));
}

/* 节点展开/选中状态 */
:deep(.n-tree-node--expanded > .n-tree-node-content) {
  background-color: rgba(var(--base-text-color), 0.08);
  font-weight: 500;
}

:deep(.n-tree-node-content--selected) {
  background-color: rgba(var(--primary-color), 0.1) !important;
}

/* 缩进样式 */
:deep(.n-tree-node-indent) {
  display: flex;
  align-items: center;
  height: 32px;
}
</style>

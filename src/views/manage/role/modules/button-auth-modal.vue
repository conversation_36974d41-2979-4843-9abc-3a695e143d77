<script setup lang="ts">
import { computed, reactive, shallowRef, watch } from 'vue';
import { fetchAddRolePermission, fetchGetMenuPermission, fetchGetRolePermissionIds } from '@/service/api';
import { $t } from '@/locales';

defineOptions({
  name: 'ButtonAuthModal'
});

interface Props {
  /** the roleId */
  roleId: string;
}

const props = defineProps<Props>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const title = computed(
  () => $t('common.edit' as App.I18n.I18nKey) + $t('page.manage.role.buttonAuth' as App.I18n.I18nKey)
);

/** tree checks */
const checks = shallowRef<string[]>([]);

/** menu auth model */
const model: Api.SystemManage.RolePermission = reactive(createDefaultModel());

function createDefaultModel(): Api.SystemManage.RolePermission {
  return {
    roleId: props.roleId,
    permissionIds: []
  };
}

/** menu permission data */
const permissionData = shallowRef<Api.SystemManage.MenuPermission[]>([]);

async function getPermissionData() {
  const { error, data } = await fetchGetMenuPermission();
  if (!error) {
    permissionData.value = data;
  }
}

/** 全选/全不选功能 */
function handleSelectAll() {
  if (!permissionData.value || !Array.isArray(permissionData.value)) return;

  const allButtonIds: string[] = [];
  permissionData.value.forEach(menu => {
    if (menu.buttons && Array.isArray(menu.buttons)) {
      menu.buttons.forEach(button => {
        allButtonIds.push(button.id);
      });
    }
  });
  checks.value = allButtonIds;
}

function handleUnselectAll() {
  checks.value = [];
}

/** 分组全选/全不选功能 */
function handleGroupSelectAll(menuItem: Api.SystemManage.MenuPermission) {
  if (!menuItem.buttons || !Array.isArray(menuItem.buttons)) return;

  const groupButtonIds = menuItem.buttons.map(button => button.id);
  const newChecks = [...checks.value];

  groupButtonIds.forEach(id => {
    if (!newChecks.includes(id)) {
      newChecks.push(id);
    }
  });

  checks.value = newChecks;
}

function handleGroupUnselectAll(menuItem: Api.SystemManage.MenuPermission) {
  if (!menuItem.buttons || !Array.isArray(menuItem.buttons)) return;

  const groupButtonIds = menuItem.buttons.map(button => button.id);
  checks.value = checks.value.filter(id => !groupButtonIds.includes(id));
}

/** 检查分组是否全选 */
function isGroupAllSelected(menuItem: Api.SystemManage.MenuPermission): boolean {
  if (!menuItem.buttons || !Array.isArray(menuItem.buttons) || menuItem.buttons.length === 0) {
    return false;
  }

  const groupButtonIds = menuItem.buttons.map(button => button.id);
  return groupButtonIds.every(id => checks.value.includes(id));
}

/** 检查是否全选 */
function isAllSelected(): boolean {
  if (!permissionData.value || !Array.isArray(permissionData.value) || permissionData.value.length === 0) {
    return false;
  }

  const allButtonIds: string[] = [];
  permissionData.value.forEach(menu => {
    if (menu.buttons && Array.isArray(menu.buttons)) {
      menu.buttons.forEach(button => {
        allButtonIds.push(button.id);
      });
    }
  });

  return allButtonIds.length > 0 && allButtonIds.every(id => checks.value.includes(id));
}

/** init get permissionIds for roleId, belong checks */
async function getPermissionIds() {
  const { error, data } = await fetchGetRolePermissionIds(props.roleId);
  if (!error) {
    checks.value = data;
  }
}

function closeModal() {
  visible.value = false;
}

async function handleSubmit() {
  model.permissionIds = checks.value;
  const { error } = await fetchAddRolePermission(model);
  if (!error) {
    window.$message?.success?.($t('common.modifySuccess' as App.I18n.I18nKey));
    closeModal();
  }
}

async function init() {
  Object.assign(model, createDefaultModel());
  // 同时请求按钮权限数据和角色按钮权限ID，而不是串行请求
  try {
    await Promise.all([getPermissionData(), getPermissionIds()]);
  } catch {}
}

watch(visible, () => {
  if (visible.value) {
    init();
  }
});
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" :segmented="false" class="w-1080px">
    <!-- 全选/全不选按钮 -->
    <div class="permission-header">
      <div class="header-actions">
        <NButton size="small" type="primary" :disabled="isAllSelected()" class="action-btn" @click="handleSelectAll">
          <template #icon>
            <i class="i-carbon-checkmark-filled" />
          </template>
          {{ $t('page.manage.role.selectAll') }}
        </NButton>
        <NButton size="small" quaternary :disabled="checks.length === 0" class="action-btn" @click="handleUnselectAll">
          <template #icon>
            <i class="i-carbon-close-filled" />
          </template>
          {{ $t('page.manage.role.unselectAll') }}
        </NButton>
      </div>
      <div class="selection-count">
        <span class="count-text">{{ $t('page.manage.role.selected') }}</span>
        <span class="count-number">{{ checks.length }}</span>
        <span class="count-text">{{ $t('page.manage.role.items') }}</span>
      </div>
    </div>

    <div class="permission-content">
      <NCheckboxGroup v-model:value="checks" size="small">
        <div class="permission-groups">
          <div v-for="item in permissionData" :key="item.menuId" class="permission-group">
            <!-- 分组标题 -->
            <div class="group-header">
              <div class="group-title">
                <i class="i-carbon-folder mr-2 text-lg" />
                <span class="title-text">{{ $t(item.i18nKey) }}</span>
                <span class="button-count">
                  ({{ (item.buttons || []).length }}{{ $t('page.manage.role.permissions') }})
                </span>
              </div>
              <div class="group-actions">
                <NButton
                  size="tiny"
                  type="primary"
                  text
                  :disabled="isGroupAllSelected(item)"
                  class="group-btn"
                  @click="handleGroupSelectAll(item)"
                >
                  <template #icon>
                    <i class="i-carbon-checkmark" />
                  </template>
                  {{ $t('page.manage.role.selectGroup') }}
                </NButton>
                <NButton
                  size="tiny"
                  quaternary
                  :disabled="
                    !item.buttons || !Array.isArray(item.buttons) || !item.buttons.some(btn => checks.includes(btn.id))
                  "
                  class="group-btn"
                  @click="handleGroupUnselectAll(item)"
                >
                  <template #icon>
                    <i class="i-carbon-close" />
                  </template>
                  {{ $t('page.manage.role.unselectGroup') }}
                </NButton>
              </div>
            </div>

            <!-- 权限按钮列表 -->
            <div class="group-content">
              <div class="permission-grid">
                <div v-for="button in item.buttons || []" :key="button.id" class="permission-item">
                  <NCheckbox :value="button.id" :label="button.name" class="permission-checkbox" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </NCheckboxGroup>
    </div>
    <template #footer>
      <NSpace justify="end">
        <NButton size="small" quaternary @click="closeModal">
          {{ $t('common.cancel') }}
        </NButton>
        <NButton type="primary" size="small" @click="handleSubmit">
          {{ $t('common.confirm') }}
        </NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped>
/* 权限头部样式 */
.permission-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, rgba(var(--primary-color), 0.05) 0%, rgba(var(--primary-color), 0.02) 100%);
  border: 1px solid rgba(var(--primary-color), 0.1);
  border-radius: 8px;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  font-weight: 500;
  transition: all 0.2s ease;
}

.selection-count {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.count-text {
  color: rgba(var(--base-text-color), 0.7);
}

.count-number {
  color: rgb(var(--primary-color));
  font-weight: 600;
  font-size: 16px;
}

/* 权限内容区域 */
.permission-content {
  height: 500px;
  overflow-y: auto;
  padding: 4px;
}

.permission-groups {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 权限分组样式 */
.permission-group {
  border: 1px solid rgba(var(--base-text-color), 0.1);
  border-radius: 12px;
  overflow: hidden;
  background: rgb(var(--container-bg-color));
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
}

.permission-group:hover {
  border-color: rgba(var(--primary-color), 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 分组头部 */
.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(var(--base-text-color), 0.02) 0%, rgba(var(--base-text-color), 0.05) 100%);
  border-bottom: 1px solid rgba(var(--base-text-color), 0.08);
}

.group-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: rgb(var(--base-text-color));
}

.title-text {
  font-size: 15px;
}

.button-count {
  margin-left: 8px;
  font-size: 12px;
  color: rgba(var(--base-text-color), 0.6);
  font-weight: 400;
}

.group-actions {
  display: flex;
  gap: 8px;
}

.group-btn {
  font-size: 12px;
  height: 28px;
  padding: 0 12px;
}

/* 分组内容 */
.group-content {
  padding: 16px;
}

.permission-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.permission-item {
  display: flex;
  align-items: center;
}

.permission-checkbox {
  width: 100%;
}

/* 复选框样式优化 */
:deep(.n-checkbox) {
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

:deep(.n-checkbox:hover) {
  background: rgba(var(--primary-color), 0.05);
  border-color: rgba(var(--primary-color), 0.1);
}

:deep(.n-checkbox--checked) {
  background: rgba(var(--primary-color), 0.08);
  border-color: rgba(var(--primary-color), 0.2);
}

:deep(.n-checkbox__label) {
  font-size: 13px;
  font-weight: 500;
  color: rgb(var(--base-text-color));
}

/* 滚动条样式 */
.permission-content::-webkit-scrollbar {
  width: 6px;
}

.permission-content::-webkit-scrollbar-track {
  background: rgba(var(--base-text-color), 0.05);
  border-radius: 3px;
}

.permission-content::-webkit-scrollbar-thumb {
  background: rgba(var(--base-text-color), 0.2);
  border-radius: 3px;
}

.permission-content::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--base-text-color), 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .permission-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .permission-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 8px;
  }

  .group-header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .group-actions {
    justify-content: center;
  }
}
</style>

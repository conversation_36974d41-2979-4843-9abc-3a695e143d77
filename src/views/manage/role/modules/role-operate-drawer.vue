<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { fetchAddRole, fetchUpdateRoleInfo } from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';
import { $t } from '@/locales';

defineOptions({
  name: 'RoleOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.SystemManage.RoleEdit | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const { dictOptions } = useDict();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: $t('page.manage.role.addRole'),
    edit: $t('page.manage.role.editRole')
  };
  return titles[props.operateType];
});

type Model = Api.SystemManage.RoleEdit;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    id: '',
    roleName: '',
    roleCode: '',
    roleDesc: '',
    status: '1' as any,
    createBy: '',
    updateBy: '',
    createTime: '',
    updateTime: ''
  };
}

type RuleKey = 'roleName' | 'roleCode' | 'status';

const rules: Record<RuleKey, App.Global.FormRule> = {
  roleName: defaultRequiredRule,
  roleCode: defaultRequiredRule,
  status: defaultRequiredRule
};

const isEdit = computed(() => props.operateType === 'edit');

function handleInitModel() {
  Object.assign(model, createDefaultModel());
  if (props.operateType === 'edit' && props.rowData) {
    // 深拷贝行数据并确保状态值正确
    const roleData = { ...props.rowData };

    // 根据API类型定义，status应该是数字类型(1或2)，但表单需要字符串
    if (roleData.status !== undefined && roleData.status !== null) {
      // 确保转换为字符串
      roleData.status = String(roleData.status) as any;
    } else {
      // 如果状态为空，设置默认值
      roleData.status = '1' as any;
    }

    Object.assign(model, roleData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  let submitData: Partial<Api.SystemManage.RoleEdit>;
  // 获取当前登录用户名
  const currentUser = getCurrentUserName();
  if (isEdit.value) {
    // 编辑模式：只提交ID和已修改的字段
    submitData = {
      id: model.id,
      roleName: model.roleName,
      roleCode: model.roleCode,
      roleDesc: model.roleDesc,
      status: model.status,
      updateBy: currentUser
    };
  } else {
    // 新增模式：提交所有必要字段，但不包括ID和时间字段
    submitData = {
      roleName: model.roleName,
      roleCode: model.roleCode,
      roleDesc: model.roleDesc,
      status: model.status,
      createBy: currentUser,
      updateBy: currentUser
    };
  }

  // 选择对应的API函数
  const func = isEdit.value ? fetchUpdateRoleInfo : fetchAddRole;
  // 发送请求
  const { error } = await func(submitData as Api.SystemManage.RoleEdit);
  if (!error) {
    window.$message?.success(isEdit.value ? $t('common.updateSuccess') : $t('common.addSuccess'));
    closeDrawer();
    emit('submitted');
  }
}

// 获取当前登录用户名
function getCurrentUserName() {
  // 从authStore中获取当前登录用户信息
  const authStore = useAuthStore();
  return authStore.userInfo.userName || 'system';
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="360">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem :label="$t('page.manage.role.roleName')" path="roleName">
          <NInput v-model:value="model.roleName" :placeholder="$t('page.manage.role.form.roleName')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.role.roleCode')" path="roleCode">
          <NInput v-model:value="model.roleCode" :placeholder="$t('page.manage.role.form.roleCode')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.role.status')" path="status">
          <NRadioGroup v-model:value="model.status">
            <NRadio v-for="item in dictOptions('status')" :key="item.value" :value="item.value" :label="item.label" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem :label="$t('page.manage.role.roleDesc')" path="roleDesc">
          <NInput v-model:value="model.roleDesc" :placeholder="$t('page.manage.role.form.roleDesc')" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace>
          <NButton quaternary @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>

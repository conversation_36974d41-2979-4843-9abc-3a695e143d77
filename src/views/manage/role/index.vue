<script setup lang="tsx">
import { N<PERSON><PERSON>on, NPopconfirm } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { fetchDeleteRole, fetchDeleteRoleBatch, fetchGetRoleList } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { useAuth } from '@/hooks/business/auth';
import { useDict } from '@/hooks/business/dict';
import { $t } from '@/locales';
import NoPermission from '@/components/common/no-permission.vue';
import RoleOperateDrawer from './modules/role-operate-drawer.vue';
import RoleSearch from './modules/role-search.vue';
import MenuAuthModal from './modules/menu-auth-modal.vue';
import ButtonAuthModal from './modules/button-auth-modal.vue';

const appStore = useAppStore();

const { bool: menuModalVisible, setTrue: openMenuModal } = useBoolean();

const { bool: buttonModalVisible, setTrue: openButtonModal } = useBoolean();

const { hasAuth } = useAuth();

const { dictTag } = useDict();

const {
  columns,
  columnChecks,
  data,
  loading,
  getData,
  getDataByPage,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetRoleList,
  apiParams: {
    current: 1,
    size: 10,
    // if you want to use the searchParams in Form, you need to define the following properties, and the value is null
    // the value can not be undefined, otherwise the property in Form will not be reactive
    roleName: null,
    roleCode: null,
    status: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'index',
      title: $t('common.index'),
      width: 64,
      align: 'center'
    },
    {
      key: 'roleName',
      title: $t('page.manage.role.roleName'),
      align: 'center',
      minWidth: 120
    },
    {
      key: 'roleCode',
      title: $t('page.manage.role.roleCode'),
      align: 'center',
      minWidth: 120
    },
    {
      key: 'status',
      title: $t('page.manage.role.status'),
      align: 'center',
      width: 100,
      render: row => dictTag('status', row.status)
    },
    {
      key: 'roleDesc',
      title: $t('page.manage.role.roleDesc'),
      align: 'center',
      minWidth: 120
    },
    // 只有当用户有任意操作权限时才显示操作列
    ...(hasAuth('sys:role:menu:add') ||
    hasAuth('sys:role:permission:add') ||
    hasAuth('sys:role:update') ||
    hasAuth('sys:role:delete')
      ? [
          {
            key: 'operate',
            title: $t('common.operate'),
            align: 'center',
            width: 300,
            render: row => (
              <div class="flex-center gap-8px">
                {hasAuth('sys:role:menu:add') && (
                  <NButton type="primary" quaternary size="small" onClick={() => handleMenuAuth(row.id)}>
                    {$t('page.manage.role.menuAuth')}
                  </NButton>
                )}
                {hasAuth('sys:role:permission:add') && (
                  <NButton type="primary" quaternary size="small" onClick={() => handleButtonAuth(row.id)}>
                    {$t('page.manage.role.buttonAuth')}
                  </NButton>
                )}
                {hasAuth('sys:role:update') && (
                  <NButton type="primary" quaternary size="small" onClick={() => edit(row.id)}>
                    {$t('common.edit')}
                  </NButton>
                )}
                {hasAuth('sys:role:delete') && (
                  <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
                    {{
                      default: () => $t('common.confirmDelete'),
                      trigger: () => (
                        <NButton type="error" quaternary size="small">
                          {$t('common.delete')}
                        </NButton>
                      )
                    }}
                  </NPopconfirm>
                )}
              </div>
            )
          }
        ]
      : [])
  ]
});

const {
  drawerVisible,
  operateType,
  editingId,
  editingData,
  handleId,
  handleAdd,
  handleEdit,
  checkedRowKeys,
  onBatchDeleted,
  onDeleted
} = useTableOperate(data as any, getData);

async function handleBatchDelete() {
  // request
  const ids = checkedRowKeys.value.map(id => Number(id));
  const { error } = await fetchDeleteRoleBatch(ids);
  if (!error) {
    onBatchDeleted();
  }
}

async function handleDelete(id: string) {
  // request
  const { error } = await fetchDeleteRole(Number(id));
  if (!error) {
    onDeleted();
  }
}

function edit(id: string) {
  handleEdit(id);
}

function handleMenuAuth(id: string) {
  handleId(id);
  openMenuModal();
}

function handleButtonAuth(id: string) {
  handleId(id);
  openButtonModal();
}
</script>

<template>
  <div v-if="hasAuth('sys:role:view')" class="min-h-500px flex-col-stretch gap-8px overflow-hidden lt-sm:overflow-auto">
    <RoleSearch v-model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper" content-class="flex-col">
      <TableHeaderOperation
        v-model:columns="columnChecks"
        :checked-row-keys="checkedRowKeys"
        :loading="loading"
        add-auth="sys:role:add"
        delete-auth="sys:role:delete"
        @add="handleAdd"
        @delete="handleBatchDelete"
        @refresh="getData"
      />
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        remote
        striped
        size="small"
        class="sm:h-full"
        :data="data"
        :scroll-x="962"
        :columns="columns as any"
        :flex-height="!appStore.isMobile"
        :loading="loading"
        :single-line="false"
        :row-key="row => row.id"
        :pagination="mobilePagination"
      />
      <RoleOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData as any"
        @submitted="getDataByPage"
      />
      <MenuAuthModal v-model:visible="menuModalVisible" :role-id="editingId" />
      <ButtonAuthModal v-model:visible="buttonModalVisible" :role-id="editingId" />
    </NCard>
  </div>
  <NoPermission v-else :description="$t('page.manage.role.noPermissionDesc')" />
</template>

<style scoped></style>

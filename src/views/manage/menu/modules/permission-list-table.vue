<script setup lang="tsx">
import { watch } from 'vue';
import { N<PERSON>utton, NPopconfirm } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { fetchDeletePermission, fetchGetPermissionList } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { useAuth } from '@/hooks/business/auth';
import { useDict } from '@/hooks/business/dict';
import { $t } from '@/locales';
import NoPermission from '@/components/common/no-permission.vue';
import type { MenuTreeModel } from '../index.vue';
import PermissionOperateModal from './permission-operate-modal.vue';

defineOptions({
  name: 'PermissionListTable'
});

interface Props {
  /** select data */
  showData: MenuTreeModel;
}

const props = defineProps<Props>();

const appStore = useAppStore();

const { hasAuth } = useAuth();

const { dictTag } = useDict();

const { bool: modalVisible, setTrue: openModalVisible } = useBoolean();

const { columns, data, loading, mobilePagination, searchParams, getData, getDataByPage } = useTable<
  typeof fetchGetPermissionList
>({
  apiFn: fetchGetPermissionList,
  apiParams: {
    current: 1,
    size: 10,
    menuId: props.showData.id,
    name: null
  },
  columns: () => [
    {
      key: 'name' as keyof Api.SystemManage.Permission,
      title: $t('page.manage.permission.name'),
      align: 'center',
      width: 150
    },
    {
      key: 'resource' as keyof Api.SystemManage.Permission,
      title: $t('page.manage.permission.resource'),
      align: 'center',
      width: 200,
      ellipsis: true
    },
    {
      key: 'status' as keyof Api.SystemManage.Permission,
      title: $t('page.manage.permission.status'),
      align: 'center',
      width: 50,
      render: row => dictTag('status', row.status)
    },
    {
      key: 'sort' as keyof Api.SystemManage.Permission,
      title: $t('page.manage.permission.sort'),
      align: 'center',
      width: 50
    },
    {
      key: 'description' as keyof Api.SystemManage.Permission,
      title: $t('page.manage.permission.description'),
      align: 'center',
      width: 120,
      ellipsis: {
        tooltip: true
      }
    },
    // 只有当用户有编辑或删除权限时才显示操作列
    ...(hasAuth('sys:permission:update') || hasAuth('sys:permission:delete')
      ? [
          {
            key: 'operate' as const,
            title: $t('common.operate'),
            align: 'center',
            width: 80,
            fixed: 'right',
            render: row => (
              <div class="flex-center gap-8px">
                {hasAuth('sys:permission:update') && (
                  <NButton type="primary" quaternary size="small" onClick={() => handleEditButton(row.id)}>
                    {$t('common.edit')}
                  </NButton>
                )}
                {hasAuth('sys:permission:delete') && (
                  <NPopconfirm onPositiveClick={() => handleDeleteButton(row.id)}>
                    {{
                      default: () => $t('common.confirmDelete'),
                      trigger: () => (
                        <NButton type="error" quaternary size="small">
                          {$t('common.delete')}
                        </NButton>
                      )
                    }}
                  </NPopconfirm>
                )}
              </div>
            )
          }
        ]
      : [])
  ]
});

const { operateType, handleEdit, onDeleted, editingData } = useTableOperate<Api.SystemManage.Permission>(data, getData);

/** add permission button */
function handleAddButton() {
  operateType.value = 'add';
  openModalVisible();
}

/** edit permission button */
function handleEditButton(id: string) {
  operateType.value = 'edit';
  handleEdit(Number(id));
  openModalVisible();
}

/** delete permission button */
async function handleDeleteButton(id: string) {
  try {
    const { error } = await fetchDeletePermission(Number(id));
    // 确保组件还在活动状态
    if (!error && document.body.contains(document.querySelector('.permission-list-table'))) {
      onDeleted();
    }
  } catch {}
}

// query permisson button data with menu id.
watch(props.showData, () => {
  searchParams.menuId = props.showData.id;
  getDataByPage();
});
</script>

<template>
  <div v-if="hasAuth('sys:permission:view')" class="permission-list-table flex flex-grow">
    <NCard v-if="showData.type === '2'" :title="$t('page.manage.permission.title')" :bordered="false" size="small">
      <template #header-extra>
        <NButton v-if="hasAuth('sys:permission:add')" type="primary" ghost size="small" @click="handleAddButton()">
          {{ $t('common.add') }}
        </NButton>
      </template>
      <NDataTable
        remote
        striped
        size="small"
        class="sm:h-full"
        :data="data"
        :scroll-x="962"
        :columns="columns as any"
        :flex-height="!appStore.isMobile"
        :loading="loading"
        :single-line="false"
        :row-key="row => row.id"
        :pagination="mobilePagination"
      />
    </NCard>
    <NCard v-else :bordered="false" size="small">
      <NEmpty :description="$t('page.manage.menu.menuTypeIsDirectory')" class="h-full justify-center" />
    </NCard>
    <PermissionOperateModal
      v-model:visible="modalVisible"
      :operate-type="operateType"
      :menu-data="showData"
      :row-data="editingData"
      @submitted="getDataByPage()"
    />
  </div>
  <NoPermission v-else :description="$t('page.manage.permission.noPermissionDesc')" :show-back-button="false" />
</template>

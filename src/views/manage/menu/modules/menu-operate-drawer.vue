<script setup lang="tsx">
import { computed, reactive, ref, watch } from 'vue';
import type { SelectOption } from 'naive-ui';
import type { LastLevelRouteKey } from '@elegant-router/types';
import { fetchAddMenu, fetchGetEditMenuInfo, fetchUpdateMenuInfo } from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';
import { getLocalIcons } from '@/utils/icon';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { $t } from '@/locales';
import type { MenuTreeModel } from '../index.vue';
import {
  getLayoutAndPage,
  getPathParamFromRoutePath,
  getRoutePathByRouteName,
  transformLayoutAndPageToComponent
} from './shared';

defineOptions({
  name: 'MenuOperateDrawer'
});

export type OperateType = NaiveUI.TableOperateType | 'addChild';

interface Props {
  /** the type of operation */
  operateType: OperateType;
  /** the edit menu data or the parent menu data when adding a child menu */
  rowData?: MenuTreeModel | null;
  /** all pages */
  allPages: string[];
}

const props = defineProps<Props>();

type MenuEdit = {
  id: string;
  type: string;
  name: string;
  menuType: '1' | '2' | '3' | '4';
  i18nKey: string | null;
  routeName: string;
  routePath: string;
  component: string;
  icon: string;
  iconType: '1' | '2';
  status: string;
  hide: string;
  sort: number;
  href: string;
  iframeUrl: string;
  keepAlive: string;
  parentId: string;
  multiTab: string;
  activeMenu: LastLevelRouteKey;
  fixedIndexInTab: number;
  // query: Array<{ key: string; value: string }>;
};

type Model = MenuEdit & {
  // query: NonNullable<MenuEdit['query']>;
  layout: string;
  page: string;
  // pathParam: string;
  key: string;
  label: string;
};

interface Emits {
  (e: 'submitted', data: Model): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { dictOptions } = useDict();

// 菜单类型选项（确保动态获取最新的翻译）
const menuTypeOptions = computed(() => [
  { value: '1', label: $t('page.manage.menu.menuTypeEnum.topDirectory') },
  { value: '2', label: $t('page.manage.menu.menuTypeEnum.subMenu') },
  { value: '3', label: $t('page.manage.menu.menuTypeEnum.singleMenu') },
  { value: '4', label: $t('page.manage.menu.menuTypeEnum.specialPage') }
]);
const authStore = useAuthStore();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: $t('page.manage.menu.addMenu'),
    addChild: $t('page.manage.menu.addChildMenu'),
    edit: $t('page.manage.menu.editMenu')
  };
  return titles[props.operateType];
});

const model: Model = reactive(createDefaultModel());

const originalPage = ref('');

function createDefaultModel(): Model {
  return {
    id: '',
    type: '1',
    name: '',
    menuType: '1',
    i18nKey: null,
    routeName: '',
    routePath: '',
    // pathParam: '',
    component: '',
    icon: '',
    iconType: '1',
    layout: '',
    page: '',
    status: '1',
    hide: 'N',
    sort: 0,
    href: '',
    iframeUrl: '',
    keepAlive: 'Y',
    parentId: '0',
    multiTab: 'N',
    activeMenu: '' as LastLevelRouteKey,
    fixedIndexInTab: -1,
    key: '',
    label: ''
  };
}

type RuleKey = Extract<keyof Model, 'name' | 'status' | 'routeName' | 'routePath'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule,
  status: defaultRequiredRule,
  routeName: defaultRequiredRule,
  routePath: defaultRequiredRule
};

const disabledMenuType = computed(() => props.operateType === 'edit');

const localIcons = getLocalIcons();
const localIconOptions = localIcons.map<SelectOption>(item => ({
  label: () => (
    <div class="flex-y-center gap-16px">
      <SvgIcon localIcon={item} class="text-icon" />
      <span>{item}</span>
    </div>
  ),
  value: item
}));

const showLayout = computed(() => {
  // 所有类型都显示布局选项（但选项会根据类型自动限制）
  return true;
});

const showPage = computed(() => {
  // 只有需要页面组件的菜单类型才显示页面选项
  return ['2', '3', '4'].includes(model.type) || Boolean(model.routeName);
});

// has external link
const hasExternalLink = computed(() => Boolean(model.href || model.iframeUrl));

const pageOptions = computed(() => {
  const allPages = [...props.allPages];

  // has external link add iframe-page
  if (hasExternalLink.value) {
    allPages.unshift('iframe-page');
  } else if (model.routeName && !allPages.includes(model.routeName)) {
    allPages.unshift(model.routeName);
  }

  const opts: CommonType.Option[] = allPages.map(page => ({
    label: page,
    value: page
  }));

  return opts;
});

const layoutOptions = computed(() => {
  const opts: CommonType.Option[] = [];

  // 根据新的4种菜单类型自动设置布局选项
  /* eslint-disable default-case */
  switch (model.type) {
    case '1': // 目录
      opts.push({
        label: 'base (目录)',
        value: 'base'
      });
      break;
    case '2': // 子菜单
      opts.push({
        label: '无布局 (继承父级)',
        value: ''
      });
      break;
    case '3': // 顶级菜单
      opts.push({
        label: 'base (顶级菜单)',
        value: 'base'
      });
      break;
    case '4': // 特殊页面
      opts.push({
        label: 'blank (特殊页面)',
        value: 'blank'
      });
      break;
  }

  return opts;
});

const isEdit = computed(() => props.operateType === 'edit');

async function handleInitModel() {
  Object.assign(model, createDefaultModel());

  if (!props.rowData) return;

  if (props.operateType === 'addChild') {
    const { id } = props.rowData;
    Object.assign(model, { parentId: id });
    return;
  }

  if (props.operateType === 'edit') {
    const { error, data } = await fetchGetEditMenuInfo(props.rowData?.id);
    if (!error) {
      const { component, ...rest } = data;
      const { layout, page } = getLayoutAndPage(component);
      const { path, param } = getPathParamFromRoutePath(rest.routePath);
      Object.assign(model, rest, { layout, page, routePath: path, pathParam: param });

      // 确保 type 和 menuType 保持同步
      // if (rest.type) {
      //   model.menuType = rest.type;
      // }
    }
  }
}

function closeDrawer() {
  visible.value = false;
}

function handleUpdateRoutePathByRouteName() {
  if (model.routeName) {
    model.routePath = getRoutePathByRouteName(model.routeName);
  } else {
    model.routePath = '';
  }
}

function handleUpdateI18nKeyByRouteName() {
  if (model.routeName) {
    model.i18nKey = `route.${model.routeName}` as App.I18n.I18nKey;
  } else {
    model.i18nKey = null;
  }
}

function getSubmitParams() {
  const { layout, page, ...params } = model;

  const component = transformLayoutAndPageToComponent(layout, page);
  // const routePath = getRoutePathWithParam(model.routePath, pathParam);

  params.component = component;
  // params.routePath = routePath;
  params.routePath = model.routePath;

  return params;
}

async function handleSubmit() {
  await validate();

  const params = getSubmitParams();

  // 获取当前用户信息
  const userName = authStore.userInfo.userName || 'system';
  const userId = authStore.userInfo.userId || '0';

  // 根据操作类型设置不同的字段
  if (!isEdit.value) {
    // 新增时需要创建和修改用户信息
    Object.assign(params, {
      createUser: userName,
      createUserId: userId,
      updateUser: userName,
      updateUserId: userId
    });

    // 新增时不应该传递ID字段，删除ID字段
    if (props.operateType === 'add') {
      delete params.id;
    }
  } else {
    // 编辑时只需要修改用户信息
    Object.assign(params, {
      updateUser: userName,
      updateUserId: userId
    });
  }

  // 设置 key 和 label
  model.key = model.id;
  model.label = $t(model.i18nKey as App.I18n.I18nKey) || model.name;

  // request
  const func = isEdit.value ? fetchUpdateMenuInfo : fetchAddMenu;
  const { error } = await func(params);
  if (!error) {
    window.$message?.success(isEdit.value ? $t('common.updateSuccess') : $t('common.addSuccess'));
    closeDrawer();
    emit('submitted', model);
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});

watch(
  () => model.routeName,
  () => {
    handleUpdateRoutePathByRouteName();
    handleUpdateI18nKeyByRouteName();
  }
);

watch(
  () => hasExternalLink.value,
  newValue => {
    if (newValue) {
      // 保存原始页面设置
      originalPage.value = model.page;
      model.page = 'iframe-page';
    } else if (originalPage.value && originalPage.value !== 'iframe-page') {
      model.page = originalPage.value;
    } else if (model.routeName) {
      model.page = model.routeName;
    } else {
      model.page = '';
    }
  }
);

// 监听菜单类型变化，自动调整布局和页面设置
watch(
  () => model.type,
  newType => {
    // 保持 type 和 menuType 同步
    model.menuType = newType as '1' | '2' | '3' | '4';
    /* eslint-disable default-case */
    switch (newType) {
      case '1': // 目录
        model.layout = 'base';
        model.page = '';
        break;
      case '2': // 子菜单
        model.layout = '';
        if (model.routeName) {
          model.page = model.routeName;
        }
        break;
      case '3': // 顶级菜单
        model.layout = 'base';
        if (model.routeName) {
          model.page = model.routeName;
        }
        break;
      case '4': // 特殊页面
        model.layout = 'blank';
        if (model.routeName) {
          model.page = model.routeName;
        }
        break;
    }
  }
);

// 新的4种菜单类型不再需要监听父级变化，因为类型已经明确了组件结构
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" width="sm:450 s:360">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="80">
        <NGrid>
          <NFormItemGi span="12" :label="$t('page.manage.menu.type')" path="type">
            <NRadioGroup v-model:value="model.type" :disabled="disabledMenuType">
              <NRadio v-for="item in menuTypeOptions" :key="item.value" :value="item.value" :label="item.label" />
            </NRadioGroup>
          </NFormItemGi>
          <NFormItemGi span="12" :label="$t('page.manage.menu.sort')" path="sort">
            <NInputNumber v-model:value="model.sort" :placeholder="$t('page.manage.menu.form.sort')" />
          </NFormItemGi>
          <NFormItemGi span="12" :label="$t('page.manage.menu.status')" path="status">
            <NRadioGroup v-model:value="model.status">
              <NRadio v-for="item in dictOptions('status')" :key="item.value" :value="item.value" :label="item.label" />
            </NRadioGroup>
          </NFormItemGi>
          <NFormItemGi span="12" :label="$t('page.manage.menu.hideInMenu')" path="hide">
            <NRadioGroup v-model:value="model.hide">
              <NRadio
                v-for="item in dictOptions('feature_status')"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </NRadioGroup>
          </NFormItemGi>
          <NFormItemGi span="24" :label="$t('page.manage.menu.keepAlive')" path="keepAlive">
            <NRadioGroup v-model:value="model.keepAlive">
              <NRadio
                v-for="item in dictOptions('feature_status')"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </NRadioGroup>
          </NFormItemGi>
          <NFormItemGi span="12" :label="$t('page.manage.menu.multiTab')" path="multiTab">
            <NRadioGroup v-model:value="model.multiTab">
              <NRadio
                v-for="item in dictOptions('feature_status')"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </NRadioGroup>
          </NFormItemGi>
          <NFormItemGi span="12" :label="$t('page.manage.menu.fixedIndexInTab')" path="fixedIndexInTab">
            <NInputNumber
              v-model:value="model.fixedIndexInTab"
              :placeholder="$t('page.manage.menu.form.fixedIndexInTab')"
            />
          </NFormItemGi>
          <NFormItemGi span="24" :label="$t('page.manage.menu.name')" path="name">
            <NInput v-model:value="model.name" :placeholder="$t('page.manage.menu.form.name')" :disabled="isEdit" />
          </NFormItemGi>
          <NFormItemGi span="24" :label="$t('page.manage.menu.routeName')" path="routeName">
            <NInput
              v-model:value="model.routeName"
              :placeholder="$t('page.manage.menu.form.routeName')"
              :disabled="isEdit"
            />
          </NFormItemGi>
          <NFormItemGi span="24" :label="$t('page.manage.menu.i18nKey')" path="i18nKey">
            <NInput
              v-model:value="model.i18nKey"
              :placeholder="$t('page.manage.menu.form.i18nKey')"
              :disabled="isEdit"
            />
          </NFormItemGi>
          <NFormItemGi span="24" :label="$t('page.manage.menu.iconTypeTitle')" path="iconType">
            <NRadioGroup v-model:value="model.iconType">
              <NRadio
                v-for="item in dictOptions('menu_icon_type')"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </NRadioGroup>
          </NFormItemGi>
          <NFormItemGi span="24" :label="$t('page.manage.menu.icon')" path="icon">
            <template v-if="model.iconType === '1'">
              <NInput v-model:value="model.icon" :placeholder="$t('page.manage.menu.form.icon')" class="flex-1">
                <template #suffix>
                  <SvgIcon v-if="model.icon" :icon="model.icon" class="text-icon" />
                </template>
              </NInput>
            </template>
            <template v-if="model.iconType === '2'">
              <NSelect
                v-model:value="model.icon"
                :placeholder="$t('page.manage.menu.form.localIcon')"
                :options="localIconOptions"
              />
            </template>
          </NFormItemGi>
          <NFormItemGi span="24" :label="$t('page.manage.menu.routePath')" path="routePath">
            <NInput v-model:value="model.routePath" disabled :placeholder="$t('page.manage.menu.form.routePath')" />
          </NFormItemGi>
          <NFormItemGi v-if="showLayout" span="24" :label="$t('page.manage.menu.layout')" path="layout">
            <NSelect
              v-model:value="model.layout"
              :options="layoutOptions"
              :placeholder="$t('page.manage.menu.form.layout')"
            />
            <div class="component-tip mt-1 text-xs">
              <template v-if="model.type === '1'">
                {{ $t('page.manage.menu.componentTips.topDirectory') }}
              </template>
              <template v-else-if="model.type === '2'">
                {{ $t('page.manage.menu.componentTips.subMenu') }}
              </template>
              <template v-else-if="model.type === '3'">
                {{ $t('page.manage.menu.componentTips.singleMenu') }}
              </template>
              <template v-else-if="model.type === '4'">
                {{ $t('page.manage.menu.componentTips.specialPage') }}
              </template>
            </div>
          </NFormItemGi>
          <NFormItemGi v-if="showPage" span="24" :label="$t('page.manage.menu.page')" path="page">
            <NSelect
              v-model:value="model.page"
              :options="pageOptions"
              :placeholder="$t('page.manage.menu.form.page')"
            />
            <div class="component-tip mt-1 text-xs">
              <template v-if="model.type === '1'">
                {{ $t('page.manage.menu.componentTips.topDirectoryNoPage') }}
              </template>
              <template v-else-if="model.type === '2'">
                {{ $t('page.manage.menu.componentTips.subMenuNeedPage') }}
              </template>
              <template v-else-if="model.type === '3'">
                {{ $t('page.manage.menu.componentTips.singleMenuNeedPage') }}
              </template>
              <template v-else-if="model.type === '4'">
                {{ $t('page.manage.menu.componentTips.specialPageNeedPage') }}
              </template>
            </div>
          </NFormItemGi>
          <!--
 <NFormItemGi span="24" :label="$t('page.manage.menu.pathParam')" path="pathParam">
            <NInput v-model:value="model.pathParam" :placeholder="$t('page.manage.menu.form.pathParam')" />
          </NFormItemGi>
-->
          <NFormItemGi span="24" :label="$t('page.manage.menu.href')" path="href">
            <NInput v-model:value="model.href" :placeholder="$t('page.manage.menu.form.href')" />
          </NFormItemGi>
          <NFormItemGi span="24" :label="$t('page.manage.menu.iframeUrl')" path="iframeUrl">
            <NInput v-model:value="model.iframeUrl" :placeholder="$t('page.manage.menu.form.iframeUrl')" />
          </NFormItemGi>
          <!--
 <NFormItemGi span="24" :label="$t('page.manage.menu.query')">
            <NDynamicInput
              v-model:value="model.query"
              preset="pair"
              :key-placeholder="$t('page.manage.menu.form.queryKey')"
              :value-placeholder="$t('page.manage.menu.form.queryValue')"
            >
              <template #action="{ index, create, remove }">
                <NSpace class="ml-8px">
                  <NButton size="medium" @click="() => create(index)">
                    <icon-ic:round-plus class="text-icon" />
                  </NButton>
                  <NButton size="medium" @click="() => remove(index)">
                    <icon-ic-round-remove class="text-icon" />
                  </NButton>
                </NSpace>
              </template>
            </NDynamicInput>
          </NFormItemGi>
-->
        </NGrid>
      </NForm>
      <template #footer>
        <NSpace :size="12" justify="end">
          <NButton quaternary @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

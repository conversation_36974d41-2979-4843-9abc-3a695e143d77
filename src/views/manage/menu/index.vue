<script setup lang="tsx">
import type { Ref } from 'vue';
import { h, reactive, ref, shallowRef } from 'vue';
import { NEmpty, NTag } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { fetchDeleteMenu, fetchGetAllPages, fetchGetMenuTree } from '@/service/api';
import { useAuth } from '@/hooks/business/auth';
import { useDict } from '@/hooks/business/dict';
import { $t } from '@/locales';
import NoPermission from '@/components/common/no-permission.vue';
import SvgIcon from '@/components/custom/svg-icon.vue';
import PermissionListTable from './modules/permission-list-table.vue';
import MenuOperateDrawer, { type OperateType } from './modules/menu-operate-drawer.vue';

const { bool: detailVisible, setBool: setDetailVisible, setFalse: hideDetail } = useBoolean();

const { bool: menuDrawerVisible, setTrue: openMenuDrawer } = useBoolean();

const { hasAuth } = useAuth();

const { dcitType, dictLabel } = useDict();

const operateType = ref<OperateType>('add');

// 1. 自定义类型，完全以实际用法为准
export interface MenuTreeModel {
  id: string;
  routeName: string;
  routePath: string;
  icon: string;
  iconType: string;
  status: string;
  hideInMenu?: string;
  href: string;
  sort: number;
  parentId: string;
  key: string;
  label: string;
  children?: MenuTreeModel[];
  keepAlive?: string;
  prefix?: () => any;
  i18nKey?: string;
  type: string;
  name: string;
  hide: string;
  iframeUrl: string;
  multiTab?: string;
  [key: string]: any;
}

/** tree data */
const tree = shallowRef<MenuTreeModel[]>([]);

/** tree pattern name , use tree search */
const name: Ref<string> = ref('');

/** the select menu data */
const showData: MenuTreeModel = reactive({
  id: '0',
  type: '1',
  routeName: '',
  routePath: '',
  icon: '',
  iconType: '1',
  status: '1',
  hideInMenu: 'N',
  href: '',
  sort: 0,
  parentId: '0',
  key: '0',
  label: '',
  name: '',
  hide: '',
  iframeUrl: '',
  keepAlive: 'Y',
  multiTab: 'N'
});

/** get tree data */
async function getTree() {
  const { error, data } = await fetchGetMenuTree();
  if (!error) {
    tree.value = data.map(recursive);
  }
}

/** recursive menu tree data, add prefix transform treeOption format */
function recursive(item: any): MenuTreeModel {
  const result: MenuTreeModel = {
    ...item,
    key: item.id,
    label: $t(item.i18nKey as App.I18n.I18nKey) || item.name,
    prefix: () => {
      const icon = item.iconType === '1' ? item.icon : undefined;
      const localIcon = item.iconType === '2' ? item.icon : undefined;
      return h(SvgIcon, {
        icon,
        localIcon,
        class: 'text-icon'
      });
    }
  };
  if (item.children) {
    result.children = item.children.map(recursive);
  }
  return result;
}

/** tree select handle */
function handleSelectKeys(node: any, action: string) {
  setDetailVisible(action === 'select');
  if (detailVisible && node) {
    Object.assign(showData, node as MenuTreeModel);
  }
}

const allPages = shallowRef<string[]>([]);

async function getAllPages() {
  const { data: pages } = await fetchGetAllPages();
  allPages.value = pages?.map(page => page.name) || [];
}

function handleAddMenu() {
  operateType.value = 'add';
  openMenuDrawer();
}

function handleAddChildMenu() {
  operateType.value = 'addChild';
  openMenuDrawer();
}

function handleEditMenu() {
  operateType.value = 'edit';
  openMenuDrawer();
}

async function handleDeleteMenu() {
  // request
  const { error } = await fetchDeleteMenu(Number(showData.id));
  if (!error) {
    window.$message?.success($t('common.deleteSuccess'));
    init(null);
    hideDetail();
  }
}

function init(data: MenuTreeModel | null) {
  if (data) {
    const menuData = {
      ...data,
      key: data.id,
      label: $t(data.i18nKey as App.I18n.I18nKey) || data.name
    };
    Object.assign(showData, menuData);
  }
  getTree();
  getAllPages();
}

init(null);
</script>

<template>
  <div v-if="hasAuth('sys:menu:view')" class="flex overflow-hidden">
    <NGrid
      :x-gap="8"
      :y-gap="8"
      item-responsive
      responsive="screen"
      cols="1 s:1 m:5 l:5 xl:5 2xl:5"
      class="h-full-hidden"
    >
      <NGridItem span="1" class="h-full-hidden">
        <NCard
          :title="$t('page.manage.menu.title')"
          :bordered="false"
          size="small"
          class="h-full sm:flex-1-hidden"
          content-class="h-full-hidden"
        >
          <template #header-extra>
            <NFlex>
              <NButton v-if="hasAuth('sys:menu:add')" ghost type="primary" size="small" @click="handleAddMenu()">
                {{ $t('common.add') }}
              </NButton>
              <NButton quaternary @click="init(null)">
                <template #icon>
                  <SvgIcon icon="ic:round-refresh" />
                </template>
              </NButton>
            </NFlex>
          </template>
          <div class="h-full flex flex-col">
            <NInput v-model:value="name" class="mb-2" clearable :placeholder="$t('page.manage.menu.form.name')" />
            <NTree
              :data="tree"
              :pattern="name"
              accordion
              block-line
              class="p-tree flex-col-stretch"
              key-field="id"
              virtual-scroll
              :show-irrelevant-nodes="false"
              @update-selected-keys="(_key, _option, { node, action }) => handleSelectKeys(node, action)"
            />
          </div>
        </NCard>
      </NGridItem>
      <NGridItem v-if="detailVisible" span="4" class="flex flex-col">
        <NCard :title="$t('page.manage.menu.detail')" :bordered="false" size="small" class="mb-2">
          <template #header-extra>
            <NFlex>
              <NButton
                v-if="(showData.type === '1' || showData.type === '3') && hasAuth('sys:menu:add')"
                type="primary"
                quaternary
                size="small"
                @click="handleAddChildMenu()"
              >
                {{ $t('page.manage.menu.addChildMenu') }}
              </NButton>
              <NButton v-if="hasAuth('sys:menu:update')" ghost type="primary" size="small" @click="handleEditMenu()">
                {{ $t('common.edit') }}
              </NButton>
              <NPopconfirm v-if="hasAuth('sys:menu:delete')" placement="bottom" @positive-click="handleDeleteMenu">
                <template #trigger>
                  <NButton ghost type="error" size="small">
                    {{ $t('common.delete') }}
                  </NButton>
                </template>
                {{ $t('common.confirmDelete') }}
              </NPopconfirm>
            </NFlex>
          </template>
          <NDescriptions label-placement="left" size="small" bordered :column="2">
            <NDescriptionsItem :label="$t('page.manage.menu.type')">
              <NTag :type="dcitType('menu_type', showData.type) || 'default'">
                {{ dictLabel('menu_type', showData.type) }}
              </NTag>
            </NDescriptionsItem>
            <NDescriptionsItem :label="$t('page.manage.menu.status')">
              <NTag :type="dcitType('status', showData.status) || 'default'">
                {{ dictLabel('status', showData.status) }}
              </NTag>
            </NDescriptionsItem>
            <NDescriptionsItem :label="$t('page.manage.menu.name')">{{ showData.name }}</NDescriptionsItem>
            <NDescriptionsItem :label="$t('page.manage.menu.i18nKey')">{{ showData.i18nKey }}</NDescriptionsItem>
            <NDescriptionsItem :label="$t('page.manage.menu.routeName')">{{ showData.routeName }}</NDescriptionsItem>
            <NDescriptionsItem :label="$t('page.manage.menu.routePath')">{{ showData.routePath }}</NDescriptionsItem>
            <NDescriptionsItem :label="$t('page.manage.menu.hideInMenu')">
              <NTag :type="dcitType('feature_status', showData.hide || 'N') || 'default'">
                {{ dictLabel('feature_status', showData.hide || 'N') }}
              </NTag>
            </NDescriptionsItem>
            <NDescriptionsItem :label="$t('page.manage.menu.keepAlive')">
              <NTag :type="dcitType('feature_status', showData.keepAlive || 'N') || 'default'">
                {{ dictLabel('feature_status', showData.keepAlive || 'N') }}
              </NTag>
            </NDescriptionsItem>
            <NDescriptionsItem :label="$t('page.manage.menu.href')" :span="2">{{ showData.href }}</NDescriptionsItem>
          </NDescriptions>
        </NCard>
        <PermissionListTable :show-data="showData" :all-pages="allPages" />
      </NGridItem>
      <NGridItem v-else span="4" class="h-full overflow-auto">
        <NCard :bordered="false" size="small" class="h-full">
          <NEmpty :description="$t('page.manage.menu.selectTreeIsEmptyTip')" class="h-full justify-center" />
        </NCard>
      </NGridItem>
    </NGrid>
    <MenuOperateDrawer
      v-model:visible="menuDrawerVisible"
      :row-data="showData"
      :operate-type="operateType"
      :all-pages="allPages"
      @submitted="data => init(data)"
    />
  </div>
  <NoPermission v-else :description="$t('page.manage.menu.noPermissionDesc')" />
</template>

<style scoped></style>

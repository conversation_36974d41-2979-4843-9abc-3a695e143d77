<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { fetchAddOrgUnits, fetchUpdateOrgUnits } from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';
import { $t } from '@/locales';

defineOptions({
  name: 'OrgUnitsOperateDrawer'
});

export type OperateType = NaiveUI.TableOperateType | 'addChild';

interface Props {
  /** the type of operation */
  operateType: OperateType;
  /** the edit row data */
  rowData?: Api.SystemManage.OrgUnits | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { dictOptions } = useDict();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: $t('page.manage.orgUnits.addOrgUnits'),
    edit: $t('page.manage.orgUnits.editOrgUnits'),
    addChild: $t('page.manage.orgUnits.addChildOrgUnits')
  };
  return titles[props.operateType];
});

type Model = Api.SystemManage.OrgUnitsEdit;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    id: '0',
    parentId: 0,
    name: '',
    code: '',
    abbr: '',
    level: 1,
    ancestors: '0',
    description: '',
    sort: 1,
    status: '1'
  };
}

type RuleKey = Exclude<
  keyof Model,
  'id' | 'parentId' | 'abbr' | 'level' | 'ancestors' | 'description' | 'i18nKey' | 'sort'
>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  code: defaultRequiredRule,
  name: defaultRequiredRule,
  status: defaultRequiredRule
};

function handleInitModel() {
  Object.assign(model, createDefaultModel());

  if (!props.rowData) return;

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }

  if (props.operateType === 'addChild') {
    const { id } = props.rowData;
    const { level, ancestors } = {
      level: (props.rowData.level || 0) + 1,
      ancestors: props.rowData.ancestors ? `${props.rowData.ancestors},${props.rowData.id}` : `0,${props.rowData.id}`
    };
    Object.assign(model, { parentId: id, level, ancestors });
  }
}

function closeDrawer() {
  visible.value = false;
}

const isAdd = computed(() => props.operateType === 'add' || props.operateType === 'addChild');

/** 从响应中提取数据 */
function extractResponseData(response: any) {
  if (response.data) {
    return response.data;
  } else if (response.response && response.response.data) {
    return response.response.data;
  }
  return null;
}

/** 判断请求是否成功 */
function isResponseSuccess(response: any, responseData: any) {
  return (
    (!response.error && response.response?.status === 200) || // 响应状态码为200
    (!response.error && responseData?.code === 200) || // 响应中的code为200
    responseData?.code === 200 // 直接检查code是200
  );
}

async function handleSubmit() {
  try {
    await validate();

    const authStore = useAuthStore();
    const userId = authStore.userInfo.userId;
    const userName = authStore.userInfo.userName;

    // 创建提交数据对象
    const submitData = { ...model };

    // 根据操作类型设置不同的字段
    if (isAdd.value) {
      // 新增时需要创建和修改用户信息
      Object.assign(submitData, {
        createUser: userName,
        createUserId: userId,
        updateUser: userName,
        updateUserId: userId
      });

      // 新增时不应该传递ID字段，删除ID字段
      if (props.operateType === 'add') {
        delete submitData.id;
      }
    } else {
      // 编辑时只需要修改用户信息
      Object.assign(submitData, {
        updateUser: userName,
        updateUserId: userId
      });
    }

    const func = isAdd.value ? fetchAddOrgUnits : fetchUpdateOrgUnits;
    const response = await func(submitData as any);

    // 提取真正的响应数据
    const responseData = extractResponseData(response);

    // 判断是否成功
    const isSuccess = isResponseSuccess(response, responseData);

    if (isSuccess) {
      // 使用消息组件显示成功提示
      const successMsg = responseData?.msg || (isAdd.value ? $t('common.addSuccess') : $t('common.updateSuccess'));

      if (window.$message) {
        window.$message.success(successMsg);
      }

      // 关闭抽屉并发送刷新事件
      closeDrawer();
      emit('submitted');
    } else {
      const errorMsg = responseData?.msg || response.error || $t('common.error');
      if (window.$message) {
        window.$message.error(errorMsg);
      }
    }
  } catch (err) {
    if (window.$message) {
      window.$message.error(`${$t('common.error')}: ${err}`);
    }
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="360">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem :label="$t('page.manage.orgUnits.name')" path="name">
          <NInput v-model:value="model.name" :placeholder="$t('page.manage.orgUnits.form.name')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.orgUnits.code')" path="code">
          <NInput v-model:value="model.code" :placeholder="$t('page.manage.orgUnits.form.code')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.orgUnits.abbr')" path="abbr">
          <NInput v-model:value="model.abbr" :placeholder="$t('page.manage.orgUnits.form.abbr')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.orgUnits.status')" path="status">
          <NRadioGroup v-model:value="model.status">
            <NRadio v-for="item in dictOptions('status')" :key="item.value" :value="item.value" :label="item.label" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem :label="$t('page.manage.orgUnits.sort')" path="sort">
          <NInputNumber v-model:value="model.sort" :placeholder="$t('page.manage.orgUnits.form.sort')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.orgUnits.description')" path="description">
          <NInput v-model:value="model.description" :placeholder="$t('page.manage.orgUnits.form.description')" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace>
          <NButton quaternary @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>

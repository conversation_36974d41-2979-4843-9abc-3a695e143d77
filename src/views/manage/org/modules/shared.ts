/**
 * 获取组织单位级别和祖先列表
 *
 * @param parentId 父级ID
 * @param orgUnits 所有组织单位
 * @returns 组织单位级别和祖先列表
 */
export function getLevelAndAncestors(
  parentId: number,
  orgUnits: Api.SystemManage.OrgUnits[]
): { level: number; ancestors: string } {
  if (!parentId) {
    return { level: 1, ancestors: '0' };
  }

  // 查找父级组织
  const parent = orgUnits.find(unit => unit.id === parentId);

  if (!parent) {
    return { level: 1, ancestors: '0' };
  }

  // 当前级别 = 父级别 + 1
  const level = parent.level + 1;
  // 当前祖先 = 父级祖先 + 父级ID
  const ancestors = `${parent.ancestors},${parent.id}`;

  return { level, ancestors };
}

/**
 * 从组织单位树中收集所有ID
 *
 * @param item 组织单位树节点
 * @returns ID列表
 */
export function collectIdsFromItem(item: Api.SystemManage.OrgUnitsTree): string {
  if (!item) return '';

  let ids = [String(item.id)];

  if (item.children && item.children.length > 0) {
    item.children.forEach(child => {
      const childIds = collectIdsFromItem(child);
      if (childIds) {
        ids = [...ids, ...childIds.split(',')];
      }
    });
  }

  return ids.join(',');
}

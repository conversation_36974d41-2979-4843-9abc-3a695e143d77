<script setup lang="tsx">
import type { Ref } from 'vue';
import { ref } from 'vue';
import { <PERSON><PERSON><PERSON><PERSON>, NCard, NPopconfirm } from 'naive-ui';
import { fetchDeleteOrgUnits, fetchDeleteOrgUnitsBatch, fetchGetOrgUnitsPageList } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useAuth } from '@/hooks/business/auth';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { useDict } from '@/hooks/business/dict';
import { $t } from '@/locales';
import NoPermission from '@/components/common/no-permission.vue';
import OrgUnitsOperateDrawer, { type OperateType } from './modules/org-units-operate-drawer.vue';
import OrgUnitsSearch from './modules/org-units-search.vue';

defineOptions({
  name: 'OrgUnitsPage'
});

const appStore = useAppStore();

const { hasAuth } = useAuth();

const { dictTag } = useDict();

const operateType = ref<OperateType>('add');

const editingData: Ref<Api.SystemManage.OrgUnits | null> = ref(null);

const {
  columns,
  columnChecks,
  data,
  loading,
  getData,
  getDataByPage,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetOrgUnitsPageList,
  apiParams: {
    current: 1,
    size: 10,
    name: null,
    status: null
  },
  showTotal: true,
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },

    {
      // @ts-ignore
      key: 'index',
      title: $t('common.index'),
      width: 64,
      align: 'center'
    },
    {
      key: 'name' as const,
      title: $t('page.manage.orgUnits.name'),
      align: 'center',
      width: 150,
      minWidth: 150
    },
    {
      key: 'code' as const,
      title: $t('page.manage.orgUnits.code'),
      align: 'center',
      width: 100,
      minWidth: 100
    },
    {
      key: 'abbr' as const,
      title: $t('page.manage.orgUnits.abbr'),
      align: 'center',
      width: 100,
      minWidth: 100
    },
    {
      key: 'description' as const,
      title: $t('page.manage.orgUnits.description'),
      align: 'center',
      width: 120,
      minWidth: 120
    },
    {
      key: 'sort' as const,
      title: $t('page.manage.orgUnits.sort'),
      align: 'center',
      width: 50,
      minWidth: 50
    },
    {
      key: 'status' as const,
      title: $t('page.manage.orgUnits.status'),
      align: 'center',
      width: 60,
      minWidth: 60,
      render: row => dictTag('status', row.status)
    },
    // 只有当用户有任意操作权限时才显示操作列
    ...(hasAuth('sys:org:units:add') || hasAuth('sys:org:units:update') || hasAuth('sys:org:units:delete')
      ? [
          {
            key: 'operate' as const,
            title: $t('common.operate'),
            align: 'center',
            width: 240,
            fixed: 'right',
            render: row => (
              <div class="flex-center gap-8px">
                {hasAuth('sys:org:units:add') && (
                  <NButton type="primary" quaternary size="small" onClick={() => handleAddChildOrgUnits(row)}>
                    {$t('page.manage.orgUnits.addChildOrgUnits')}
                  </NButton>
                )}
                {hasAuth('sys:org:units:update') && (
                  <NButton type="primary" quaternary size="small" onClick={() => edit(row)}>
                    {$t('common.edit')}
                  </NButton>
                )}
                {hasAuth('sys:org:units:delete') && (
                  <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
                    {{
                      default: () => $t('common.confirmDelete'),
                      trigger: () => (
                        <NButton type="error" quaternary size="small">
                          {$t('common.delete')}
                        </NButton>
                      )
                    }}
                  </NPopconfirm>
                )}
              </div>
            )
          }
        ]
      : [])
  ]
});

const { drawerVisible, openDrawer, checkedRowKeys, onDeleted, onBatchDeleted } = useTableOperate(data, getData);

function handleAdd() {
  operateType.value = 'add';
  openDrawer();
}

function edit(item: Api.SystemManage.OrgUnits) {
  operateType.value = 'edit';
  editingData.value = { ...item };
  openDrawer();
}

async function handleDelete(id: string) {
  try {
    const result = await fetchDeleteOrgUnits(Number(id));

    // 从response中获取响应数据
    const responseData = result.response?.data;

    if (!result.error && responseData && responseData.code === 200) {
      // 不显示成功消息，让onDeleted函数来处理
      await onDeleted();
    }
    // 错误消息已由全局错误处理器显示，此处不需要重复显示
  } catch {
    // 避免重复显示错误消息，这里不再调用window.$message
  }
}

async function handleBatchDelete() {
  try {
    const ids = checkedRowKeys.value.map(id => Number(id));
    if (ids.length === 0) {
      window.$message?.warning('请选择要删除的项');
      return;
    }

    const result = await fetchDeleteOrgUnitsBatch(ids);

    // 从response中获取响应数据
    const responseData = result.response?.data;

    if (!result.error && responseData && responseData.code === 200) {
      // 不显示成功消息，让onBatchDeleted函数来处理
      await onBatchDeleted();
    }
    // 错误消息已由全局错误处理器显示，此处不需要重复显示
  } catch {
    // 避免重复显示错误消息，这里不再调用window.$message
  }
}

async function handleAddChildOrgUnits(item: Api.SystemManage.OrgUnits) {
  operateType.value = 'addChild';
  editingData.value = { ...item };
  openDrawer();
}

function handleSubmitted() {
  getDataByPage();
}
</script>

<template>
  <div
    v-if="hasAuth('sys:org:units:view')"
    class="min-h-500px flex-col-stretch gap-8px overflow-hidden lt-sm:overflow-auto"
  >
    <OrgUnitsSearch v-model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper" content-class="flex-col">
      <TableHeaderOperation
        v-model:columns="columnChecks"
        :checked-row-keys="checkedRowKeys"
        :loading="loading"
        add-auth="sys:org:units:add"
        delete-auth="sys:org:units:delete"
        @add="handleAdd"
        @delete="handleBatchDelete"
        @refresh="getData"
      />
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        remote
        striped
        size="small"
        class="sm:h-full"
        :data="data"
        :scroll-x="962"
        :columns="columns as any"
        :flex-height="!appStore.isMobile"
        :loading="loading"
        :single-line="false"
        :row-key="row => row.id"
        :pagination="mobilePagination"
      />
      <OrgUnitsOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="handleSubmitted"
      />
    </NCard>
  </div>
  <NoPermission v-else :description="$t('page.manage.orgUnits.noPermissionDesc')" />
</template>

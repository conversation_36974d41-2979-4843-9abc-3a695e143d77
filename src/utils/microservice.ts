import { getMicroservicesConfig } from '../config/microservices';

/** 微服务配置接口 */
export interface MicroserviceConfig {
  name: string;
  prefix: string;
  description?: string;
}

/** 微服务配置映射 */
export type MicroserviceConfigMap = Record<string, MicroserviceConfig>;

/**
 * 创建微服务配置
 *
 * @param env 环境变量
 */
export function createMicroserviceConfig(env: Env.ImportMeta): {
  gatewayBaseURL: string;
  services: MicroserviceConfigMap;
} {
  const { VITE_GATEWAY_BASE_URL } = env;

  let services: MicroserviceConfigMap = {};

  // 使用简化的配置获取函数
  services = getMicroservicesConfig();

  return {
    gatewayBaseURL: VITE_GATEWAY_BASE_URL,
    services
  };
}

/** 微服务URL配置选项 */
export interface MicroserviceURLOptions {
  serviceName: string;
  path: string;
  env: Env.ImportMeta;
  isProxy?: boolean;
}

/**
 * 获取微服务请求URL
 *
 * @param options 配置选项
 */
export function getMicroserviceURL(options: MicroserviceURLOptions): string {
  const { serviceName, path, env, isProxy = false } = options;
  const { gatewayBaseURL, services } = createMicroserviceConfig(env);

  const service = services[serviceName];
  if (!service) {
    throw new Error(`Microservice "${serviceName}" not found in configuration`);
  }

  // 确保路径以 / 开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;

  if (isProxy) {
    // 开发环境使用代理
    return `/proxy-${serviceName}${normalizedPath}`;
  }
  // 生产环境直接使用网关地址
  return `${gatewayBaseURL}${service.prefix}${normalizedPath}`;
}

/**
 * 创建微服务代理配置
 *
 * @param env 环境变量
 */
export function createMicroserviceProxyConfig(env: Env.ImportMeta) {
  const { gatewayBaseURL, services } = createMicroserviceConfig(env);
  const proxyConfig: Record<string, any> = {};
  Object.entries(services).forEach(([serviceName, service]) => {
    const proxyPattern = `/proxy-${serviceName}`;
    proxyConfig[proxyPattern] = {
      target: gatewayBaseURL,
      changeOrigin: true,
      rewrite: (path: string) => path.replace(new RegExp(`^${proxyPattern}`), service.prefix)
    };
  });
  return proxyConfig;
}

/** 微服务请求工具类 */
export class MicroserviceRequest {
  private env: Env.ImportMeta;
  private isProxy: boolean;

  constructor(env: Env.ImportMeta, isProxy: boolean = false) {
    this.env = env;
    this.isProxy = isProxy;
  }

  /**
   * 构建微服务请求URL
   *
   * @param serviceName 服务名称
   * @param path API路径
   */
  buildURL(serviceName: string, path: string): string {
    return getMicroserviceURL({
      serviceName,
      path,
      env: this.env,
      isProxy: this.isProxy
    });
  }

  /** 获取所有可用的微服务列表 */
  getAvailableServices(): MicroserviceConfig[] {
    const { services } = createMicroserviceConfig(this.env);
    return Object.values(services);
  }

  /**
   * 检查服务是否存在
   *
   * @param serviceName 服务名称
   */
  hasService(serviceName: string): boolean {
    const { services } = createMicroserviceConfig(this.env);
    return serviceName in services;
  }
}

/**
 * 创建微服务请求实例
 *
 * @param env 环境变量
 * @param isProxy 是否使用代理
 */
export function createMicroserviceRequest(env: Env.ImportMeta, isProxy: boolean = false) {
  return new MicroserviceRequest(env, isProxy);
}

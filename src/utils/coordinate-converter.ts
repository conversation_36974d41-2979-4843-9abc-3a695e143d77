/**
 * 坐标转换工具类
 * 支持 WGS84、GCJ02、BD09 坐标系之间的转换
 * 主要用于将GPS设备的WGS84坐标转换为百度地图的BD09坐标
 */

// GPS坐标点接口
export interface GPSPoint {
  lng: number;
  lat: number;
}

// 坐标转换结果接口
export interface CoordinateResult {
  lng: number;
  lat: number;
}

export class CoordinateConverter {
  // 高精度圆周率
  private static readonly PI = 3.1415926535897932384626433832795;

  // 百度坐标系参数
  private static readonly X_PI = CoordinateConverter.PI * 3000.0 / 180.0;

  // <PERSON><PERSON><PERSON> 1940椭球参数
  private static readonly A = 6378245.0;

  // WGS84椭球的偏心率平方
  private static readonly EE = 0.00669342162296594323;

  // 经纬度换算率
  private static readonly DEFAULT_COORDINATE_MULTIPLIE = 10000000;

  /**
   * Hex String 转 String
   */
  public static hexToString(hex: string[]): string {
    const hexString = hex.join('');
    return String(parseInt(hexString, 16));
  }

  /**
   * Hex String 转 Int
   */
  public static hexToInt(hex: string[]): number {
    const hexString = hex.join('');
    return parseInt(hexString, 16);
  }

  /**
   * 将HEX 经纬度转换为实际的String经纬度
   */
  public static hexToStringLonOrLat(hexArray: string[]): string {
    const hexString = hexArray.join('');
    try {
      // 假设十六进制字符串表示一个32位整数
      const intValue = parseInt(hexString, 16);
      // 假设转换为浮点数时需要除以某个因子
      const result = intValue / 10000000.0; // 根据实际情况调整因子
      return String(result);
    } catch (error) {
      console.error(`无法将十六进制字符串 ${hexString} 转换为整数:`, error);
      throw error;
    }
  }

  /**
   * 将HEX 经纬度转换为实际的Double经纬度
   */
  public static hexToDoubleLonOrLat(hex: string[]): number {
    const hexString = hex.join('');
    const intValue = parseInt(hexString, 16);
    return Number((intValue / CoordinateConverter.DEFAULT_COORDINATE_MULTIPLIE).toFixed(7));
  }

  /**
   * 基本WGS84转GCJ02(无迭代)
   */
  private static basicWgs84ToGcj02(lng: number, lat: number): CoordinateResult {
    let dlat = CoordinateConverter.transformLat(lng - 105.0, lat - 35.0);
    let dlng = CoordinateConverter.transformLng(lng - 105.0, lat - 35.0);
    const radlat = lat / 180.0 * CoordinateConverter.PI;
    let magic = Math.sin(radlat);
    magic = 1 - CoordinateConverter.EE * magic * magic;
    const sqrtmagic = Math.sqrt(magic);
    dlat = (dlat * 180.0) / ((CoordinateConverter.A * (1 - CoordinateConverter.EE)) / (magic * sqrtmagic) * CoordinateConverter.PI);
    dlng = (dlng * 180.0) / (CoordinateConverter.A / sqrtmagic * Math.cos(radlat) * CoordinateConverter.PI);
    const mglat = lat + dlat;
    const mglng = lng + dlng;

    return {
      lng: mglng,
      lat: mglat
    };
  }

  /**
   * GCJ02转WGS84(近似逆向)
   */
  private static gcj02ToWgs84(lng: number, lat: number): CoordinateResult {
    const d = CoordinateConverter.basicWgs84ToGcj02(lng, lat);
    const wgsLng = lng * 2 - d.lng;
    const wgsLat = lat * 2 - d.lat;

    return {
      lng: wgsLng,
      lat: wgsLat
    };
  }

  /**
   * GCJ02转百度BD09
   */
  public static gcj02ToBd09(lng: number, lat: number): CoordinateResult {
    const z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * CoordinateConverter.X_PI);
    const theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * CoordinateConverter.X_PI);
    const bdLng = z * Math.cos(theta) + 0.0065;
    const bdLat = z * Math.sin(theta) + 0.006;

    return {
      lng: bdLng,
      lat: bdLat
    };
  }

  /**
   * 判断坐标是否在中国境外
   */
  private static outOfChina(lng: number, lat: number): boolean {
    return lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271;
  }

  /**
   * 纬度转换
   */
  private static transformLat(lng: number, lat: number): number {
    let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
    ret += (20.0 * Math.sin(6.0 * lng * CoordinateConverter.PI) + 20.0 * Math.sin(2.0 * lng * CoordinateConverter.PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(lat * CoordinateConverter.PI) + 40.0 * Math.sin(lat / 3.0 * CoordinateConverter.PI)) * 2.0 / 3.0;
    ret += (160.0 * Math.sin(lat / 12.0 * CoordinateConverter.PI) + 320 * Math.sin(lat * CoordinateConverter.PI / 30.0)) * 2.0 / 3.0;
    return ret;
  }

  /**
   * 经度转换
   */
  private static transformLng(lng: number, lat: number): number {
    let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
    ret += (20.0 * Math.sin(6.0 * lng * CoordinateConverter.PI) + 20.0 * Math.sin(2.0 * lng * CoordinateConverter.PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(lng * CoordinateConverter.PI) + 40.0 * Math.sin(lng / 3.0 * CoordinateConverter.PI)) * 2.0 / 3.0;
    ret += (150.0 * Math.sin(lng / 12.0 * CoordinateConverter.PI) + 300.0 * Math.sin(lng / 30.0 * CoordinateConverter.PI)) * 2.0 / 3.0;
    return ret;
  }

  /**
   * WGS84转百度BD09 - 主要转换方法
   * @param lng WGS84经度
   * @param lat WGS84纬度
   * @returns BD09坐标点
   */
  public static wgs84ToBd09(lng: number, lat: number): GPSPoint {
    // 如果坐标在中国境外，直接返回原坐标
    if (CoordinateConverter.outOfChina(lng, lat)) {
      return { lng, lat };
    }

    // WGS84 -> GCJ02 -> BD09
    const gcjResult = CoordinateConverter.basicWgs84ToGcj02(lng, lat);
    const bd09Result = CoordinateConverter.gcj02ToBd09(gcjResult.lng, gcjResult.lat);

    return {
      lng: bd09Result.lng,
      lat: bd09Result.lat
    };
  }

  /**
   * 批量转换WGS84坐标到BD09
   * @param points WGS84坐标点数组
   * @returns BD09坐标点数组
   */
  public static batchWgs84ToBd09(points: GPSPoint[]): GPSPoint[] {
    return points.map(point => CoordinateConverter.wgs84ToBd09(point.lng, point.lat));
  }

  /**
   * 计算两点之间的距离是否小于等于规定距离
   * @param lat 纬度1
   * @param lon 经度1
   * @param centerLat 纬度2
   * @param centerLon 经度2
   * @param distance 规定距离(米)
   * @returns 是否在规定距离内
   */
  public static isInRange(lat: number, lon: number, centerLat: number, centerLon: number, distance: number): boolean {
    const actualDistance = CoordinateConverter.calculateDistance(lat, lon, centerLat, centerLon);
    return actualDistance <= distance;
  }

  /**
   * 计算两点之间的距离(米)
   * 使用Haversine公式计算球面距离
   * @param lat1 纬度1
   * @param lon1 经度1
   * @param lat2 纬度2
   * @param lon2 经度2
   * @returns 距离(米)，保留2位小数
   */
  public static calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    // 转换为弧度
    const lat1Rad = CoordinateConverter.toRadians(lat1);
    const lat2Rad = CoordinateConverter.toRadians(lat2);
    const lon1Rad = CoordinateConverter.toRadians(lon1);
    const lon2Rad = CoordinateConverter.toRadians(lon2);

    // 纬度之差
    const deltaLat = lat1Rad - lat2Rad;
    // 经度之差
    const deltaLon = lon1Rad - lon2Rad;

    // Haversine公式计算两点距离
    const a = Math.pow(Math.sin(deltaLat / 2), 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) * Math.pow(Math.sin(deltaLon / 2), 2);
    const c = 2 * Math.asin(Math.sqrt(a));

    // 地球半径(米)
    const earthRadius = 6378137;
    const distance = c * earthRadius;

    // 保留2位小数
    return Math.round(distance * 100) / 100;
  }

  /**
   * 角度转弧度
   * @param degrees 角度
   * @returns 弧度
   */
  private static toRadians(degrees: number): number {
    return degrees * (CoordinateConverter.PI / 180);
  }

  /**
   * 弧度转角度
   * @param radians 弧度
   * @returns 角度
   */
  private static toDegrees(radians: number): number {
    return radians * (180 / CoordinateConverter.PI);
  }

  /**
   * BD09转GCJ02
   * @param lng BD09经度
   * @param lat BD09纬度
   * @returns GCJ02坐标
   */
  public static bd09ToGcj02(lng: number, lat: number): CoordinateResult {
    const x = lng - 0.0065;
    const y = lat - 0.006;
    const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * CoordinateConverter.X_PI);
    const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * CoordinateConverter.X_PI);
    const gcjLng = z * Math.cos(theta);
    const gcjLat = z * Math.sin(theta);

    return {
      lng: gcjLng,
      lat: gcjLat
    };
  }

  /**
   * BD09转WGS84
   * @param lng BD09经度
   * @param lat BD09纬度
   * @returns WGS84坐标
   */
  public static bd09ToWgs84(lng: number, lat: number): GPSPoint {
    const gcjResult = CoordinateConverter.bd09ToGcj02(lng, lat);
    const wgs84Result = CoordinateConverter.gcj02ToWgs84(gcjResult.lng, gcjResult.lat);

    return {
      lng: wgs84Result.lng,
      lat: wgs84Result.lat
    };
  }

  /**
   * 验证经纬度是否有效
   * @param lng 经度
   * @param lat 纬度
   * @returns 是否有效
   */
  public static isValidCoordinate(lng: number, lat: number): boolean {
    return lng >= -180 && lng <= 180 && lat >= -90 && lat <= 90;
  }

  /**
   * 格式化坐标显示
   * @param lng 经度
   * @param lat 纬度
   * @param precision 精度(小数位数)，默认6位
   * @returns 格式化后的坐标字符串
   */
  public static formatCoordinate(lng: number, lat: number, precision: number = 6): string {
    return `${lng.toFixed(precision)}, ${lat.toFixed(precision)}`;
  }
}

/**
 * 坐标转换工具使用示例
 * 展示如何在Vue组件中使用CoordinateConverter
 */

import { CoordinateConverter, type GPSPoint } from './coordinate-converter';

// 示例1: 单个GPS坐标转换
export function convertSingleGps() {
  // 假设从后端接收到的WGS84坐标
  const wgs84Lng = 104.7436647;
  const wgs84Lat = 28.8150648;

  // 转换为百度地图坐标
  const bd09Point = CoordinateConverter.wgs84ToBd09(wgs84Lng, wgs84Lat);
  
  console.log('原始WGS84坐标:', { lng: wgs84Lng, lat: wgs84Lat });
  console.log('转换后BD09坐标:', bd09Point);
  
  return bd09Point;
}

// 示例2: 批量转换车辆GPS数据
export function convertVehicleData() {
  // 模拟从后端接收的车辆数据
  const vehicleData = [
    {
      id: 'vehicle001',
      name: '巡检车辆1',
      lng: 104.7436647,  // WGS84经度
      lat: 28.8150648,   // WGS84纬度
      speed: 60,
      status: 'running'
    },
    {
      id: 'vehicle002', 
      name: '巡检车辆2',
      lng: 104.7536647,
      lat: 28.8250648,
      speed: 45,
      status: 'stopped'
    }
  ];

  // 批量转换坐标
  const convertedVehicles = CoordinateConverter.convertVehicleGpsData(vehicleData);
  
  console.log('转换后的车辆数据:', convertedVehicles);
  
  return convertedVehicles;
}

// 示例3: 在Vue组件中使用的composable函数
export function useGpsConverter() {
  /**
   * 转换单个GPS点
   */
  const convertGpsPoint = (lng: number, lat: number): GPSPoint => {
    return CoordinateConverter.convertGpsForMap({ lng, lat });
  };

  /**
   * 转换车辆数组的GPS数据
   */
  const convertVehicleGps = <T extends Record<string, any>>(
    vehicles: T[],
    lngField = 'lng',
    latField = 'lat'
  ): T[] => {
    return CoordinateConverter.convertVehicleGpsData(vehicles, lngField, latField);
  };

  /**
   * 计算两个GPS点之间的距离
   */
  const calculateDistance = (
    point1: { lng: number; lat: number },
    point2: { lng: number; lat: number }
  ): number => {
    return CoordinateConverter.calculateDistance(
      point1.lat, point1.lng,
      point2.lat, point2.lng
    );
  };

  /**
   * 检查GPS点是否在指定范围内
   */
  const isInRange = (
    point: { lng: number; lat: number },
    center: { lng: number; lat: number },
    distance: number
  ): boolean => {
    return CoordinateConverter.isInRange(
      point.lat, point.lng,
      center.lat, center.lng,
      distance
    );
  };

  /**
   * 格式化坐标显示
   */
  const formatCoordinate = (lng: number, lat: number, precision = 6): string => {
    return CoordinateConverter.formatCoordinate(lng, lat, precision);
  };

  return {
    convertGpsPoint,
    convertVehicleGps,
    calculateDistance,
    isInRange,
    formatCoordinate
  };
}

// 示例4: WebSocket接收GPS数据时的处理
export function handleWebSocketGpsData(rawData: any) {
  try {
    // 假设WebSocket数据格式
    const gpsData = {
      vehicleId: rawData.vehicleId,
      timestamp: rawData.timestamp,
      lng: rawData.longitude,  // WGS84经度
      lat: rawData.latitude,   // WGS84纬度
      speed: rawData.speed,
      direction: rawData.direction
    };

    // 验证坐标有效性
    if (!CoordinateConverter.isValidCoordinate(gpsData.lng, gpsData.lat)) {
      console.error('接收到无效的GPS坐标:', gpsData);
      return null;
    }

    // 转换坐标
    const convertedPoint = CoordinateConverter.wgs84ToBd09(gpsData.lng, gpsData.lat);
    
    // 返回处理后的数据
    return {
      ...gpsData,
      lng: convertedPoint.lng,  // 转换后的百度坐标
      lat: convertedPoint.lat,
      originalLng: gpsData.lng, // 保留原始坐标
      originalLat: gpsData.lat
    };
  } catch (error) {
    console.error('处理GPS数据时出错:', error);
    return null;
  }
}

// 示例5: 地图标记点转换
export function convertMapMarkers(markers: Array<{
  id: string;
  name: string;
  lng: number;
  lat: number;
  [key: string]: any;
}>) {
  return markers.map(marker => {
    const converted = CoordinateConverter.wgs84ToBd09(marker.lng, marker.lat);
    return {
      ...marker,
      lng: converted.lng,
      lat: converted.lat,
      // 添加格式化的坐标显示
      coordinateText: CoordinateConverter.formatCoordinate(converted.lng, converted.lat)
    };
  });
}

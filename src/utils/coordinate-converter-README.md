# GPS坐标转换工具使用说明

## 概述

这个工具类用于解决GPS设备输出的WGS84坐标系与百度地图BD09坐标系之间的转换问题。当您的车辆GPS设备提供WGS84坐标，但需要在百度地图上正确显示位置时，必须进行坐标转换。

## 坐标系说明

- **WGS84**: 世界大地坐标系，GPS设备的标准输出格式
- **GCJ02**: 国家测绘局坐标系，也称"火星坐标系"
- **BD09**: 百度坐标系，百度地图使用的坐标系

转换流程：`WGS84 → GCJ02 → BD09`

## 基本使用

### 1. 导入工具类

```typescript
import { CoordinateConverter } from '@/utils/coordinate-converter';
```

### 2. 单个坐标转换

```typescript
// GPS设备提供的WGS84坐标
const wgs84Lng = 104.7436647;
const wgs84Lat = 28.8150648;

// 转换为百度地图坐标
const bd09Point = CoordinateConverter.wgs84ToBd09(wgs84Lng, wgs84Lat);
console.log(bd09Point); // { lng: 104.75018, lat: 28.82156 }
```

### 3. 批量转换车辆数据

```typescript
const vehicleData = [
  {
    id: 'vehicle001',
    name: '巡检车辆1',
    lng: 104.7436647,  // WGS84经度
    lat: 28.8150648,   // WGS84纬度
    speed: 60
  }
  // ... 更多车辆
];

// 批量转换
const convertedVehicles = CoordinateConverter.convertVehicleGpsData(vehicleData);
```

### 4. 在Vue组件中使用

```vue
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { CoordinateConverter } from '@/utils/coordinate-converter';

const vehicleList = ref([]);

// 处理从WebSocket接收的GPS数据
const handleGpsUpdate = (gpsData) => {
  // 转换坐标
  const convertedPoint = CoordinateConverter.wgs84ToBd09(
    gpsData.longitude, 
    gpsData.latitude
  );
  
  // 更新地图标记
  updateMapMarker(gpsData.vehicleId, convertedPoint);
};
</script>
```

## API 参考

### 主要转换方法

#### `wgs84ToBd09(lng: number, lat: number): GPSPoint`
将WGS84坐标转换为BD09坐标（百度地图坐标）

**参数:**
- `lng`: WGS84经度
- `lat`: WGS84纬度

**返回:** `{ lng: number, lat: number }` BD09坐标

#### `convertGpsForMap(gpsData: {lng: number, lat: number}): GPSPoint`
便捷的转换函数，包含坐标验证

#### `convertVehicleGpsData<T>(vehicles: T[], lngField?: string, latField?: string): T[]`
批量转换车辆GPS数据

**参数:**
- `vehicles`: 车辆数据数组
- `lngField`: 经度字段名，默认'lng'
- `latField`: 纬度字段名，默认'lat'

### 距离计算方法

#### `calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number`
计算两点之间的距离（米）

#### `isInRange(lat: number, lon: number, centerLat: number, centerLon: number, distance: number): boolean`
判断点是否在指定范围内

### 工具方法

#### `isValidCoordinate(lng: number, lat: number): boolean`
验证经纬度是否有效

#### `formatCoordinate(lng: number, lat: number, precision?: number): string`
格式化坐标显示

## 实际应用场景

### 1. WebSocket实时GPS数据处理

```typescript
const handleWebSocketMessage = (message) => {
  const data = JSON.parse(message.data);
  
  // 转换GPS坐标
  const convertedPoint = CoordinateConverter.wgs84ToBd09(
    data.longitude, 
    data.latitude
  );
  
  // 更新地图上的车辆位置
  updateVehiclePosition(data.vehicleId, convertedPoint);
};
```

### 2. 地图标记点转换

```typescript
const addMapMarkers = (vehicles) => {
  vehicles.forEach(vehicle => {
    // 转换坐标
    const point = CoordinateConverter.wgs84ToBd09(vehicle.lng, vehicle.lat);
    
    // 在百度地图上添加标记
    const marker = new BMap.Marker(new BMap.Point(point.lng, point.lat));
    map.addOverlay(marker);
  });
};
```

### 3. 轨迹数据转换

```typescript
const convertTrackData = (trackPoints) => {
  return trackPoints.map(point => {
    const converted = CoordinateConverter.wgs84ToBd09(point.lng, point.lat);
    return {
      ...point,
      lng: converted.lng,
      lat: converted.lat,
      timestamp: point.timestamp
    };
  });
};
```

## 注意事项

1. **坐标系识别**: 确保输入的是WGS84坐标系数据
2. **精度保持**: 转换过程会保持原有精度
3. **性能考虑**: 大量数据转换时建议使用批量转换方法
4. **错误处理**: 工具类包含坐标有效性验证
5. **调试支持**: 转换后的数据会保留原始坐标用于调试

## 测试验证

可以使用以下坐标进行测试：

```typescript
// 测试坐标（成都市中心）
const testLng = 104.7436647; // WGS84经度
const testLat = 28.8150648;  // WGS84纬度

const converted = CoordinateConverter.wgs84ToBd09(testLng, testLat);
console.log('转换结果:', converted);

// 验证转换是否正确：在百度地图上查看该坐标是否指向正确位置
```

## 常见问题

**Q: 为什么GPS坐标在地图上显示位置不准确？**
A: GPS设备通常输出WGS84坐标，而百度地图使用BD09坐标系，必须进行转换。

**Q: 转换后的坐标精度会降低吗？**
A: 不会，转换算法保持原有精度。

**Q: 可以反向转换吗？**
A: 可以，工具类提供了`bd09ToWgs84`方法进行反向转换。

**Q: 转换算法的准确性如何？**
A: 使用标准的坐标转换算法，在中国境内准确性很高。

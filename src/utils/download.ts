/**
 * 下载二进制文件
 *
 * @param response Blob响应体
 * @param fileName 文件名，如果为空则从响应头中获取
 */
export function downloadBlob(response: Response, fileName?: string) {
  // 获取文件名
  let name = fileName;
  if (!name && response.headers) {
    // 尝试从响应头中获取文件名
    const contentDisposition = response.headers.get('content-disposition');
    if (contentDisposition) {
      const match = contentDisposition.match(/filename=(.+)/);
      if (match && match[1]) {
        name = decodeURIComponent(match[1].replace(/"/g, ''));
      }
    }
  }

  // 如果仍然没有文件名，使用时间戳作为文件名
  if (!name) {
    name = `download_${new Date().getTime()}`;
  }

  // 创建下载链接
  response.blob().then(blob => {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', name as string);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  });
}

/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  /** 通用API响应结构 */
  interface ApiResponse<T = any> {
    /** 响应数据 */
    data: T;
    /** 响应消息 */
    msg: string;
    /** 响应状态码 */
    code: number;
  }

  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    /** common search params of table */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    /**
     * enable status
     *
     * - "1": enabled
     * - "2": disabled
     */
    type EnableStatus = 1 | 2;

    /** common record */
    type CommonRecord<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createBy: string;
      /** record create time */
      createTime: string;
      /** record updater */
      updateBy: string;
      /** record update time */
      updateTime: string;
      /** record status */
      status: EnableStatus | null;
    } & T;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginToken {
      tokenValue: string;
      refreshToken: string;
    }

    interface UserInfo {
      user: {
        id: string;
        userName: string;
        realName: string;
        userPhone: string;
        userEmail: string;
        userGender: string;
        status: number;
        createBy: string;
        updateBy: string;
        createTime: string;
        updateTime: string;
      };
      roleList: string[];
      permissionList: (string | null)[];
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }

  namespace SystemManage {
    /** 用户性别 */
    type UserGender = 1 | 2;

    /** 角色信息 */
    interface Role {
      roleName: string;
      roleCode: string;
      roleDesc: string;
    }

    /** 角色编辑信息 */
    interface RoleEdit {
      id: string;
      roleName: string;
      roleCode: string;
      roleDesc: string;
      status: string;
      createBy: string;
      updateBy: string;
      createTime: string;
      updateTime: string;
    }

    /** 角色搜索参数 */
    interface RoleSearchParams {
      roleName: string | null;
      roleCode: string | null;
      status: Common.EnableStatus | null;
    }

    /** 用户信息 */
    interface User {
      id: number;
      userName: string;
      userGender: UserGender | null;
      realName: string;
      userPhone: string;
      userEmail: string;
      userRoles: number[];
      userOrgUnits?: number[];
      status: Common.EnableStatus | null;
    }

    /** 用户搜索参数 */
    interface UserSearchParams {
      userName: string | null;
      realName: string | null;
      userEmail: string | null;
      orgIds: string | null;
      current?: number;
      size?: number;
    }

    /** 用户编辑信息 */
    interface UserEdit {
      id: string;
      userName: string;
      userGender: string;
      realName: string;
      userPhone: string;
      userEmail: string;
      status: string;
      userRoles?: number[];
      userOrgUnits?: number[];
      password?: string;
    }

    /** 组织单位状态 */
    type OrgUnitsStatus = Common.EnableStatus;

    /** 组织单位信息 */
    interface OrgUnits extends Common.CommonRecord<object> {
      id: number;
      parentId: number;
      name: string;
      code: string;
      abbr?: string;
      level: number;
      ancestors: string;
      description?: string;
      sort: number;
      status: OrgUnitsStatus | null;
    }

    /** 组织单位编辑信息 */
    interface OrgUnitsEdit {
      id: string;
      parentId: number;
      name: string;
      code: string;
      abbr?: string;
      level: number;
      ancestors: string;
      description?: string;
      sort: number;
      status: string;
    }

    /** 组织单位搜索参数 */
    interface OrgUnitsSearchParams {
      name: string | null;
      status: OrgUnitsStatus | null;
    }

    /** 组织单位树结构 */
    interface OrgUnitsTree extends OrgUnits {
      children?: OrgUnitsTree[];
    }

    /** 职位状态 */
    type PositionStatus = Common.EnableStatus;

    /** 职位信息 */
    interface Position {
      id: number;
      name: string;
      code: string;
      abbr?: string;
      description?: string;
      sort: number;
      status: PositionStatus | null;
      createTime?: string;
      updateTime?: string;
      createBy?: string;
      updateBy?: string;
    }

    /** 职位编辑信息 */
    interface PositionEdit {
      id: string;
      name: string;
      code: string;
      abbr?: string;
      description?: string;
      sort: number;
      status: string;
    }

    /** 职位搜索参数 */
    interface PositionSearchParams {
      name: string | null;
      status: PositionStatus | null;
    }

    /** 用户职责信息 */
    interface UserResponsibilities {
      userId: string;
      positionId: number;
      positionName?: string;
      orgUnitIds: number[];
      orgUnitNames?: string[];
    }

    /** 用户职责编辑信息 */
    interface UserResponsibilitiesEdit {
      userId: string;
      positionId: number;
      orgUnitIds: number[];
    }

    /** 菜单页面信息 */
    interface Page {
      id: number;
      name: string;
      code: string;
      description?: string;
      status: Common.EnableStatus | null;
    }

    /** 菜单树结构 */
    interface MenuTree {
      // id: string;
      // parentId: string;
      // name: string;
      // routeName: string;
      // routePath: string;
      // component: string;
      // icon: string;
      // sort: number;
      // isHidden: boolean;
      // isCache: boolean;
      // isAffix: boolean;
      // isFull: boolean;
      // activeMenu: string;
      // status: Common.EnableStatus;
      // children?: MenuTree[];
      id: string;
      type: string;
      name: string;
      routeName: string;
      routePath: string;
      icon: string;
      iconType: string;
      status: string;
      hide: string;
      href: string;
      iframeUrl: string;
      sort: number;
      parentId: string;
    }

    /** 菜单结构 */
    interface Menu {
      id: string;
      parentId: string;
      name: string;
      i18nKey: string;
      children?: Menu[];
      component?: string;
      routePath?: string;
    }

    /** 角色菜单关联 */
    interface RoleMenu {
      roleId: string;
      menuIds: string[];
    }

    /** 按钮权限 */
    interface Permission {
      id: number;
      name: string;
      code: string;
      description?: string;
      createBy: string;
      createTime: string;
      updateBy: string;
      updateTime: string;
      status: Common.EnableStatus;
    }

    /** 按钮权限编辑 */
    interface PermissionEdit {
      id: string;
      menuId: string;
      menuName: string;
      name: string;
      resource: string;
      description: string;
      status: string;
      sort: number;
    }

    /** 菜单按钮权限 */
    interface MenuPermission {
      menuId: string;
      i18nKey: string;
      buttons: {
        id: string;
        name: string;
      }[];
    }

    /** 角色权限关联 */
    interface RolePermission {
      roleId: string;
      permissionIds: string[];
    }
  }

  /**
   * namespace Gateway
   *
   * backend api module: "gateway"
   */
  namespace Gateway {
    /** 网关日志查询参数 */
    interface LogQueryParams {
      // 基本查询参数
      requestPath?: string;
      requestMethod?: string;
      targetServer?: string;
      ip?: string;
      userId?: string;
      statusCode?: number;
      minExecuteTime?: string;
      maxExecuteTime?: string;
      startTime?: string;
      endTime?: string;
      // 分页参数
      current?: number;
      size?: number;
      // 排序参数
      sortField?: string;
      sortDirection?: string;
    }

    /** 网关日志信息 */
    interface Log {
      id: string;
      targetServer: string;
      requestPath: string;
      requestMethod: string;
      schema: string;
      requestBody?: string;
      responseData?: string;
      ip: string;
      startTime?: number;
      endTime?: number;
      requestTime: string;
      responseTime?: string;
      executeTime: number;
      status?: string;
      statusCode: number;
      userId?: string;
      userName?: string;
      // userAgent?: string;
      referer?: string;
      createTime?: string;
    }
  }

  /**
   * namespace Device
   *
   * backend api module: "device"
   */
  namespace Device {
    /** 设备查询参数 */
    interface DeviceQueryParams extends Common.CommonSearchParams {
      /** 设备ID */
      // deviceId?: string;
      /** 设备手机号 */
      mobileNo?: string;
      /** 车牌号 */
      plateNo?: string;
      /** 设备名称 */
      deviceName?: string;
      /** 设备类型 */
      // deviceType?: string;
      /** 设备厂商 */
      // manufacturer?: string;
      /** 设备状态 - 0:禁用 1:启用 2:维护中 */
      status?: number;
      /** 是否在线 - 0:离线 1:在线 */
      isOnline?: number;
    }

    /** 设备信息DTO */
    interface DeviceDTO {
      /** 主键ID - 更新时必填 */
      id?: number;
      /** 设备ID - JT808协议中的设备ID */
      deviceId: string;
      /** 设备手机号 - SIM卡号 */
      mobileNo: string;
      /** 车牌号 */
      plateNo?: string;
      /** 设备名称/别名 */
      deviceName?: string;
      /** 设备类型 */
      deviceType?: string;
      /** 设备厂商 */
      manufacturer?: string;
      /** 设备型号 */
      deviceModel?: string;
      /** 协议版本号 */
      protocolVersion?: number;
      /** 设备状态 - 0:禁用 1:启用 2:维护中 */
      status: number;
      /** 绑定司机ID */
      driverId?: number;
    }

    /** 设备信息响应VO */
    interface DeviceVO {
      /** 主键ID */
      id: number;
      /** 设备ID */
      deviceId: string;
      /** 设备手机号 */
      mobileNo: string;
      /** 车牌号 */
      plateNo?: string;
      /** 设备名称/别名 */
      deviceName?: string;
      /** 设备类型 */
      deviceType?: string;
      /** 设备厂商 */
      manufacturer?: string;
      /** 设备型号 */
      deviceModel?: string;
      /** 协议版本号 */
      protocolVersion?: number;
      /** 设备状态 - 0:禁用 1:启用 2:维护中 */
      status: number;
      /** 设备状态描述 */
      statusDesc?: string;
      /** 是否在线 - 0:离线 1:在线 */
      isOnline?: number;
      /** 在线状态描述 */
      onlineDesc?: string;
      /** 最后在线时间 */
      lastOnlineTime?: string;
      /** 注册时间 */
      registerTime?: string;
      /** 绑定司机ID */
      driverId?: number;
      /** 创建时间 */
      createTime?: string;
      /** 更新时间 */
      updateTime?: string;
      /** 创建人 */
      createBy?: string;
      /** 更新人 */
      updateBy?: string;
    }
  }

  /** 位置定位相关接口 */
  namespace Location {
    /** 设备位置信息 */
    interface DeviceLocationVO {
      /** MongoDB文档ID */
      id: string;
      /** 设备ID */
      deviceId: string;
      /** 设备手机号 */
      mobileNo: string;
      /** 车牌号 */
      plateNo: string;
      /** 报警标志位 */
      warnBit: number;
      /** 状态标志位 */
      statusBit: number;
      /** 纬度（度） */
      lat: number;
      /** 经度（度） */
      lng: number;
      /** 海拔高度（米） */
      altitude: number;
      /** 实际速度（公里每小时） */
      speedKph: number;
      /** 方向（0-359度，正北为0，顺时针） */
      direction: number;
      /** 设备时间（GPS时间） */
      deviceTime: string;
      /** 位置附加信息 */
      attributes: Record<string, any>;
      /** 地址信息（逆地理编码结果） */
      address: string;
      /** 是否有效位置（GPS定位状态） */
      isValid: boolean;
      /** 卫星数量 */
      satelliteCount: number;
      /** 数据接收时间（服务器时间） */
      receiveTime: string;
      /** 创建时间 */
      createTime: string;
    }

    /** 设备轨迹查询参数 */
    interface DeviceTrackParams {
      /** 设备ID */
      deviceId: string;
      /** 开始时间 */
      startTime: string;
      /** 结束时间 */
      endTime: string;
      /** 限制数量 */
      limit?: number;
    }

    /** 区域查询参数 */
    interface AreaQueryParams {
      /** 最小经度 */
      minLng: number;
      /** 最大经度 */
      maxLng: number;
      /** 最小纬度 */
      minLat: number;
      /** 最大纬度 */
      maxLat: number;
      /** 开始时间（可选） */
      startTime?: string;
      /** 结束时间（可选） */
      endTime?: string;
    }

    /** 位置统计查询参数 */
    interface LocationStatsParams {
      /** 设备ID（可选） */
      deviceId?: string;
      /** 开始时间（可选） */
      startTime?: string;
      /** 结束时间（可选） */
      endTime?: string;
    }

    /** 位置统计信息 */
    interface LocationStatsVO {
      /** 总记录数 */
      totalRecords: number;
      /** 设备数量 */
      deviceCount: number;
      /** 在线设备数量 */
      onlineDeviceCount: number;
      /** 最新更新时间 */
      lastUpdateTime: string;
    }
  }
}

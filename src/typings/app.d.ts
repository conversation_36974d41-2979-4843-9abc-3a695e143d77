/** The global namespace for the app */
declare namespace App {
  /** Theme namespace */
  namespace Theme {
    type ColorPaletteNumber = import('@sa/color').ColorPaletteNumber;

    /** Theme setting */
    interface ThemeSetting {
      /** Theme scheme */
      themeScheme: UnionKey.ThemeScheme;
      /** grayscale mode */
      grayscale: boolean;
      /** colour weakness mode */
      colourWeakness: boolean;
      /** Whether to recommend color */
      recommendColor: boolean;
      /** Theme color */
      themeColor: string;
      /** Other color */
      otherColor: OtherColor;
      /** Whether info color is followed by the primary color */
      isInfoFollowPrimary: boolean;
      /** Reset cache strategy */
      resetCacheStrategy: UnionKey.ResetCacheStrategy;
      /** Layout */
      layout: {
        /** Layout mode */
        mode: UnionKey.ThemeLayoutMode;
        /** Scroll mode */
        scrollMode: UnionKey.ThemeScrollMode;
        /**
         * Whether to reverse the horizontal mix
         *
         * if true, the vertical child level menus in left and horizontal first level menus in top
         */
        reverseHorizontalMix: boolean;
      };
      /** Page */
      page: {
        /** Whether to show the page transition */
        animate: boolean;
        /** Page animate mode */
        animateMode: UnionKey.ThemePageAnimateMode;
      };
      /** Header */
      header: {
        /** Header height */
        height: number;
        /** Header breadcrumb */
        breadcrumb: {
          /** Whether to show the breadcrumb */
          visible: boolean;
          /** Whether to show the breadcrumb icon */
          showIcon: boolean;
        };
        /** Multilingual */
        multilingual: {
          /** Whether to show the multilingual */
          visible: boolean;
        };
      };
      /** Tab */
      tab: {
        /** Whether to show the tab */
        visible: boolean;
        /**
         * Whether to cache the tab
         *
         * If cache, the tabs will get from the local storage when the page is refreshed
         */
        cache: boolean;
        /** Tab height */
        height: number;
        /** Tab mode */
        mode: UnionKey.ThemeTabMode;
      };
      /** Fixed header and tab */
      fixedHeaderAndTab: boolean;
      /** Sider */
      sider: {
        /** Inverted sider */
        inverted: boolean;
        /** Sider width */
        width: number;
        /** Collapsed sider width */
        collapsedWidth: number;
        /** Sider width when the layout is 'vertical-mix' or 'horizontal-mix' */
        mixWidth: number;
        /** Collapsed sider width when the layout is 'vertical-mix' or 'horizontal-mix' */
        mixCollapsedWidth: number;
        /** Child menu width when the layout is 'vertical-mix' or 'horizontal-mix' */
        mixChildMenuWidth: number;
      };
      /** Footer */
      footer: {
        /** Whether to show the footer */
        visible: boolean;
        /** Whether fixed the footer */
        fixed: boolean;
        /** Footer height */
        height: number;
        /** Whether float the footer to the right when the layout is 'horizontal-mix' */
        right: boolean;
      };
      /** Watermark */
      watermark: {
        /** Whether to show the watermark */
        visible: boolean;
        /** Watermark text */
        text: string;
      };
      /** define some theme settings tokens, will transform to css variables */
      tokens: {
        light: ThemeSettingToken;
        dark?: {
          [K in keyof ThemeSettingToken]?: Partial<ThemeSettingToken[K]>;
        };
      };
    }

    interface OtherColor {
      info: string;
      success: string;
      warning: string;
      error: string;
    }

    interface ThemeColor extends OtherColor {
      primary: string;
    }

    type ThemeColorKey = keyof ThemeColor;

    type ThemePaletteColor = {
      [key in ThemeColorKey | `${ThemeColorKey}-${ColorPaletteNumber}`]: string;
    };

    type BaseToken = Record<string, Record<string, string>>;

    interface ThemeSettingTokenColor {
      /** the progress bar color, if not set, will use the primary color */
      nprogress?: string;
      container: string;
      layout: string;
      inverted: string;
      'base-text': string;
    }

    interface ThemeSettingTokenBoxShadow {
      header: string;
      sider: string;
      tab: string;
    }

    interface ThemeSettingToken {
      colors: ThemeSettingTokenColor;
      boxShadow: ThemeSettingTokenBoxShadow;
    }

    type ThemeTokenColor = ThemePaletteColor & ThemeSettingTokenColor;

    /** Theme token CSS variables */
    type ThemeTokenCSSVars = {
      colors: ThemeTokenColor & { [key: string]: string };
      boxShadow: ThemeSettingTokenBoxShadow & { [key: string]: string };
    };
  }

  /** Global namespace */
  namespace Global {
    type VNode = import('vue').VNode;
    type RouteLocationNormalizedLoaded = import('vue-router').RouteLocationNormalizedLoaded;
    type RouteKey = import('@elegant-router/types').RouteKey;
    type RouteMap = import('@elegant-router/types').RouteMap;
    type RoutePath = import('@elegant-router/types').RoutePath;
    type LastLevelRouteKey = import('@elegant-router/types').LastLevelRouteKey;

    /** The router push options */
    type RouterPushOptions = {
      query?: Record<string, string>;
      params?: Record<string, string>;
    };

    /** The global header props */
    interface HeaderProps {
      /** Whether to show the logo */
      showLogo?: boolean;
      /** Whether to show the menu toggler */
      showMenuToggler?: boolean;
      /** Whether to show the menu */
      showMenu?: boolean;
    }

    /** The global menu */
    type Menu = {
      /**
       * The menu key
       *
       * Equal to the route key
       */
      key: string;
      /** The menu label */
      label: string;
      /** The menu i18n key */
      i18nKey?: I18n.I18nKey | null;
      /** The route key */
      routeKey: RouteKey;
      /** The route path */
      routePath: RoutePath;
      /** The menu icon */
      icon?: () => VNode;
      /** The menu children */
      children?: Menu[];
      /** The iframe url for iframe-page menu */
      iframeUrl?: string;
    };

    type Breadcrumb = Omit<Menu, 'children'> & {
      options?: Breadcrumb[];
    };

    /** Tab route */
    type TabRoute = Pick<RouteLocationNormalizedLoaded, 'name' | 'path' | 'meta'> &
      Partial<Pick<RouteLocationNormalizedLoaded, 'fullPath' | 'query' | 'matched'>>;

    /** The global tab */
    type Tab = {
      /** The tab id */
      id: string;
      /** The tab label */
      label: string;
      /**
       * The new tab label
       *
       * If set, the tab label will be replaced by this value
       */
      newLabel?: string;
      /**
       * The old tab label
       *
       * when reset the tab label, the tab label will be replaced by this value
       */
      oldLabel?: string;
      /** The tab route key */
      routeKey: LastLevelRouteKey;
      /** The tab route path */
      routePath: RouteMap[LastLevelRouteKey];
      /** The tab route full path */
      fullPath: string;
      /** The tab fixed index */
      fixedIndex?: number | null;
      /**
       * Tab icon
       *
       * Iconify icon
       */
      icon?: string;
      /**
       * Tab local icon
       *
       * Local icon
       */
      localIcon?: string;
      /** I18n key */
      i18nKey?: I18n.I18nKey | null;
    };

    /** Form rule */
    type FormRule = import('naive-ui').FormItemRule;

    /** The global dropdown key */
    type DropdownKey = 'closeCurrent' | 'closeOther' | 'closeLeft' | 'closeRight' | 'closeAll';
  }

  /**
   * I18n namespace
   *
   * Locales type
   */
  namespace I18n {
    type RouteKey = import('@elegant-router/types').RouteKey;

    type LangType = 'en-US' | 'zh-CN';

    type LangOption = {
      label: string;
      key: LangType;
    };

    type I18nRouteKey = Exclude<RouteKey, 'root' | 'not-found'>;

    type FormMsg = {
      required: string;
      invalid: string;
    };

    type Schema = {
      system: {
        title: string;
        updateTitle: string;
        updateContent: string;
        updateConfirm: string;
        updateCancel: string;
      };
      common: {
        action: string;
        add: string;
        addSuccess: string;
        backToHome: string;
        batchDelete: string;
        cancel: string;
        close: string;
        check: string;
        expandColumn: string;
        columnSetting: string;
        config: string;
        confirm: string;
        delete: string;
        deleteSuccess: string;
        deleteError: string;
        confirmDelete: string;
        view: string;
        edit: string;
        warning: string;
        error: string;
        index: string;
        keywordSearch: string;
        logout: string;
        logoutConfirm: string;
        lookForward: string;
        modify: string;
        modifySuccess: string;
        modifyFailed: string;
        requestError: string;
        more: string;
        moreOperation: string;
        noData: string;
        operate: string;
        pleaseCheckValue: string;
        refresh: string;
        reset: string;
        search: string;
        switch: string;
        tip: string;
        trigger: string;
        update: string;
        updateSuccess: string;
        userCenter: string;
        upload: string;
        uploadSuccess: string;
        noPermission: string;
        yesOrNo: {
          yes: string;
          no: string;
        };
        enableStatus: {
          enable: string;
          disable: string;
        };
        userGender: {
          male: string;
          female: string;
        };
      };
      request: {
        logout: string;
        logoutMsg: string;
        logoutWithModal: string;
        logoutWithModalMsg: string;
        refreshToken: string;
        tokenExpired: string;
      };
      theme: {
        themeSchema: { title: string } & Record<UnionKey.ThemeScheme, string>;
        grayscale: string;
        colourWeakness: string;
        layoutMode: { title: string; reverseHorizontalMix: string } & Record<UnionKey.ThemeLayoutMode, string>;
        recommendColor: string;
        recommendColorDesc: string;
        themeColor: {
          title: string;
          followPrimary: string;
        } & Theme.ThemeColor;
        scrollMode: { title: string } & Record<UnionKey.ThemeScrollMode, string>;
        page: {
          animate: string;
          mode: { title: string } & Record<UnionKey.ThemePageAnimateMode, string>;
        };
        fixedHeaderAndTab: string;
        header: {
          height: string;
          breadcrumb: {
            visible: string;
            showIcon: string;
          };
          multilingual: {
            visible: string;
          };
        };
        tab: {
          visible: string;
          cache: string;
          height: string;
          mode: { title: string } & Record<UnionKey.ThemeTabMode, string>;
        };
        sider: {
          inverted: string;
          width: string;
          collapsedWidth: string;
          mixWidth: string;
          mixCollapsedWidth: string;
          mixChildMenuWidth: string;
        };
        footer: {
          visible: string;
          fixed: string;
          height: string;
          right: string;
        };
        watermark: {
          visible: string;
          text: string;
        };
        themeDrawerTitle: string;
        pageFunTitle: string;
        resetCacheStrategy: { title: string } & Record<UnionKey.ResetCacheStrategy, string>;
        configOperation: {
          copyConfig: string;
          copySuccessMsg: string;
          resetConfig: string;
          resetSuccessMsg: string;
        };
      };
      route: Record<I18nRouteKey, string>;
      ai: {
        chat: {
          title: string;
          connected: string;
          disconnected: string;
          messages: string;
          newChat: string;
          config: string;
          clear: string;
          configureFirst: string;
          configureDescription: string;
          configureNow: string;
          startChat: string;
          inputPlaceholder: string;
          send: string;
          copy: string;
          justNow: string;
          minutesAgo: string;
          hoursAgo: string;
          startChatting: string;
          newChatCreated: string;
          configSaved: string;
          historyCleared: string;
          chatStarted: string;
          copiedToClipboard: string;
          copyFailed: string;
          connectionSuccess: string;
          connectionFailed: string;
          welcomeMessage: string;
          minimize: string;
          maximize: string;
          close: string;
        };
        welcome: {
          title: string;
          description: string;
          quickStart: string;
          features: string;
          startChat: string;
          learnFeatures: string;
          analyzeProblems: string;
          getAdvice: string;
          examplePrompts: {
            learnFeatures: string;
            analyzeProblems: string;
            getAdvice: string;
          };
          featureList: {
            smartQA: string;
            docAnalysis: string;
            efficiency: string;
            streaming: string;
          };
        };
        config: {
          title: string;
          apiKey: string;
          apiKeyPlaceholder: string;
          apiUrl: string;
          model: string;
          modelPlaceholder: string;
          maxTokens: string;
          temperature: string;
          testConnection: string;
          saveConfig: string;
          validation: {
            apiKeyRequired: string;
            apiUrlRequired: string;
            modelRequired: string;
            maxTokensRange: string;
            temperatureRange: string;
          };
        };
      };
      page: {
        login: {
          common: {
            loginOrRegister: string;
            userNamePlaceholder: string;
            phonePlaceholder: string;
            codePlaceholder: string;
            passwordPlaceholder: string;
            confirmPasswordPlaceholder: string;
            codeLogin: string;
            confirm: string;
            back: string;
            validateSuccess: string;
            loginSuccess: string;
            welcomeBack: string;
          };
          pwdLogin: {
            title: string;
            rememberMe: string;
            forgetPassword: string;
            register: string;
            otherAccountLogin: string;
            otherLoginMode: string;
            superAdmin: string;
            admin: string;
            user: string;
          };
          codeLogin: {
            title: string;
            getCode: string;
            reGetCode: string;
            sendCodeSuccess: string;
            imageCodePlaceholder: string;
          };
          register: {
            title: string;
            agreement: string;
            protocol: string;
            policy: string;
          };
          resetPwd: {
            title: string;
          };
          bindWeChat: {
            title: string;
          };
        };
        home: {
          branchDesc: string;
          greeting: string;
          weatherDesc: string;
          projectCount: string;
          todo: string;
          message: string;
          downloadCount: string;
          registerCount: string;
          schedule: string;
          study: string;
          work: string;
          rest: string;
          entertainment: string;
          visitCount: string;
          turnover: string;
          dealCount: string;
          projectNews: {
            title: string;
            moreNews: string;
            desc1: string;
            desc2: string;
            desc3: string;
            desc4: string;
            desc5: string;
          };
          creativity: string;
        };
        manage: {
          user: {
            title: string;
            selectTreeIsEmptyTip: string;
            userName: string;
            realName: string;
            gender: string;
            phone: string;
            email: string;
            status: string;
            password: string;
            userRole: string;
            userOrgUnits: string;
            resetPwd: string;
            resetPwdSuccess: string;
            resetPwdFail: string;
            resetPassword: string;
            newPassword: string;
            unassigned: string;
            noPermissionDesc: string;
            addUser: string;
            editUser: string;
            deleteUser: string;
            batchDeleteUser: string;
            pleaseSelectUser: string;
            form: {
              userName: string;
              password: string;
              realName: string;
              phone: string;
              email: string;
              userRole: string;
              userOrgUnits: string;
            };
            validate: {
              passwordRequired: string;
              passwordLength: string;
              phoneInvalid: string;
              emailInvalid: string;
            };
            roleEnum: {
              admin: string;
              user: string;
            };
            positionEnum: {
              manager: string;
              staff: string;
            };
            orgEnum: {
              dept1: string;
              dept2: string;
            };
          };
          orgUnits: {
            title: string;
            name: string;
            code: string;
            abbr: string;
            level: string;
            description: string;
            status: string;
            sort: string;
            unassigned: string;
            orgStructure: string;
            addOrgUnits: string;
            editOrgUnits: string;
            addChildOrgUnits: string;
            noPermissionDesc: string;
            form: {
              name: string;
              code: string;
              abbr: string;
              description: string;
              status: string;
              sort: string;
            };
          };
          role: {
            title: string;
            roleName: string;
            roleCode: string;
            roleDesc: string;
            roleStatus: string;
            status: string;
            sort: string;
            addRole: string;
            editRole: string;
            menuAuth: string;
            buttonAuth: string;
            selectAll: string;
            unselectAll: string;
            selectGroup: string;
            unselectGroup: string;
            selected: string;
            items: string;
            permissions: string;
            noPermissionDesc: string;
            form: {
              roleName: string;
              roleCode: string;
              roleDesc: string;
              roleStatus: string;
              status: string;
            };
          };
          menu: {
            title: string;
            id: string;
            menuType: string;
            icon: string;
            iconTypeTitle: string;
            routeName: string;
            routePath: string;
            menuStatus: string;
            status: string;
            hideInMenu: string;
            parentId: string;
            order: string;
            sort: string;
            addChildMenu: string;
            addMenu: string;
            editMenu: string;
            keepAlive: string;
            constant: string;
            href: string;
            iframeUrl: string;
            activeMenu: string;
            multiTab: string;
            fixedIndexInTab: string;
            query: string;
            pathParam: string;
            i18nKey: string;
            button: string;
            layout: string;
            page: string;
            home: string;
            detail: string;
            type: string;
            name: string;
            selectTreeIsEmptyTip: string;
            menuTypeIsDirectory: string;
            menuTypeEnum: {
              topDirectory: string;
              subMenu: string;
              singleMenu: string;
              specialPage: string;
              directory: string;
              menu: string;
            };
            iconTypeEnum: {
              iconify: string;
              local: string;
            };
            form: {
              name: string;
              routeName: string;
              routePath: string;
              pathParam: string;
              i18nKey: string;
              icon: string;
              localIcon: string;
              order: string;
              sort: string;
              layout: string;
              page: string;
              href: string;
              iframeUrl: string;
              activeMenu: string;
              fixedIndexInTab: string;
              queryKey: string;
              queryValue: string;
              buttonCode: string;
              buttonDesc: string;
            };
            componentTips: {
              topDirectory: string;
              subMenu: string;
              singleMenu: string;
              specialPage: string;
              topDirectoryNoPage: string;
              subMenuNeedPage: string;
              singleMenuNeedPage: string;
              specialPageNeedPage: string;
              directory: string;
              topLevelMenu: string;
              directoryNoPage: string;
              menuNeedPage: string;
            };
            noPermissionDesc: string;
            permission: {
              title: string;
              name: string;
              resource: string;
              description: string;
              status: string;
              sort: string;
              addButton: string;
              editButton: string;
              noPermissionDesc: string;
              form: {
                name: string;
                resource: string;
                description: string;
                status: string;
                sort: string;
                resourceIntroduction: string;
              };
            };
          };
          permission: {
            title: string;
            name: string;
            resource: string;
            description: string;
            status: string;
            sort: string;
            addButton: string;
            editButton: string;
            form: {
              name: string;
              resource: string;
              description: string;
              status: string;
              sort: string;
              resourceIntroduction: string;
            };
          };
          gateway: {
            title: string;
            searchTitle: string;
            tableTitle: string;
            noPermissionDesc: string;
            detail: {
              title: string;
              basicInfo: string;
              requestInfo: string;
              responseInfo: string;
              requestId: string;
              targetServer: string;
              requestTime: string;
              responseTime: string;
              executeTime: string;
              protocol: string;
              clientIp: string;
              userId: string;
              userName: string;
              referer: string;
              requestMethod: string;
              requestPath: string;
              requestBody: string;
              responseStatus: string;
              responseStatusDesc: string;
              responseData: string;
            };
            table: {
              userId: string;
              userName: string;
              protocol: string;
              requestMethod: string;
              referer: string;
              requestPath: string;
              responseStatus: string;
              clientIp: string;
              targetServer: string;
              executeTime: string;
              requestTime: string;
              viewDetail: string;
            };
            search: {
              requestPath: string;
              requestMethod: string;
              responseStatus: string;
              clientIp: string;
              targetServer: string;
              userId: string;
              timeRange: string;
              minExecuteTime: string;
              maxExecuteTime: string;
              reset: string;
              search: string;
              placeholder: {
                requestPath: string;
                requestMethod: string;
                responseStatus: string;
                clientIp: string;
                targetServer: string;
                userId: string;
                startTime: string;
                endTime: string;
                minExecuteTime: string;
                maxExecuteTime: string;
              };
              statusOptions: {
                success: string;
                badRequest: string;
                unauthorized: string;
                forbidden: string;
                notFound: string;
                serverError: string;
                badGateway: string;
                serviceUnavailable: string;
              };
            };
          };
          device: {
            title: string;
            deviceId: string;
            mobileNo: string;
            plateNo: string;
            deviceName: string;
            deviceType: string;
            manufacturer: string;
            deviceModel: string;
            protocolVersion: string;
            status: string;
            statusDesc: string;
            isOnline: string;
            onlineDesc: string;
            lastOnlineTime: string;
            registerTime: string;
            driverId: string;
            createTime: string;
            updateTime: string;
            createBy: string;
            updateBy: string;
            addDevice: string;
            editDevice: string;
            deviceDetail: string;
            deleteDevice: string;
            batchDeleteDevice: string;
            enableDevice: string;
            disableDevice: string;
            maintenanceDevice: string;
            deviceIdPlaceholder: string;
            mobileNoPlaceholder: string;
            plateNoPlaceholder: string;
            deviceNamePlaceholder: string;
            deviceTypePlaceholder: string;
            manufacturerPlaceholder: string;
            deviceModelPlaceholder: string;
            protocolVersionPlaceholder: string;
            statusPlaceholder: string;
            driverIdPlaceholder: string;
            deviceIdRequired: string;
            mobileNoRequired: string;
            statusRequired: string;
            deviceIdExists: string;
            mobileNoExists: string;
            addDeviceSuccess: string;
            editDeviceSuccess: string;
            deleteDeviceSuccess: string;
            batchDeleteDeviceSuccess: string;
            statusUpdateSuccess: string;
            confirmDelete: string;
            confirmBatchDelete: string;
            confirmStatusChange: string;
            statusEnabled: string;
            statusDisabled: string;
            statusMaintenance: string;
            onlineStatus: string;
            offlineStatus: string;
            noPermissionDesc: string;
          };
          notice: {
            categoryEnum: {
              notice: string;
              announcement: string;
            };
          };
          vehicleLocation: {
            title: string;
            mapTitle: string;
            deviceList: string;
            onlineDevices: string;
            totalDevices: string;
            lastUpdate: string;
            refresh: string;
            search: string;
            deviceStatus: {
              online: string;
              offline: string;
              lost: string;
            };
            speedStatus: {
              stop: string;
              slow: string;
              normal: string;
              fast: string;
            };
            track: string;
            trackPlayer: string;
            selectTimeRange: string;
            queryTrack: string;
            trackStats: {
              totalPoints: string;
              totalDistance: string;
              duration: string;
              maxSpeed: string;
            };
            playControl: {
              play: string;
              pause: string;
              reset: string;
              speed: string;
            };
            mapTools: {
              fitView: string;
              clearTrack: string;
            };
            deviceInfo: {
              longitude: string;
              latitude: string;
              direction: string;
              satellite: string;
              position: string;
              speed: string;
              updateTime: string;
            };
            noData: string;
            noTrackData: string;
            noPermissionDesc: string;
          };
        };
      };
      form: {
        required: string;
        userName: FormMsg;
        phone: FormMsg;
        pwd: FormMsg;
        confirmPwd: FormMsg;
        code: FormMsg;
        email: FormMsg;
      };
      dropdown: Record<Global.DropdownKey, string>;
      icon: {
        themeConfig: string;
        themeSchema: string;
        lang: string;
        fullscreen: string;
        fullscreenExit: string;
        reload: string;
        collapse: string;
        expand: string;
        pin: string;
        unpin: string;
      };
      datatable: {
        itemCount: string;
      };
    };

    type GetI18nKey<T extends Record<string, unknown>, K extends keyof T = keyof T> = K extends string
      ? T[K] extends Record<string, unknown>
        ? `${K}.${GetI18nKey<T[K]>}`
        : K
      : never;

    type I18nKey = GetI18nKey<Schema>;

    type TranslateOptions<Locales extends string> = import('vue-i18n').TranslateOptions<Locales>;

    interface $T {
      (key: I18nKey): string;
      (key: I18nKey, plural: number, options?: TranslateOptions<LangType>): string;
      (key: I18nKey, defaultMsg: string, options?: TranslateOptions<I18nKey>): string;
      (key: I18nKey, list: unknown[], options?: TranslateOptions<I18nKey>): string;
      (key: I18nKey, list: unknown[], plural: number): string;
      (key: I18nKey, list: unknown[], defaultMsg: string): string;
      (key: I18nKey, named: Record<string, unknown>, options?: TranslateOptions<LangType>): string;
      (key: I18nKey, named: Record<string, unknown>, plural: number): string;
      (key: I18nKey, named: Record<string, unknown>, defaultMsg: string): string;
    }
  }

  /** Service namespace */
  namespace Service {
    /** Other baseURL key */
    type OtherBaseURLKey = 'demo';

    interface ServiceConfigItem {
      /** The backend service base url */
      baseURL: string;
      /** The proxy pattern of the backend service base url */
      proxyPattern: string;
    }

    interface OtherServiceConfigItem extends ServiceConfigItem {
      key: OtherBaseURLKey;
    }

    /** The backend service config */
    interface ServiceConfig extends ServiceConfigItem {
      /** Other backend service config */
      other: OtherServiceConfigItem[];
    }

    interface SimpleServiceConfig extends Pick<ServiceConfigItem, 'baseURL'> {
      other: Record<OtherBaseURLKey, string>;
    }

    /** The backend service response data */
    type Response<T = unknown> = {
      /** The backend service response code */
      code: number;
      /** The backend service response message */
      msg: string;
      /** The backend service response data */
      data: T;
    };

    /** The demo backend service response data */
    type DemoResponse<T = unknown> = {
      /** The backend service response code */
      status: string;
      /** The backend service response message */
      message: string;
      /** The backend service response data */
      result: T;
    };
  }
}

/** The common type namespace */
declare namespace CommonType {
  /** The strategic pattern */
  interface StrategicPattern {
    /** The condition */
    condition: boolean;
    /** If the condition is true, then call the action function */
    callback: () => void;
  }

  /**
   * The option type
   *
   * @property value: The option value
   * @property label: The option label
   */
  type Option<K = string, M = string> = { value: K; label: M };

  type YesOrNo = 'Y' | 'N';

  /** add null to all properties */
  type RecordNullable<T> = {
    [K in keyof T]?: T[K] | null;
  };

  /**
   * 按钮下拉选项
   *
   * @template K 按钮key类型
   * @template T 数据行类型
   */
  interface ButtonDropdown<K = string, T = any> {
    /** 按钮key */
    key: K;
    /** 按钮标签 */
    label: string;
    /** 图标 */
    icon?: () => any;
    /** 是否显示 */
    show?: boolean;
    /** 按钮处理函数 */
    handler?: (key: K, row: T) => void;
  }
}

import { defineConfig } from '@unocss/vite';
import transformerDirectives from '@unocss/transformer-directives';
import transformerVariantGroup from '@unocss/transformer-variant-group';
import presetWind3 from '@unocss/preset-wind3';
import type { Theme } from '@unocss/preset-uno';
import { presetSoybeanAdmin } from '@sa/uno-preset';
import { themeVars } from './src/theme/vars';

export default defineConfig<Theme>({
  // 内容配置
  content: {
    pipeline: {
      // 排除不需要处理的文件
      exclude: ['node_modules', 'dist']
    }
  },
  // 主题配置
  theme: {
    ...themeVars,
    // 字体大小配置
    fontSize: {
      'icon-xs': '0.875rem',
      'icon-small': '1rem',
      icon: '1.125rem',
      'icon-large': '1.5rem',
      'icon-xl': '2rem'
    }
  },
  // 快捷方式（样式简写）
  shortcuts: {
    'card-wrapper': 'rd-8px shadow-sm'
  },
  // 转换器配置
  transformers: [transformerDirectives(), transformerVariantGroup()],
  // 预设配置
  presets: [presetWind3({ dark: 'class' }), presetSoybeanAdmin()]
});

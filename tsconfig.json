{
  "compilerOptions": {
    // 目标ECMAScript版本
    "target": "ESNext",
    // 保留JSX语法
    "jsx": "preserve",
    // JSX导入源
    "jsxImportSource": "vue",
    // 包含的库文件
    "lib": ["DOM", "ESNext"],
    // 基础路径
    "baseUrl": ".",
    // 模块系统
    "module": "ESNext",
    // 模块解析策略
    "moduleResolution": "node",
    // 路径别名配置
    "paths": {
      "@/*": ["./src/*"],
      "~/*": ["./*"]
    },
    // 允许导入JSON模块
    "resolveJsonModule": true,
    // 类型声明文件
    "types": ["vite/client", "node", "unplugin-icons/types/vue", "naive-ui/volar"],
    // 关闭严格模式
    "strict": false,
    // 关闭严格空值检查
    "strictNullChecks": false,
    // 允许未使用的局部变量
    "noUnusedLocals": false,
    // 允许隐式的any类型
    "noImplicitAny": false,
    // 跳过类型声明文件的检查
    "skipLibCheck": true,
    // 关闭JavaScript文件检查
    "checkJs": false,
    // 允许合成默认导入
    "allowSyntheticDefaultImports": true,
    // 支持CommonJS和ES模块之间的互操作
    "esModuleInterop": true,
    // 强制文件名大小写一致性
    "forceConsistentCasingInFileNames": true,
    // 将每个文件视为单独的模块
    "isolatedModules": true,
    // 即使有错误也不阻止生成输出
    "noEmitOnError": false
  },
  // 包含的文件匹配模式
  "include": ["./**/*.ts", "./**/*.tsx", "./**/*.vue"],
  // 排除的文件/目录
  "exclude": ["node_modules", "dist"]
}

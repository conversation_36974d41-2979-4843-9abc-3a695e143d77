import process from 'node:process';
import { URL, fileURLToPath } from 'node:url';
import { defineConfig, loadEnv } from 'vite';
import { setupVitePlugins } from './build/plugins';
import { createViteProxy, getBuildTime } from './build/config';

export default defineConfig(configEnv => {
  // 加载环境变量
  const viteEnv = loadEnv(configEnv.mode, process.cwd()) as unknown as Env.ImportMeta;

  // 获取构建时间
  const buildTime = getBuildTime();

  // 判断是否启用代理（仅在开发服务器运行且非预览模式下启用）
  const enableProxy = configEnv.command === 'serve' && !configEnv.isPreview;

  return {
    // 部署基础路径
    base: viteEnv.VITE_BASE_URL,
    // 路径解析配置
    resolve: {
      alias: {
        // 设置路径别名
        '~': fileURLToPath(new URL('./', import.meta.url)),
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    // CSS相关配置
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
          // 全局引入scss变量
          additionalData: `@use "@/styles/scss/global.scss" as *;`
        }
      }
    },
    // 插件配置
    plugins: setupVitePlugins(viteEnv, buildTime),
    // 全局常量定义
    define: {
      BUILD_TIME: JSON.stringify(buildTime)
    },
    // 开发服务器配置
    server: {
      host: '0.0.0.0',
      port: 9527,
      open: true,
      // API代理配置
      proxy: createViteProxy(viteEnv, enableProxy)
    },
    // 预览服务器配置
    preview: {
      port: 9725
    },
    // 构建配置
    build: {
      // 禁用压缩大小报告
      reportCompressedSize: false,
      // 是否生成sourcemap
      sourcemap: viteEnv.VITE_SOURCE_MAP === 'Y',
      commonjsOptions: {
        // 处理commonjs模块的try/catch
        ignoreTryCatch: false
      }
    }
  };
});
